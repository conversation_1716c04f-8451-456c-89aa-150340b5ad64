<!-- 简报预览 -->
<template>
  <div>
    <div class="PreviewMain" id="brieFing">
      <div class="topTool">
        <div class="callback" @click="switchPreview">
          <i class="el-icon-arrow-left iconCallback"></i>返回
        </div>
        <el-button
          size="mini"
          type="primary"
          @click="ReportStatisics"
          ref="down"
          v-if="preViewData.reportStatus == 1"
          >生成报告</el-button
        >
        <el-button
          icon="el-icon-download"
          size="mini"
          type="primary"
          class="download"
          @click="downloadBrieFing('download')"
          ref="down"
          v-if="!preViewData.preview"
          >下载报告</el-button
        >
        {{ preViewData.preView }}
      </div>
      <div class="title">
        <h1>{{ infoData.title }}</h1>
        <h5>{{ content ? content.detectionTime : null }}</h5>
      </div>
      <!-- <div class="describe">
        <div class="BoxHeader">
          01 报告描述
        </div>
        <div class="cellStyle">
          <ul>
            <li v-for="(item, key) in content.description" :key="key">
              {{ item }}
            </li>
          </ul>
        </div>
                </div>  2023 9-4   沈老师  暂时注释-->
      <div class="detail-container">
        <template v-if="!preViewData.isWechat">
          <div class="describe">
            <div class="BoxHeader">01 事件走势</div>
            <span
              style="position: relative; top: 20px; left: 55px; font-size: 14px"
              v-if="content"
              >{{ content ? content.eventDesc : null }}</span
            >
            <div class="charts" id="line"></div>
          </div>
          <div class="describe">
            <div class="BoxHeader">02 热门文章</div>
            <div class="tableStyle">
              <div>
                <el-table
                  size="mini"
                  :data="tableData"
                  border
                  style="width: 92.5%"
                  :header-cell-style="{
                    textAlign: 'center',
                    backgroundColor: 'rgb(64, 158, 255)',
                    color: '#ffff',
                  }"
                  :cell-style="{}"
                >
                  <el-table-column
                    type="index"
                    label="序号"
                    width="60"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="cnTitle"
                    label="标题"
                    :show-overflow-tooltip="true"
                  >
                    <template slot-scope="scope">
                      <span @click="openNewView(scope.row)" class="tableTitle">
                        {{ scope.row.cnTitle }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="publishType"
                    label="平台类型"
                    width="130"
                    align="center"
                  >
                    <template slot-scope="scope">
                      {{
                        scope.row.sourceType == 1
                          ? "微信公众号"
                          : null || scope.row.sourceType == 2
                          ? "网站"
                          : null || scope.row.sourceType == 3
                          ? "手动录入"
                          : null
                      }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="sourceName"
                    label="媒体来源"
                    width="300"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="publishTime"
                    label="发布时间"
                    width="180"
                    align="center"
                  >
                    <template slot-scope="scope">
                      {{ formatDate(scope.row.publishTime) }}
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </template>
        <template v-if="preViewData.isWechat == 1">
          <div class="title" style="font-weight: 600; font-size: 20px">
            {{ preViewData.reportName }}报送条目
          </div>
          <!-- <div class="title" style="font-weight: 100;font-size: 32px;margin: 20px auto;width: 50%;font-family: cursive;">
          （★条目为“境外媒体关于我国的文章”，已由国内翻译、转载，并审核溯源可靠、内容准确。）</div> -->
          <div
            v-for="(item, index) in wechatList"
            :key="index"
            class="worldStyle"
          >
            <p class="cnSummary">
              <!-- {{ index + 1 + ".微信公众号" + item.sourceName }} -->
              {{ index + 1 + "." + item.sourceName }}
              {{
                item.publishTime
                  ? new Date(item.publishTime).getMonth() +
                    1 +
                    "月" +
                    new Date(item.publishTime).getDate() +
                    "日"
                  : ""
              }}
              {{ "报道:" + (item.cnSummary ? item.cnSummary : "暂无摘要") }}
            </p>
            <p class="link" @click="openNewViewWechat(item)">
              {{
                "(" + (item.originalUrl ? item.originalUrl : "暂无链接") + ")"
              }}
            </p>
          </div>
        </template>
        <div class="docx-container">
          <div ref="file"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { renderLineCharts, renderAnnular } from "@/utils/renderLine.js";
import { antiShake } from "@/utils/utils.js";
import { formatDate } from "@/utils/index.js";
import * as echarts from "echarts";
import API from "@/api/ScienceApi/briefing.js";
// 引入docx-preview插件
let docx = require("docx-preview");
export default {
  props: {
    preViewData: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      tableData: [],
      downloading: false,
      myChart: null,
      infoData: {},
      content: {
        detectionTime: "",
      },
      xAxis: {},
      timer: null,
      wechatList: [],
    };
  },
  mounted() {
    this.formatDate = formatDate;
  },
  created() {
    if (this.preViewData.flag) {
      this.downloadBrieFing();
    }

    /* 微信公众号模板 */
    if (this.preViewData.isWechat == 1) {
      this.StatisticalList();
    } else if (this.preViewData.isWechat == 2) {
      this.getWorld();
    } else {
      this.getInfoData();
    }
  },
  methods: {
    renderLineCharts() {
      let data = this.chrtsDataHandle();
      new renderLineCharts({
        dom: "line",
        data: data,
        titleShow: false,
        xAxis: this.xAxis,
      }).render();
      // this.renderAnnular()
    },
    async getWorld() {
      await API.exportWorld({ reportId: this.preViewData.briefingId }).then(
        (res) => {
          docx.renderAsync(res, this.$refs.file); // 渲染到页面
          this.$store.commit("app/set_Loding", false);
        }
      );
    },
    renderBar() {
      var chartDom = document.getElementById("bar");
      this.myChart = echarts.init(chartDom);
      var option;
      option = {
        xAxis: {
          type: "value",
          max: 250,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        yAxis: {
          type: "category",
          boundaryGap: [0, 0.01],
          data: ["微博", "微信", "今日头条", "网易新闻", "腾讯新闻"],
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        series: [
          {
            data: [120, 200, 150, 80, 70, 110, 130],
            type: "bar",
          },
        ],
      };
      option && this.myChart.setOption(option);
    },
    async downloadBrieFing(flag) {
      this.$store.commit("app/set_Loding", true);
      /* 使用微信公众号模板 */
      if (this.preViewData.isWechat) {
        await API.exportWorld({ reportId: this.preViewData.briefingId }).then(
          (res) => {
            if (this.preViewData.isWechat == 1) {
              this.downLoadXls(res);
              this.timer = setTimeout(() => {
                this.$message({ message: "下载完成", type: "success" });
                this.switchPreview();
              }, 1000);
            } else if (this.preViewData.isWechat == 2) {
              let a = document.createElement("a");
              a.href = window.URL.createObjectURL(res);
              a.download = "briefing.docx";
              a.click();
              this.timer = setTimeout(() => {
                this.$message({ message: "下载完成", type: "success" });
                this.switchPreview();
              }, 1000);
            }
          }
        );
      } else {
        /* 使用普通模板 */
        let dom = document.querySelector("#brieFing");

        if (dom) {
          this.$store.commit("app/set_Loding", true);
          this.getPdf(dom);
          setTimeout(() => {
            this.$store.commit("app/set_Loding", false);
          }, 2000);
        } else {
          this.timer = setInterval(() => {
            dom = document.querySelector("#brieFing");
            if (dom) {
              this.getPdf(dom);
              this.switchPreview();
              this.$message({ message: "下载成功", type: "success" });
            }
          }, 2000);
        }
      }
    },
    ReportStatisics() {
      this.$emit("ReportStatisics");
    },
    /* 获取详情数据 */
    async getInfoData() {
      let res;
      /* 当前为生成前预览 */
      if (this.preViewData.previewData) {
        this.content = JSON.parse(this.preViewData.previewData);
        this.infoData.title = this.content.title;
        this.tableData = this.content.hotList;
        this.description = this.content.description;
        setTimeout(() => {
          try {
            this.renderLineCharts();
          } catch (error) {}
        }, 200);
        return;
      }
      res = await API.briefingInfo(this.preViewData.briefingId);
      if (res.code == 200) {
        this.infoData = res.data;
        this.content = JSON.parse(res.data.content);
        if (this.content) {
          this.tableData = this.content.hotList;
          this.description = this.content.description;
        }
      } else {
        this.$message({ message: "数据获取失败", type: "error" });
      }
      try {
        this.renderLineCharts();
      } catch (error) {}
    },
    /* 打开外网链接 */
    openNewViewWechat(item) {
      if (!item.originalUrl) return;
      window.open(item.originalUrl);
    },
    /* 图表数据处理 */
    chrtsDataHandle() {
      let WeArr = [],
        WyArr = [],
        Xdata = [],
        end_Time,
        start_Time,
        Year,
        endYear;
      /* 当前是报告详情 */
      if (!this.preViewData.previewData && this.content.dateType) {
        switch (this.content.dateType) {
          case "hour" /* 按小时计算 */:
            WeArr.length = 24;
            WyArr.length = 24;
            WeArr.fill(0);
            WyArr.fill(0);
            /* 处理x轴数据 */
            for (let i = 0; i < 24; i++) {
              Xdata.push(`${i.toString().length == 1 ? "0" + i : i}:00`);
            }
            /* 处理y轴数据 */
            Object.keys(this.content.wyTrendCount).forEach((item) => {
              let key = item.slice(11, 13) + ":00";
              WyArr[Xdata.lastIndexOf(key)] = this.content.wyTrendCount[item];
            });
            Object.keys(this.content.wxTrendCount).forEach((item) => {
              let key = item.slice(11, 13) + ":00";
              WeArr[Xdata.lastIndexOf(key)] = this.content.wyTrendCount[item];
            });
            break;
          case "day":
            (end_Time = Number(this.content.endTime.slice(8, 10))),
              (start_Time = Number(this.content.startTime.slice(8, 10))),
              (Year = this.content.startTime.slice(0, 7));
            endYear = this.content.endTime.slice(0, 7);
            /* 跨越两个月的情况 */
            let end = Number(this.content.endTime.slice(5, 7)),
              start = Number(this.content.startTime.slice(5, 7)),
              num = 30 - Number(start_Time);
            if (end > start) {
              end_Time = end_Time + num;
            }
            WeArr.length = end_Time;
            WyArr.length = end_Time;
            /* 数据填充 */
            WeArr.fill(0);
            WyArr.fill(0);
            /* 循环数据 */
            let a,
              i = 1;
            while (Xdata.length < end_Time) {
              a = i;
              let item;
              if (start_Time <= 30) {
                i = start_Time;
              } else if (start_Time <= 31) {
                i = 1;
                a = 1;
              }
              if (start_Time > 30) {
                item = endYear + "-" + (i.toString().length == 1 ? "0" + i : i);
              } else {
                item = Year + "-" + (i.toString().length == 1 ? "0" + i : i);
              }
              Xdata.push(item);
              i = a;
              ++start_Time;
              ++i;
            }
            /* 处理y轴数据 */
            Object.keys(this.content.wyTrendCount).forEach((item) => {
              let key = item.slice(8, 11);
              WyArr[Xdata.lastIndexOf(Year + "-" + key)] =
                this.content.wyTrendCount[item];
            });
            Object.keys(this.content.wxTrendCount).forEach((item) => {
              let key = item.slice(8, 11);
              WeArr[Xdata.lastIndexOf(Year + "-" + key)] =
                this.content.wyTrendCount[item];
            });
            break;
          case "month":
            end_Time = Number(this.content.endTime.slice(5, 7));
            start_Time = Number(this.content.startTime.slice(5, 7));
            Year = this.content.startTime.slice(0, 4);
            WeArr.length = end_Time;
            WyArr.length = end_Time;
            WeArr.fill(0);
            WyArr.fill(0);
            for (let i = start_Time; i <= end_Time; i++) {
              let item = Year + "-" + (i.toString().length == 1 ? "0" + i : i);
              Xdata.push(item);
            }

            /* 处理y轴数据 */
            Object.keys(this.content.wyTrendCount).forEach((item) => {
              let key = item.slice(5, 7);
              WyArr[Xdata.lastIndexOf(Year + "-" + key)] =
                this.content.wyTrendCount[item];
            });

            Object.keys(this.content.wxTrendCount).forEach((item) => {
              let key = item.slice(5, 7);
              let index = Xdata.lastIndexOf(Year + "-" + key);
              WeArr[index] = this.content.wxTrendCount[item];
            });
            break;
          default:
            break;
        }
        this.xAxis = {
          type: "category",
          data: Xdata,
        };
      } else {
        /* 当前是草稿详情 */

        let Wx = this.content.wxTrendCount,
          WEB = this.content.wyTrendCount;

        Object.keys(Wx).forEach((item) => {
          WeArr.push(Wx[item]);
          Xdata.push(item);
        });
        Object.keys(WEB).forEach((item) => {
          WyArr.push(WEB[item]);
        });
        this.xAxis = {
          type: "category",
          data: Xdata,
        };
      }
      return [
        {
          name: "微信",
          data: WeArr,
          type: "line",
        },
        {
          name: "网站",
          data: WyArr,
          type: "line",
        },
      ];
    },
    /* 文件流解码 */
    downLoadXls(res) {
      let fileName = this.preViewData.reportName;
      if ("download" in document.createElement("a")) {
        const a = document.createElement("a"); //创建一个a标签
        a.download = fileName + ".docx"; //指定文件名称
        a.style.display = "none"; //页面隐藏
        a.href = URL.createObjectURL(res); // href用于下载地址
        document.body.appendChild(a); //插到页面上
        a.click(); //通过点击触发
        URL.revokeObjectURL(a.href); //释放URL 对象
        document.body.removeChild(a); //删掉a标签
      } else {
        //IE10 + 下载
        navigator.msSaveBlob(res, fileName);
      }
    },
    switchPreview() {
      this.$emit("switchPreview");
    },
    /* 文章统计列表 */
    async StatisticalList() {
      this.dialogTableVisible = true;
      let res = await API.statistics({
        pageSize: 99,
        pageNum: this.pageNum1,
        reportId: this.preViewData.briefingId,
      });
      if (res.code == 200) {
        this.wechatList = res.rows;
      }
    },
    /* 打开外网链接 */
    openNewView(item) {
      if (item.originalUrl) {
        window.open(item.originalUrl);
        return;
      }
      this.$message({ message: "该文章没有原文链接" });
    },
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
};
</script>

<style lang="scss" scoped>
.tableTitle:hover {
  color: #228fd3;
  border-bottom: solid 1px #228fd3;
}

.PreviewMain {
  .topTool {
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    margin-bottom: 10px;

    .callback {
      font-size: 16px;
      color: rgb(8, 166, 240);
      cursor: pointer;
    }

    .iconCallback {
      font-size: 18px;
      color: rgb(8, 166, 240);
    }
  }

  width: 90%;
  overflow: hidden;
  height: calc(100vh - 100px);
  margin: 0 auto;
  padding-top: 20px;
  border: solid 1px #efefef;
  box-shadow: 0px 2px 11px 9px #efefef;
  margin-top: 20px;
  display: flex;
  flex-direction: column;

  .title {
    text-align: center;
    font-weight: 600;
    font-size: 14px;

    h1 {
      font-size: 30px;
    }
  }

  .describe {
    width: 98%;
    min-height: 200px;
    margin: 15px auto;
    box-shadow: 4px 6px 4px 2px #efefef;

    .BoxHeader {
      height: 40px;
      line-height: 40px;
      padding-left: 15px;
      width: 100%;
      border-top: solid 1px #e0dfdf;
      border-left: solid 1px #e0dfdf;
      border-right: solid 1px #e0dfdf;
    }

    .cellStyle {
      width: 100%;
      height: 160px;
      border: solid 1px #e0dfdf;
      display: flex;
      justify-content: center;
      align-items: center;

      p {
        width: 90%;
        font-size: 14px;
        margin: 0 auto;
      }

      ul {
        font-size: 14px;
        line-height: 25px;

        li {
          list-style: none;
        }
      }
    }

    .info {
      font-size: 14px;
      margin-left: 15px;
    }

    .charts {
      width: 100%;
      height: 300px;
      border: solid 1px #e0dfdf;
    }

    .tableStyle {
      width: 100%;
      min-height: 400px;
      border: solid 1px #e0dfdf;

      div {
        margin: 20px auto;
      }
    }
  }
}

.world {
  width: 80%;
  margin: 0 auto;
  min-height: 1200px;
  background-color: #228fd3;
}

.worldStyle {
  width: 50%;
  margin: 0 auto;
  text-overflow: clip;

  .cnSummary {
    font-size: 16px;
    line-height: 1.8em;
    font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,
      Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,
      Arial, sans-serif;
    text-indent: 2em;
  }

  .link {
    margin-top: -20px;
    font-weight: 400;
    font-size: 16px;
    line-height: 1.8em;
    font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,
      Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,
      Arial, sans-serif;
  }

  .link:hover {
    color: #228fd3;
    border-bottom: solid #228fd3 1px;
  }
}

.detail-container {
  flex: 1;
  overflow-y: auto;
}
</style>
<style scoped>
.docx-container ::v-deep .docx-wrapper {
  background-color: #fff;
  padding: 20px 20px;
}

.docx-container ::v-deep .docx-wrapper > section.docx {
  width: 55vw !important;
  padding: 0rem !important;
  min-height: auto !important;
  box-shadow: none;
  margin-bottom: 0;
  line-height: 50px;
  overflow-y: scroll;
  height: 100vh;
}

.docx-container ::v-deep .docx-wrapper > section.docx::-webkit-scrollbar {
  display: none;
}
</style>

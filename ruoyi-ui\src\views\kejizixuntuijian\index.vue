<template>
  <div v-loading="globalLoading" element-loading-text="数据加载中">
    <splitpanes class="default-theme">
      <!-- 左侧面板 -->
      <pane
        class="leftLink"
        ref="leftLink"
        min-size="20"
        max-size="50"
        size="30"
      >
        <div class="left-panel">
          <!-- 左侧表格 -->
          <div class="left-table-container">
            <el-table
              :data="leftTableData"
              v-loading="leftLoading"
              @row-click="handleLeftRowClick"
              :highlight-current-row="true"
              ref="leftTable"
              height="auto"
              size="small"
              border
              :show-header="false"
              style="font-size: 14px"
            >
              <el-table-column
                type="index"
                label="序号"
                width="60"
                align="center"
              >
                <template slot-scope="scope">
                  {{ (leftCurrentPage - 1) * leftPageSize + scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column prop="title" label="标题" show-overflow-tooltip>
              </el-table-column>
            </el-table>

            <!-- 左侧分页 -->
            <div class="left-pagination">
              <pagination
                v-show="leftTotal > 0"
                :total="leftTotal"
                :page.sync="leftCurrentPage"
                :limit.sync="leftPageSize"
                @pagination="handleLeftPagination"
                :layout="'total, prev, pager, next'"
                :background="false"
              />
            </div>
          </div>
        </div>
      </pane>

      <!-- 右侧面板 -->
      <pane min-size="50" max-size="80" size="70">
        <div class="right-panel">
          <!-- 右侧表格 -->
          <div class="right-table-container">
            <el-table
              :data="rightTableData"
              v-loading="rightLoading"
              height="auto"
              size="small"
              border
              style="font-size: 14px"
            >
              <el-table-column
                prop="score"
                label="推荐分值"
                width="100"
                align="center"
              >
                <template slot-scope="scope">
                  <el-tag :type="getScoreTagType(scope.row.score)" size="small">
                    {{ scope.row.score }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="title" label="标题" show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-link
                    type="primary"
                    @click="openDetail(scope.row)"
                    :underline="false"
                  >
                    {{ scope.row.title }}
                  </el-link>
                </template>
              </el-table-column>
              <el-table-column
                prop="sourceName"
                label="来源名称"
                width="150"
                show-overflow-tooltip
              >
              </el-table-column>
              <el-table-column
                prop="publishTime"
                label="发布日期"
                width="120"
                align="center"
              >
              </el-table-column>
              <el-table-column
                prop="field"
                label="所属领域"
                width="120"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <el-tag size="mini" v-if="scope.row.field">
                    {{ scope.row.field }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>

            <!-- 右侧分页 -->
            <div class="right-pagination">
              <pagination
                v-show="rightTotal > 0"
                :total="rightTotal"
                :page.sync="rightCurrentPage"
                :limit.sync="rightPageSize"
                @pagination="handleRightPagination"
              />
            </div>
          </div>
        </div>
      </pane>
    </splitpanes>
  </div>
</template>

<script>
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";
import API from "@/api/ScienceApi/index.js";

export default {
  name: "KejizixuntuijianIndex",
  components: {
    Splitpanes,
    Pane,
  },
  data() {
    return {
      // 全局加载状态
      globalLoading: false,

      // 左侧数据
      leftTableData: [],
      leftLoading: false,
      leftTotal: 0,
      leftCurrentPage: 1,
      leftPageSize: 20,
      leftSearchTimer: null,
      selectedLeftRow: null, // 当前选中的左侧行

      // 右侧数据
      rightTableData: [],
      rightLoading: false,
      rightTotal: 0,
      rightCurrentPage: 1,
      rightPageSize: 20,
    };
  },
  created() {
    this.initData();
  },
  methods: {
    // 初始化数据
    async initData() {
      this.globalLoading = true;
      try {
        await this.getLeftTableData();
      } catch (error) {
        console.error("初始化数据失败:", error);
        this.$message.error("数据加载失败，请重试");
      } finally {
        this.globalLoading = false;
      }
    },

    // 获取左侧表格数据
    async getLeftTableData() {
      this.leftLoading = true;
      try {
        // 尝试调用真实API，如果失败则使用模拟数据
        try {
          const params = {
            pageNum: this.leftCurrentPage,
            pageSize: this.leftPageSize,
          };
          const response = await API.getCategoryList(params);
          this.leftTableData = response.rows || response.data || [];
          this.leftTotal = response.total || 0;
        } catch (apiError) {
          console.warn("API调用失败，使用模拟数据:", apiError);
          // 使用模拟数据作为后备
          const mockData = this.generateMockLeftData();
          this.leftTableData = mockData.rows;
          this.leftTotal = mockData.total;
        }

        // 如果是第一次加载且有数据，自动选中第一行
        if (
          this.leftCurrentPage === 1 &&
          this.leftTableData.length > 0 &&
          !this.selectedLeftRow
        ) {
          this.$nextTick(() => {
            this.handleLeftRowClick(this.leftTableData[0]);
          });
        }
      } catch (error) {
        console.error("获取左侧数据失败:", error);
        this.$message.error("获取分类数据失败");
      } finally {
        this.leftLoading = false;
      }
    },

    // 生成模拟左侧数据
    generateMockLeftData() {
      const categories = [
        "人工智能技术",
        "量子计算研究",
        "生物医学工程",
        "新能源技术",
        "航空航天技术",
        "材料科学",
        "信息安全技术",
        "机器人技术",
        "区块链技术",
        "物联网技术",
        "5G通信技术",
        "自动驾驶技术",
        "虚拟现实技术",
        "基因编辑技术",
        "纳米技术",
      ];

      const total = categories.length;
      const startIndex = (this.leftCurrentPage - 1) * this.leftPageSize;
      const endIndex = Math.min(startIndex + this.leftPageSize, total);

      let filteredCategories = categories;

      const rows = filteredCategories
        .slice(startIndex, endIndex)
        .map((title, index) => ({
          id: startIndex + index + 1,
          title: title,
        }));

      return {
        rows,
        total: filteredCategories.length,
      };
    },

    // 获取右侧表格数据
    async getRightTableData() {
      if (!this.selectedLeftRow) {
        this.rightTableData = [];
        this.rightTotal = 0;
        return;
      }

      this.rightLoading = true;
      try {
        // 尝试调用真实API，如果失败则使用模拟数据
        try {
          const params = {
            categoryId: this.selectedLeftRow.id,
            pageNum: this.rightCurrentPage,
            pageSize: this.rightPageSize,
          };
          const response = await API.getRecommendList(params);
          this.rightTableData = response.rows || response.data || [];
          this.rightTotal = response.total || 0;
        } catch (apiError) {
          console.warn("API调用失败，使用模拟数据:", apiError);
          // 使用模拟数据作为后备
          const mockData = this.generateMockRightData();
          this.rightTableData = mockData.rows;
          this.rightTotal = mockData.total;
        }
      } catch (error) {
        console.error("获取右侧数据失败:", error);
        this.$message.error("获取推荐数据失败");
      } finally {
        this.rightLoading = false;
      }
    },

    // 生成模拟右侧数据
    generateMockRightData() {
      const mockArticles = [
        {
          id: 1,
          title: "人工智能在医疗诊断中的最新突破",
          score: 95,
          sourceName: "科技日报",
          publishTime: "2024-01-15",
          field: "人工智能",
        },
        {
          id: 2,
          title: "量子计算机实现新的里程碑",
          score: 88,
          sourceName: "自然杂志",
          publishTime: "2024-01-14",
          field: "量子计算",
        },
        {
          id: 3,
          title: "基因编辑技术的伦理考量",
          score: 82,
          sourceName: "生物技术周刊",
          publishTime: "2024-01-13",
          field: "生物技术",
        },
        {
          id: 4,
          title: "新能源汽车电池技术革新",
          score: 76,
          sourceName: "能源观察",
          publishTime: "2024-01-12",
          field: "新能源",
        },
        {
          id: 5,
          title: "5G网络安全防护新方案",
          score: 71,
          sourceName: "通信世界",
          publishTime: "2024-01-11",
          field: "通信技术",
        },
      ];

      // 根据选中的分类生成相关数据
      const categoryRelatedArticles = mockArticles.map((article, index) => ({
        ...article,
        id: (this.rightCurrentPage - 1) * this.rightPageSize + index + 1,
        title: `${this.selectedLeftRow.title}相关：${article.title}`,
        field: this.selectedLeftRow.title,
      }));

      const total = 50; // 模拟总数
      const startIndex = (this.rightCurrentPage - 1) * this.rightPageSize;
      const rows = categoryRelatedArticles.slice(
        0,
        Math.min(this.rightPageSize, total - startIndex)
      );

      return {
        rows,
        total,
      };
    },

    // 处理左侧行点击
    handleLeftRowClick(row) {
      this.selectedLeftRow = row;
      this.$refs.leftTable.setCurrentRow(row);

      // 重置右侧分页并加载数据
      this.rightCurrentPage = 1;
      this.getRightTableData();
    },

    // 处理左侧分页
    handleLeftPagination({ page, limit }) {
      this.leftCurrentPage = page;
      this.leftPageSize = limit;
      this.getLeftTableData();
    },

    // 处理右侧分页
    handleRightPagination({ page, limit }) {
      this.rightCurrentPage = page;
      this.rightPageSize = limit;
      this.getRightTableData();
    },

    // 获取分值标签类型
    getScoreTagType(score) {
      if (score >= 90) return "success";
      if (score >= 80) return "warning";
      if (score >= 70) return "info";
      return "danger";
    },

    // 打开详情页
    openDetail(row) {
      // 这里可以根据实际需求打开详情页
      // 例如：跳转到新页面或打开弹窗
      const url = `/expressDetails?id=${row.id}`;
      window.open(url, "_blank");
    },
  },
};
</script>

<style lang="scss" scoped>
.left-panel {
  height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.left-table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: white;
  overflow: hidden;
}

.left-pagination {
  border-top: 1px solid #ebeef5;
  background-color: #fff;
  text-align: center;
}

.right-panel {
  height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.right-table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 4px;
  overflow: hidden;
}

.right-pagination {
  border-top: 1px solid #ebeef5;
  background-color: #fff;
  text-align: center;
}

// 左侧表格行选中样式
.left-table-container {
  ::v-deep .el-table__row {
    cursor: pointer;
  }

  ::v-deep .el-table__row:hover {
    background-color: #f5f7fa;
  }

  ::v-deep .current-row {
    background-color: #ecf5ff !important;
  }
}

// 分页组件样式调整
::v-deep .el-pagination {
  top: -2px;
  .el-pagination__sizes {
    margin-top: -2px;
  }
}
</style>

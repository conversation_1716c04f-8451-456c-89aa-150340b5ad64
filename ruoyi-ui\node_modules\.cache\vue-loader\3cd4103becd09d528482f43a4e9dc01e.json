{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\kejizixuntuijian\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\kejizixuntuijian\\index.vue", "mtime": 1754447229704}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBTcGxpdHBhbmVzLCBQYW5lIH0gZnJvbSAic3BsaXRwYW5lcyI7DQppbXBvcnQgInNwbGl0cGFuZXMvZGlzdC9zcGxpdHBhbmVzLmNzcyI7DQppbXBvcnQgQVBJIGZyb20gIkAvYXBpL1NjaWVuY2VBcGkvaW5kZXguanMiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJLZWppeml4dW50dWlqaWFuSW5kZXgiLA0KICBjb21wb25lbnRzOiB7DQogICAgU3BsaXRwYW5lcywNCiAgICBQYW5lLA0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDlhajlsYDliqDovb3nirbmgIENCiAgICAgIGdsb2JhbExvYWRpbmc6IGZhbHNlLA0KDQogICAgICAvLyDlt6bkvqfmlbDmja4NCiAgICAgIGxlZnRUYWJsZURhdGE6IFtdLA0KICAgICAgbGVmdExvYWRpbmc6IGZhbHNlLA0KICAgICAgbGVmdFRvdGFsOiAwLA0KICAgICAgbGVmdEN1cnJlbnRQYWdlOiAxLA0KICAgICAgbGVmdFBhZ2VTaXplOiAyMCwNCiAgICAgIGxlZnRTZWFyY2hUaW1lcjogbnVsbCwNCiAgICAgIHNlbGVjdGVkTGVmdFJvdzogbnVsbCwgLy8g5b2T5YmN6YCJ5Lit55qE5bem5L6n6KGMDQoNCiAgICAgIC8vIOWPs+S+p+aVsOaNrg0KICAgICAgcmlnaHRUYWJsZURhdGE6IFtdLA0KICAgICAgcmlnaHRMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHJpZ2h0VG90YWw6IDAsDQogICAgICByaWdodEN1cnJlbnRQYWdlOiAxLA0KICAgICAgcmlnaHRQYWdlU2l6ZTogMjAsDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmluaXREYXRhKCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDliJ3lp4vljJbmlbDmja4NCiAgICBhc3luYyBpbml0RGF0YSgpIHsNCiAgICAgIHRoaXMuZ2xvYmFsTG9hZGluZyA9IHRydWU7DQogICAgICB0cnkgew0KICAgICAgICBhd2FpdCB0aGlzLmdldExlZnRUYWJsZURhdGEoKTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuWIneWni+WMluaVsOaNruWksei0pToiLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuaVsOaNruWKoOi9veWksei0pe+8jOivt+mHjeivlSIpOw0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5nbG9iYWxMb2FkaW5nID0gZmFsc2U7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluW3puS+p+ihqOagvOaVsOaNrg0KICAgIGFzeW5jIGdldExlZnRUYWJsZURhdGEoKSB7DQogICAgICB0aGlzLmxlZnRMb2FkaW5nID0gdHJ1ZTsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOWwneivleiwg+eUqOecn+WunkFQSe+8jOWmguaenOWksei0peWImeS9v+eUqOaooeaLn+aVsOaNrg0KICAgICAgICB0cnkgew0KICAgICAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgICAgIHBhZ2VOdW06IHRoaXMubGVmdEN1cnJlbnRQYWdlLA0KICAgICAgICAgICAgcGFnZVNpemU6IHRoaXMubGVmdFBhZ2VTaXplLA0KICAgICAgICAgIH07DQogICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBBUEkuZ2V0Q2F0ZWdvcnlMaXN0KHBhcmFtcyk7DQogICAgICAgICAgdGhpcy5sZWZ0VGFibGVEYXRhID0gcmVzcG9uc2Uucm93cyB8fCByZXNwb25zZS5kYXRhIHx8IFtdOw0KICAgICAgICAgIHRoaXMubGVmdFRvdGFsID0gcmVzcG9uc2UudG90YWwgfHwgMDsNCiAgICAgICAgfSBjYXRjaCAoYXBpRXJyb3IpIHsNCiAgICAgICAgICBjb25zb2xlLndhcm4oIkFQSeiwg+eUqOWksei0pe+8jOS9v+eUqOaooeaLn+aVsOaNrjoiLCBhcGlFcnJvcik7DQogICAgICAgICAgLy8g5L2/55So5qih5ouf5pWw5o2u5L2c5Li65ZCO5aSHDQogICAgICAgICAgY29uc3QgbW9ja0RhdGEgPSB0aGlzLmdlbmVyYXRlTW9ja0xlZnREYXRhKCk7DQogICAgICAgICAgdGhpcy5sZWZ0VGFibGVEYXRhID0gbW9ja0RhdGEucm93czsNCiAgICAgICAgICB0aGlzLmxlZnRUb3RhbCA9IG1vY2tEYXRhLnRvdGFsOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5aaC5p6c5piv56ys5LiA5qyh5Yqg6L295LiU5pyJ5pWw5o2u77yM6Ieq5Yqo6YCJ5Lit56ys5LiA6KGMDQogICAgICAgIGlmICgNCiAgICAgICAgICB0aGlzLmxlZnRDdXJyZW50UGFnZSA9PT0gMSAmJg0KICAgICAgICAgIHRoaXMubGVmdFRhYmxlRGF0YS5sZW5ndGggPiAwICYmDQogICAgICAgICAgIXRoaXMuc2VsZWN0ZWRMZWZ0Um93DQogICAgICAgICkgew0KICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuaGFuZGxlTGVmdFJvd0NsaWNrKHRoaXMubGVmdFRhYmxlRGF0YVswXSk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuiOt+WPluW3puS+p+aVsOaNruWksei0pToiLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuiOt+WPluWIhuexu+aVsOaNruWksei0pSIpOw0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5sZWZ0TG9hZGluZyA9IGZhbHNlOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDnlJ/miJDmqKHmi5/lt6bkvqfmlbDmja4NCiAgICBnZW5lcmF0ZU1vY2tMZWZ0RGF0YSgpIHsNCiAgICAgIGNvbnN0IGNhdGVnb3JpZXMgPSBbDQogICAgICAgICLkurrlt6Xmmbrog73mioDmnK8iLA0KICAgICAgICAi6YeP5a2Q6K6h566X56CU56m2IiwNCiAgICAgICAgIueUn+eJqeWMu+WtpuW3peeoiyIsDQogICAgICAgICLmlrDog73mupDmioDmnK8iLA0KICAgICAgICAi6Iiq56m66Iiq5aSp5oqA5pyvIiwNCiAgICAgICAgIuadkOaWmeenkeWtpiIsDQogICAgICAgICLkv6Hmga/lronlhajmioDmnK8iLA0KICAgICAgICAi5py65Zmo5Lq65oqA5pyvIiwNCiAgICAgICAgIuWMuuWdl+mTvuaKgOacryIsDQogICAgICAgICLnianogZTnvZHmioDmnK8iLA0KICAgICAgICAiNUfpgJrkv6HmioDmnK8iLA0KICAgICAgICAi6Ieq5Yqo6am+6am25oqA5pyvIiwNCiAgICAgICAgIuiZmuaLn+eOsOWunuaKgOacryIsDQogICAgICAgICLln7rlm6DnvJbovpHmioDmnK8iLA0KICAgICAgICAi57qz57Gz5oqA5pyvIiwNCiAgICAgIF07DQoNCiAgICAgIGNvbnN0IHRvdGFsID0gY2F0ZWdvcmllcy5sZW5ndGg7DQogICAgICBjb25zdCBzdGFydEluZGV4ID0gKHRoaXMubGVmdEN1cnJlbnRQYWdlIC0gMSkgKiB0aGlzLmxlZnRQYWdlU2l6ZTsNCiAgICAgIGNvbnN0IGVuZEluZGV4ID0gTWF0aC5taW4oc3RhcnRJbmRleCArIHRoaXMubGVmdFBhZ2VTaXplLCB0b3RhbCk7DQoNCiAgICAgIGxldCBmaWx0ZXJlZENhdGVnb3JpZXMgPSBjYXRlZ29yaWVzOw0KDQogICAgICBjb25zdCByb3dzID0gZmlsdGVyZWRDYXRlZ29yaWVzDQogICAgICAgIC5zbGljZShzdGFydEluZGV4LCBlbmRJbmRleCkNCiAgICAgICAgLm1hcCgodGl0bGUsIGluZGV4KSA9PiAoew0KICAgICAgICAgIGlkOiBzdGFydEluZGV4ICsgaW5kZXggKyAxLA0KICAgICAgICAgIHRpdGxlOiB0aXRsZSwNCiAgICAgICAgfSkpOw0KDQogICAgICByZXR1cm4gew0KICAgICAgICByb3dzLA0KICAgICAgICB0b3RhbDogZmlsdGVyZWRDYXRlZ29yaWVzLmxlbmd0aCwNCiAgICAgIH07DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluWPs+S+p+ihqOagvOaVsOaNrg0KICAgIGFzeW5jIGdldFJpZ2h0VGFibGVEYXRhKCkgew0KICAgICAgaWYgKCF0aGlzLnNlbGVjdGVkTGVmdFJvdykgew0KICAgICAgICB0aGlzLnJpZ2h0VGFibGVEYXRhID0gW107DQogICAgICAgIHRoaXMucmlnaHRUb3RhbCA9IDA7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgdGhpcy5yaWdodExvYWRpbmcgPSB0cnVlOw0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g5bCd6K+V6LCD55So55yf5a6eQVBJ77yM5aaC5p6c5aSx6LSl5YiZ5L2/55So5qih5ouf5pWw5o2uDQogICAgICAgIHRyeSB7DQogICAgICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICAgICAgY2F0ZWdvcnlJZDogdGhpcy5zZWxlY3RlZExlZnRSb3cuaWQsDQogICAgICAgICAgICBwYWdlTnVtOiB0aGlzLnJpZ2h0Q3VycmVudFBhZ2UsDQogICAgICAgICAgICBwYWdlU2l6ZTogdGhpcy5yaWdodFBhZ2VTaXplLA0KICAgICAgICAgIH07DQogICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBBUEkuZ2V0UmVjb21tZW5kTGlzdChwYXJhbXMpOw0KICAgICAgICAgIHRoaXMucmlnaHRUYWJsZURhdGEgPSByZXNwb25zZS5yb3dzIHx8IHJlc3BvbnNlLmRhdGEgfHwgW107DQogICAgICAgICAgdGhpcy5yaWdodFRvdGFsID0gcmVzcG9uc2UudG90YWwgfHwgMDsNCiAgICAgICAgfSBjYXRjaCAoYXBpRXJyb3IpIHsNCiAgICAgICAgICBjb25zb2xlLndhcm4oIkFQSeiwg+eUqOWksei0pe+8jOS9v+eUqOaooeaLn+aVsOaNrjoiLCBhcGlFcnJvcik7DQogICAgICAgICAgLy8g5L2/55So5qih5ouf5pWw5o2u5L2c5Li65ZCO5aSHDQogICAgICAgICAgY29uc3QgbW9ja0RhdGEgPSB0aGlzLmdlbmVyYXRlTW9ja1JpZ2h0RGF0YSgpOw0KICAgICAgICAgIHRoaXMucmlnaHRUYWJsZURhdGEgPSBtb2NrRGF0YS5yb3dzOw0KICAgICAgICAgIHRoaXMucmlnaHRUb3RhbCA9IG1vY2tEYXRhLnRvdGFsOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLojrflj5blj7PkvqfmlbDmja7lpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLojrflj5bmjqjojZDmlbDmja7lpLHotKUiKTsNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMucmlnaHRMb2FkaW5nID0gZmFsc2U7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOeUn+aIkOaooeaLn+WPs+S+p+aVsOaNrg0KICAgIGdlbmVyYXRlTW9ja1JpZ2h0RGF0YSgpIHsNCiAgICAgIGNvbnN0IG1vY2tBcnRpY2xlcyA9IFsNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAxLA0KICAgICAgICAgIHRpdGxlOiAi5Lq65bel5pm66IO95Zyo5Yy755aX6K+K5pat5Lit55qE5pyA5paw56qB56C0IiwNCiAgICAgICAgICBzY29yZTogOTUsDQogICAgICAgICAgc291cmNlTmFtZTogIuenkeaKgOaXpeaKpSIsDQogICAgICAgICAgcHVibGlzaFRpbWU6ICIyMDI0LTAxLTE1IiwNCiAgICAgICAgICBmaWVsZDogIuS6uuW3peaZuuiDvSIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogMiwNCiAgICAgICAgICB0aXRsZTogIumHj+WtkOiuoeeul+acuuWunueOsOaWsOeahOmHjOeoi+eikSIsDQogICAgICAgICAgc2NvcmU6IDg4LA0KICAgICAgICAgIHNvdXJjZU5hbWU6ICLoh6rnhLbmnYLlv5ciLA0KICAgICAgICAgIHB1Ymxpc2hUaW1lOiAiMjAyNC0wMS0xNCIsDQogICAgICAgICAgZmllbGQ6ICLph4/lrZDorqHnrpciLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDMsDQogICAgICAgICAgdGl0bGU6ICLln7rlm6DnvJbovpHmioDmnK/nmoTkvKbnkIbogIPph48iLA0KICAgICAgICAgIHNjb3JlOiA4MiwNCiAgICAgICAgICBzb3VyY2VOYW1lOiAi55Sf54mp5oqA5pyv5ZGo5YiKIiwNCiAgICAgICAgICBwdWJsaXNoVGltZTogIjIwMjQtMDEtMTMiLA0KICAgICAgICAgIGZpZWxkOiAi55Sf54mp5oqA5pyvIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiA0LA0KICAgICAgICAgIHRpdGxlOiAi5paw6IO95rqQ5rG96L2m55S15rGg5oqA5pyv6Z2p5pawIiwNCiAgICAgICAgICBzY29yZTogNzYsDQogICAgICAgICAgc291cmNlTmFtZTogIuiDvea6kOinguWvnyIsDQogICAgICAgICAgcHVibGlzaFRpbWU6ICIyMDI0LTAxLTEyIiwNCiAgICAgICAgICBmaWVsZDogIuaWsOiDvea6kCIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogNSwNCiAgICAgICAgICB0aXRsZTogIjVH572R57uc5a6J5YWo6Ziy5oqk5paw5pa55qGIIiwNCiAgICAgICAgICBzY29yZTogNzEsDQogICAgICAgICAgc291cmNlTmFtZTogIumAmuS/oeS4lueVjCIsDQogICAgICAgICAgcHVibGlzaFRpbWU6ICIyMDI0LTAxLTExIiwNCiAgICAgICAgICBmaWVsZDogIumAmuS/oeaKgOacryIsDQogICAgICAgIH0sDQogICAgICBdOw0KDQogICAgICAvLyDmoLnmja7pgInkuK3nmoTliIbnsbvnlJ/miJDnm7jlhbPmlbDmja4NCiAgICAgIGNvbnN0IGNhdGVnb3J5UmVsYXRlZEFydGljbGVzID0gbW9ja0FydGljbGVzLm1hcCgoYXJ0aWNsZSwgaW5kZXgpID0+ICh7DQogICAgICAgIC4uLmFydGljbGUsDQogICAgICAgIGlkOiAodGhpcy5yaWdodEN1cnJlbnRQYWdlIC0gMSkgKiB0aGlzLnJpZ2h0UGFnZVNpemUgKyBpbmRleCArIDEsDQogICAgICAgIHRpdGxlOiBgJHt0aGlzLnNlbGVjdGVkTGVmdFJvdy50aXRsZX3nm7jlhbPvvJoke2FydGljbGUudGl0bGV9YCwNCiAgICAgICAgZmllbGQ6IHRoaXMuc2VsZWN0ZWRMZWZ0Um93LnRpdGxlLA0KICAgICAgfSkpOw0KDQogICAgICBjb25zdCB0b3RhbCA9IDUwOyAvLyDmqKHmi5/mgLvmlbANCiAgICAgIGNvbnN0IHN0YXJ0SW5kZXggPSAodGhpcy5yaWdodEN1cnJlbnRQYWdlIC0gMSkgKiB0aGlzLnJpZ2h0UGFnZVNpemU7DQogICAgICBjb25zdCByb3dzID0gY2F0ZWdvcnlSZWxhdGVkQXJ0aWNsZXMuc2xpY2UoDQogICAgICAgIDAsDQogICAgICAgIE1hdGgubWluKHRoaXMucmlnaHRQYWdlU2l6ZSwgdG90YWwgLSBzdGFydEluZGV4KQ0KICAgICAgKTsNCg0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgcm93cywNCiAgICAgICAgdG90YWwsDQogICAgICB9Ow0KICAgIH0sDQoNCiAgICAvLyDlpITnkIblt6bkvqfooYzngrnlh7sNCiAgICBoYW5kbGVMZWZ0Um93Q2xpY2socm93KSB7DQogICAgICB0aGlzLnNlbGVjdGVkTGVmdFJvdyA9IHJvdzsNCiAgICAgIHRoaXMuJHJlZnMubGVmdFRhYmxlLnNldEN1cnJlbnRSb3cocm93KTsNCg0KICAgICAgLy8g6YeN572u5Y+z5L6n5YiG6aG15bm25Yqg6L295pWw5o2uDQogICAgICB0aGlzLnJpZ2h0Q3VycmVudFBhZ2UgPSAxOw0KICAgICAgdGhpcy5nZXRSaWdodFRhYmxlRGF0YSgpOw0KICAgIH0sDQoNCiAgICAvLyDlpITnkIblt6bkvqfliIbpobUNCiAgICBoYW5kbGVMZWZ0UGFnaW5hdGlvbih7IHBhZ2UsIGxpbWl0IH0pIHsNCiAgICAgIHRoaXMubGVmdEN1cnJlbnRQYWdlID0gcGFnZTsNCiAgICAgIHRoaXMubGVmdFBhZ2VTaXplID0gbGltaXQ7DQogICAgICB0aGlzLmdldExlZnRUYWJsZURhdGEoKTsNCiAgICB9LA0KDQogICAgLy8g5aSE55CG5Y+z5L6n5YiG6aG1DQogICAgaGFuZGxlUmlnaHRQYWdpbmF0aW9uKHsgcGFnZSwgbGltaXQgfSkgew0KICAgICAgdGhpcy5yaWdodEN1cnJlbnRQYWdlID0gcGFnZTsNCiAgICAgIHRoaXMucmlnaHRQYWdlU2l6ZSA9IGxpbWl0Ow0KICAgICAgdGhpcy5nZXRSaWdodFRhYmxlRGF0YSgpOw0KICAgIH0sDQoNCiAgICAvLyDojrflj5bliIblgLzmoIfnrb7nsbvlnosNCiAgICBnZXRTY29yZVRhZ1R5cGUoc2NvcmUpIHsNCiAgICAgIGlmIChzY29yZSA+PSA5MCkgcmV0dXJuICJzdWNjZXNzIjsNCiAgICAgIGlmIChzY29yZSA+PSA4MCkgcmV0dXJuICJ3YXJuaW5nIjsNCiAgICAgIGlmIChzY29yZSA+PSA3MCkgcmV0dXJuICJpbmZvIjsNCiAgICAgIHJldHVybiAiZGFuZ2VyIjsNCiAgICB9LA0KDQogICAgLy8g5omT5byA6K+m5oOF6aG1DQogICAgb3BlbkRldGFpbChyb3cpIHsNCiAgICAgIC8vIOi/memHjOWPr+S7peagueaNruWunumZhemcgOaxguaJk+W8gOivpuaDhemhtQ0KICAgICAgLy8g5L6L5aaC77ya6Lez6L2s5Yiw5paw6aG16Z2i5oiW5omT5byA5by556qXDQogICAgICBjb25zdCB1cmwgPSBgL2V4cHJlc3NEZXRhaWxzP2lkPSR7cm93LmlkfWA7DQogICAgICB3aW5kb3cub3Blbih1cmwsICJfYmxhbmsiKTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0IA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/kejiz<PERSON><PERSON><PERSON>an", "sourcesContent": ["<template>\r\n  <div v-loading=\"globalLoading\" element-loading-text=\"数据加载中\">\r\n    <splitpanes class=\"default-theme\">\r\n      <!-- 左侧面板 -->\r\n      <pane\r\n        class=\"leftLink\"\r\n        ref=\"leftLink\"\r\n        min-size=\"20\"\r\n        max-size=\"50\"\r\n        size=\"30\"\r\n      >\r\n        <div class=\"left-panel\">\r\n          <!-- 左侧表格 -->\r\n          <div class=\"left-table-container\">\r\n            <el-table\r\n              :data=\"leftTableData\"\r\n              v-loading=\"leftLoading\"\r\n              @row-click=\"handleLeftRowClick\"\r\n              :highlight-current-row=\"true\"\r\n              ref=\"leftTable\"\r\n              height=\"auto\"\r\n              size=\"small\"\r\n              border\r\n              :show-header=\"false\"\r\n              style=\"font-size: 14px\"\r\n            >\r\n              <el-table-column\r\n                type=\"index\"\r\n                label=\"序号\"\r\n                width=\"60\"\r\n                align=\"center\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  {{ (leftCurrentPage - 1) * leftPageSize + scope.$index + 1 }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"title\" label=\"标题\" show-overflow-tooltip>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <!-- 左侧分页 -->\r\n            <div class=\"left-pagination\">\r\n              <pagination\r\n                v-show=\"leftTotal > 0\"\r\n                :total=\"leftTotal\"\r\n                :page.sync=\"leftCurrentPage\"\r\n                :limit.sync=\"leftPageSize\"\r\n                @pagination=\"handleLeftPagination\"\r\n                :layout=\"'total, prev, pager, next'\"\r\n                :background=\"false\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </pane>\r\n\r\n      <!-- 右侧面板 -->\r\n      <pane min-size=\"50\" max-size=\"80\" size=\"70\">\r\n        <div class=\"right-panel\">\r\n          <!-- 右侧表格 -->\r\n          <div class=\"right-table-container\">\r\n            <el-table\r\n              :data=\"rightTableData\"\r\n              v-loading=\"rightLoading\"\r\n              height=\"auto\"\r\n              size=\"small\"\r\n              border\r\n              style=\"font-size: 14px\"\r\n            >\r\n              <el-table-column\r\n                prop=\"score\"\r\n                label=\"推荐分值\"\r\n                width=\"100\"\r\n                align=\"center\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag :type=\"getScoreTagType(scope.row.score)\" size=\"small\">\r\n                    {{ scope.row.score }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"title\" label=\"标题\" show-overflow-tooltip>\r\n                <template slot-scope=\"scope\">\r\n                  <el-link\r\n                    type=\"primary\"\r\n                    @click=\"openDetail(scope.row)\"\r\n                    :underline=\"false\"\r\n                  >\r\n                    {{ scope.row.title }}\r\n                  </el-link>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"sourceName\"\r\n                label=\"来源名称\"\r\n                width=\"150\"\r\n                show-overflow-tooltip\r\n              >\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"publishTime\"\r\n                label=\"发布日期\"\r\n                width=\"120\"\r\n                align=\"center\"\r\n              >\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"field\"\r\n                label=\"所属领域\"\r\n                width=\"120\"\r\n                show-overflow-tooltip\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag size=\"mini\" v-if=\"scope.row.field\">\r\n                    {{ scope.row.field }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <!-- 右侧分页 -->\r\n            <div class=\"right-pagination\">\r\n              <pagination\r\n                v-show=\"rightTotal > 0\"\r\n                :total=\"rightTotal\"\r\n                :page.sync=\"rightCurrentPage\"\r\n                :limit.sync=\"rightPageSize\"\r\n                @pagination=\"handleRightPagination\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </pane>\r\n    </splitpanes>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { Splitpanes, Pane } from \"splitpanes\";\r\nimport \"splitpanes/dist/splitpanes.css\";\r\nimport API from \"@/api/ScienceApi/index.js\";\r\n\r\nexport default {\r\n  name: \"KejizixuntuijianIndex\",\r\n  components: {\r\n    Splitpanes,\r\n    Pane,\r\n  },\r\n  data() {\r\n    return {\r\n      // 全局加载状态\r\n      globalLoading: false,\r\n\r\n      // 左侧数据\r\n      leftTableData: [],\r\n      leftLoading: false,\r\n      leftTotal: 0,\r\n      leftCurrentPage: 1,\r\n      leftPageSize: 20,\r\n      leftSearchTimer: null,\r\n      selectedLeftRow: null, // 当前选中的左侧行\r\n\r\n      // 右侧数据\r\n      rightTableData: [],\r\n      rightLoading: false,\r\n      rightTotal: 0,\r\n      rightCurrentPage: 1,\r\n      rightPageSize: 20,\r\n    };\r\n  },\r\n  created() {\r\n    this.initData();\r\n  },\r\n  methods: {\r\n    // 初始化数据\r\n    async initData() {\r\n      this.globalLoading = true;\r\n      try {\r\n        await this.getLeftTableData();\r\n      } catch (error) {\r\n        console.error(\"初始化数据失败:\", error);\r\n        this.$message.error(\"数据加载失败，请重试\");\r\n      } finally {\r\n        this.globalLoading = false;\r\n      }\r\n    },\r\n\r\n    // 获取左侧表格数据\r\n    async getLeftTableData() {\r\n      this.leftLoading = true;\r\n      try {\r\n        // 尝试调用真实API，如果失败则使用模拟数据\r\n        try {\r\n          const params = {\r\n            pageNum: this.leftCurrentPage,\r\n            pageSize: this.leftPageSize,\r\n          };\r\n          const response = await API.getCategoryList(params);\r\n          this.leftTableData = response.rows || response.data || [];\r\n          this.leftTotal = response.total || 0;\r\n        } catch (apiError) {\r\n          console.warn(\"API调用失败，使用模拟数据:\", apiError);\r\n          // 使用模拟数据作为后备\r\n          const mockData = this.generateMockLeftData();\r\n          this.leftTableData = mockData.rows;\r\n          this.leftTotal = mockData.total;\r\n        }\r\n\r\n        // 如果是第一次加载且有数据，自动选中第一行\r\n        if (\r\n          this.leftCurrentPage === 1 &&\r\n          this.leftTableData.length > 0 &&\r\n          !this.selectedLeftRow\r\n        ) {\r\n          this.$nextTick(() => {\r\n            this.handleLeftRowClick(this.leftTableData[0]);\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取左侧数据失败:\", error);\r\n        this.$message.error(\"获取分类数据失败\");\r\n      } finally {\r\n        this.leftLoading = false;\r\n      }\r\n    },\r\n\r\n    // 生成模拟左侧数据\r\n    generateMockLeftData() {\r\n      const categories = [\r\n        \"人工智能技术\",\r\n        \"量子计算研究\",\r\n        \"生物医学工程\",\r\n        \"新能源技术\",\r\n        \"航空航天技术\",\r\n        \"材料科学\",\r\n        \"信息安全技术\",\r\n        \"机器人技术\",\r\n        \"区块链技术\",\r\n        \"物联网技术\",\r\n        \"5G通信技术\",\r\n        \"自动驾驶技术\",\r\n        \"虚拟现实技术\",\r\n        \"基因编辑技术\",\r\n        \"纳米技术\",\r\n      ];\r\n\r\n      const total = categories.length;\r\n      const startIndex = (this.leftCurrentPage - 1) * this.leftPageSize;\r\n      const endIndex = Math.min(startIndex + this.leftPageSize, total);\r\n\r\n      let filteredCategories = categories;\r\n\r\n      const rows = filteredCategories\r\n        .slice(startIndex, endIndex)\r\n        .map((title, index) => ({\r\n          id: startIndex + index + 1,\r\n          title: title,\r\n        }));\r\n\r\n      return {\r\n        rows,\r\n        total: filteredCategories.length,\r\n      };\r\n    },\r\n\r\n    // 获取右侧表格数据\r\n    async getRightTableData() {\r\n      if (!this.selectedLeftRow) {\r\n        this.rightTableData = [];\r\n        this.rightTotal = 0;\r\n        return;\r\n      }\r\n\r\n      this.rightLoading = true;\r\n      try {\r\n        // 尝试调用真实API，如果失败则使用模拟数据\r\n        try {\r\n          const params = {\r\n            categoryId: this.selectedLeftRow.id,\r\n            pageNum: this.rightCurrentPage,\r\n            pageSize: this.rightPageSize,\r\n          };\r\n          const response = await API.getRecommendList(params);\r\n          this.rightTableData = response.rows || response.data || [];\r\n          this.rightTotal = response.total || 0;\r\n        } catch (apiError) {\r\n          console.warn(\"API调用失败，使用模拟数据:\", apiError);\r\n          // 使用模拟数据作为后备\r\n          const mockData = this.generateMockRightData();\r\n          this.rightTableData = mockData.rows;\r\n          this.rightTotal = mockData.total;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取右侧数据失败:\", error);\r\n        this.$message.error(\"获取推荐数据失败\");\r\n      } finally {\r\n        this.rightLoading = false;\r\n      }\r\n    },\r\n\r\n    // 生成模拟右侧数据\r\n    generateMockRightData() {\r\n      const mockArticles = [\r\n        {\r\n          id: 1,\r\n          title: \"人工智能在医疗诊断中的最新突破\",\r\n          score: 95,\r\n          sourceName: \"科技日报\",\r\n          publishTime: \"2024-01-15\",\r\n          field: \"人工智能\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"量子计算机实现新的里程碑\",\r\n          score: 88,\r\n          sourceName: \"自然杂志\",\r\n          publishTime: \"2024-01-14\",\r\n          field: \"量子计算\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"基因编辑技术的伦理考量\",\r\n          score: 82,\r\n          sourceName: \"生物技术周刊\",\r\n          publishTime: \"2024-01-13\",\r\n          field: \"生物技术\",\r\n        },\r\n        {\r\n          id: 4,\r\n          title: \"新能源汽车电池技术革新\",\r\n          score: 76,\r\n          sourceName: \"能源观察\",\r\n          publishTime: \"2024-01-12\",\r\n          field: \"新能源\",\r\n        },\r\n        {\r\n          id: 5,\r\n          title: \"5G网络安全防护新方案\",\r\n          score: 71,\r\n          sourceName: \"通信世界\",\r\n          publishTime: \"2024-01-11\",\r\n          field: \"通信技术\",\r\n        },\r\n      ];\r\n\r\n      // 根据选中的分类生成相关数据\r\n      const categoryRelatedArticles = mockArticles.map((article, index) => ({\r\n        ...article,\r\n        id: (this.rightCurrentPage - 1) * this.rightPageSize + index + 1,\r\n        title: `${this.selectedLeftRow.title}相关：${article.title}`,\r\n        field: this.selectedLeftRow.title,\r\n      }));\r\n\r\n      const total = 50; // 模拟总数\r\n      const startIndex = (this.rightCurrentPage - 1) * this.rightPageSize;\r\n      const rows = categoryRelatedArticles.slice(\r\n        0,\r\n        Math.min(this.rightPageSize, total - startIndex)\r\n      );\r\n\r\n      return {\r\n        rows,\r\n        total,\r\n      };\r\n    },\r\n\r\n    // 处理左侧行点击\r\n    handleLeftRowClick(row) {\r\n      this.selectedLeftRow = row;\r\n      this.$refs.leftTable.setCurrentRow(row);\r\n\r\n      // 重置右侧分页并加载数据\r\n      this.rightCurrentPage = 1;\r\n      this.getRightTableData();\r\n    },\r\n\r\n    // 处理左侧分页\r\n    handleLeftPagination({ page, limit }) {\r\n      this.leftCurrentPage = page;\r\n      this.leftPageSize = limit;\r\n      this.getLeftTableData();\r\n    },\r\n\r\n    // 处理右侧分页\r\n    handleRightPagination({ page, limit }) {\r\n      this.rightCurrentPage = page;\r\n      this.rightPageSize = limit;\r\n      this.getRightTableData();\r\n    },\r\n\r\n    // 获取分值标签类型\r\n    getScoreTagType(score) {\r\n      if (score >= 90) return \"success\";\r\n      if (score >= 80) return \"warning\";\r\n      if (score >= 70) return \"info\";\r\n      return \"danger\";\r\n    },\r\n\r\n    // 打开详情页\r\n    openDetail(row) {\r\n      // 这里可以根据实际需求打开详情页\r\n      // 例如：跳转到新页面或打开弹窗\r\n      const url = `/expressDetails?id=${row.id}`;\r\n      window.open(url, \"_blank\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.left-panel {\r\n  height: calc(100vh - 60px);\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.left-table-container {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: white;\r\n  overflow: hidden;\r\n}\r\n\r\n.left-pagination {\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  text-align: center;\r\n}\r\n\r\n.right-panel {\r\n  height: calc(100vh - 60px);\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.right-table-container {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: white;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.right-pagination {\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  text-align: center;\r\n}\r\n\r\n// 左侧表格行选中样式\r\n.left-table-container {\r\n  ::v-deep .el-table__row {\r\n    cursor: pointer;\r\n  }\r\n\r\n  ::v-deep .el-table__row:hover {\r\n    background-color: #f5f7fa;\r\n  }\r\n\r\n  ::v-deep .current-row {\r\n    background-color: #ecf5ff !important;\r\n  }\r\n}\r\n\r\n// 分页组件样式调整\r\n::v-deep .el-pagination {\r\n  top: -2px;\r\n  .el-pagination__sizes {\r\n    margin-top: -2px;\r\n  }\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\kejizixuntuijian\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\kejizixuntuijian\\index.vue", "mtime": 1754444722156}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KDQpleHBvcnQgZGVmYXVsdCB7fTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;AAGA", "file": "index.vue", "sourceRoot": "src/views/kejiz<PERSON><PERSON><PERSON>an", "sourcesContent": ["<template></template>\r\n\r\n<script>\r\nexport default {};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped></style>\r\n"]}]}
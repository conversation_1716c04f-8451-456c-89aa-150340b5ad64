{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\expressDetails\\index2.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\expressDetails\\index2.vue", "mtime": 1754397040249}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\babel.config.js", "mtime": 1745890588273}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vuex", "require", "_index", "_interopRequireDefault", "_leaveMessage", "_htmlUtils", "_markdownUtils", "_MarkmapDialog", "_index2", "dicts", "components", "MarkmapDialog", "TextEllipsis", "data", "loading", "contentReady", "translationBtnShow", "titleShow", "drawerInfo", "htmlJson", "processedHtml", "originalArticleShow", "articleList", "type", "textarea", "url", "tabPosition", "totalImages", "loadedImages", "imageLoadTimeout", "markdownContent", "keyPoints", "entities", "markmapVisible", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "markmapTitle", "mark<PERSON>p<PERSON><PERSON><PERSON>", "computed", "_objectSpread2", "default", "mapGetters", "isWeixinArticle", "originalUrl", "includes", "keyPointsFormatted", "formatMarkdown", "entitiesFormatted", "mounted", "addNoReferrerMeta", "details", "getIndexData", "watch", "newVal", "oldVal", "console", "log", "concat", "methods", "document", "querySelector", "meta", "createElement", "name", "content", "head", "append<PERSON><PERSON><PERSON>", "fixImage<PERSON><PERSON>errer", "replace", "match", "attrPart", "replaceAllWechatImages", "Promise", "resolve", "wechatImages", "querySelectorAll", "length", "processedCount", "for<PERSON>ach", "img", "index", "hasAttribute", "complete", "naturalWidth", "originalWidth", "style", "width", "originalHeight", "height", "originalSrc", "getAttribute", "setAttribute", "classList", "add", "max<PERSON><PERSON><PERSON>", "startsWith", "httpsUrl", "src", "separator", "srcWithFormat", "setTimeout", "e", "error", "forceReloadContent", "_this", "$nextTick", "fixWechatImages", "then", "$forceUpdate", "preloadWechatImages", "_this2", "fixEscapedImageTags", "handleWechatImages", "handleRelativeImagePaths", "escapedTagRegex", "badStyleRegex", "brokenPathRegex", "escapedTags", "badStyleTags", "brokenPathTags", "allTags", "_toConsumableArray2", "Set", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "n", "done", "tag", "value", "imgUrl", "patterns", "_i", "_patterns", "pattern", "newTag", "err", "f", "textContent", "onerror", "border", "padding", "minHeight", "alt", "_this$drawerInfo", "urlParts", "split", "pop", "baseUrl", "join", "before", "after", "test", "newSrc", "substring", "url<PERSON>bj", "URL", "protocol", "host", "regex", "newContent", "escapedRegex", "_this3", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "API", "recommendHot", "response", "code", "slice", "map", "item", "cnTitle", "title", "id", "articleId", "stop", "_this4", "_callee3", "params", "_callee3$", "_context3", "$route", "query", "articleSn", "articleDetail", "_ref", "_callee2", "res", "rawContent", "processedContent", "preprocessMarkdown", "_callee2$", "_context2", "hanldeBrowseAdd", "sourceType", "cnC<PERSON>nt", "formattingJson", "swdt", "Array", "isArray", "swdtTaskid", "swdtContent", "text", "sent", "t0", "$set", "_x", "apply", "arguments", "containsHtmlTags", "extractHtmlTags", "hasValidHtmlStructure", "translateEvent", "row", "_this5", "fun", "$loading", "lock", "spinner", "background", "translationTitle", "originalText", "docId", "translationField", "translationType", "close", "catch", "$confirm", "confirmButtonText", "cancelButtonText", "viewOriginal", "backToTop", "window", "scrollTo", "top", "behavior", "submit", "_this6", "feedbackAdd", "$message", "message", "openNewView", "open", "handleCollections", "_this7", "collection", "collectApi", "cocelCollect", "handleRecommend", "_this8", "FormData", "append", "recommend", "recommendAdd", "recommendCancel", "browseAdd", "viewOriginalArticle", "openMarkmap", "warning", "storageKey", "Date", "now", "localStorage", "setItem", "URLSearchParams", "screenWidth", "screen", "screenHeight", "Math", "round", "left", "windowFeatures", "newWindow", "toString", "closed", "info", "handleMarkmapClose", "formatPublishTime", "publishTime", "publishLocaltime", "formattedPublishTime", "parseTime", "formattedWebsteTime"], "sources": ["src/views/expressDetails/index2.vue"], "sourcesContent": ["<template>\r\n  <div class=\"drawer_box\" v-loading=\"loading\">\r\n    <div\r\n      class=\"drawer_Style\"\r\n      :style=\"{ marginBottom: type ? '40px' : '100px' }\"\r\n    >\r\n      <div>\r\n        <!--p class=\"title\">\r\n          {{ drawerInfo.cnTitle || drawerInfo.title }}\r\n        </p-->\r\n        <p class=\"title\">\r\n          {{ drawerInfo.cnTitle }}\r\n        </p>\r\n        <p class=\"title\" v-if=\"titleShow\">\r\n          {{ drawerInfo.title }}\r\n        </p>\r\n        <p>\r\n          <span class=\"source\">{{ drawerInfo.sourceName }}</span>\r\n          <span class=\"time\">{{\r\n            formatPublishTime(\r\n              drawerInfo.publishTime,\r\n              drawerInfo.publishLocaltime\r\n            )\r\n          }}</span>\r\n          <span class=\"author\">{{ drawerInfo.author }}</span>\r\n        </p>\r\n        <el-divider></el-divider>\r\n        <div class=\"summary\" v-if=\"drawerInfo.cnSummary || drawerInfo.summary\">\r\n          <div class=\"summary-item1\">\r\n            小信导读：<span style=\"color: #838484\">【内容由大模型生成】</span>\r\n          </div>\r\n          <div class=\"summary-item2\">\r\n            <text-ellipsis\r\n              :text=\"drawerInfo.cnSummary || drawerInfo.summary\"\r\n              :max-lines=\"10\"\r\n              more-text=\"展开\"\r\n              less-text=\"收起\"\r\n            ></text-ellipsis>\r\n          </div>\r\n        </div>\r\n        <!-- 内容区域 -->\r\n        <div v-if=\"!contentReady && isWeixinArticle\" class=\"content-loading\">\r\n          <el-alert\r\n            title=\"正在处理微信图片，请稍等...\"\r\n            type=\"info\"\r\n            center\r\n            :closable=\"false\"\r\n            show-icon\r\n          ></el-alert>\r\n          <div v-if=\"totalImages > 0\" class=\"loading-progress\">\r\n            <div class=\"loading-text\">\r\n              图片加载进度: {{ loadedImages }}/{{ totalImages }}\r\n            </div>\r\n            <el-progress\r\n              :percentage=\"Math.floor((loadedImages / totalImages) * 100)\"\r\n              :show-text=\"false\"\r\n              status=\"success\"\r\n            ></el-progress>\r\n          </div>\r\n        </div>\r\n        <div\r\n          v-else\r\n          style=\"line-height: 30px; white-space: normal; word-break: break-word\"\r\n          v-html=\"htmlJson\"\r\n        ></div>\r\n        <el-empty description=\"当前文章暂无数据\" v-if=\"!htmlJson\"></el-empty>\r\n      </div>\r\n      <div class=\"liuyanBox\" :style=\"{ height: type ? '50px' : '110px' }\">\r\n        <div class=\"morenzhuangtai\" v-if=\"type\">\r\n          <div class=\"uesr\">\r\n            <img :src=\"avatar\" class=\"avatar\" />\r\n            <div class=\"name\">{{ name }}</div>\r\n          </div>\r\n          <div class=\"button\">\r\n            <el-tooltip\r\n              class=\"item\"\r\n              effect=\"dark\"\r\n              :content=\"drawerInfo.recommend ? '取消推荐' : '推荐'\"\r\n              placement=\"top\"\r\n            >\r\n              <el-button\r\n                :icon=\"\r\n                  drawerInfo.recommend\r\n                    ? 'el-icon-message-solid'\r\n                    : 'el-icon-bell'\r\n                \"\r\n                :type=\"drawerInfo.recommend ? 'primary' : ''\"\r\n                circle\r\n                @click=\"handleRecommend(drawerInfo)\"\r\n              ></el-button>\r\n            </el-tooltip>\r\n            <el-tooltip\r\n              class=\"item\"\r\n              effect=\"dark\"\r\n              :content=\"drawerInfo.collection ? '取消收藏' : '收藏'\"\r\n              placement=\"top\"\r\n            >\r\n              <el-button\r\n                :icon=\"\r\n                  drawerInfo.collection ? 'el-icon-star-on' : 'el-icon-star-off'\r\n                \"\r\n                :type=\"drawerInfo.collection ? 'primary' : ''\"\r\n                circle\r\n                @click=\"handleCollections(drawerInfo)\"\r\n              ></el-button>\r\n            </el-tooltip>\r\n            <el-button type=\"text\" icon=\"el-icon-edit\" @click=\"type = false\"\r\n              >写留言</el-button\r\n            >\r\n          </div>\r\n        </div>\r\n        <div class=\"shuruzhuangtai\" v-else>\r\n          <div class=\"top\">\r\n            <div class=\"uesr\">\r\n              <img :src=\"avatar\" class=\"avatar\" />\r\n              <div class=\"name\">{{ name }}</div>\r\n            </div>\r\n            <div class=\"button\">\r\n              <el-button size=\"mini\" type=\"primary\" @click=\"submit\"\r\n                >确定</el-button\r\n              >\r\n              <el-button size=\"mini\" type=\"info\" @click=\"close\">取消</el-button>\r\n            </div>\r\n          </div>\r\n          <div class=\"bottom\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              placeholder=\"请输入内容\"\r\n              v-model=\"textarea\"\r\n              maxlength=\"300\"\r\n              show-word-limit\r\n              resize=\"none\"\r\n            ></el-input>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"right\">\r\n      <div class=\"sisterDetails\">\r\n        <div class=\"right_title\">媒体详情</div>\r\n        <el-divider class=\"el-divider-right\"></el-divider>\r\n        <div class=\"media-info\">\r\n          <div class=\"media-info-item\">\r\n            <div class=\"name\">\r\n              媒体名称:<span class=\"value\">{{ drawerInfo.sourceName }}</span>\r\n            </div>\r\n            <div class=\"name\">媒体等级:<span class=\"value\"></span></div>\r\n          </div>\r\n          <div class=\"media-info-item\">\r\n            <div class=\"name\">媒体地域:<span class=\"value\"></span></div>\r\n            <div class=\"name\">媒体行业:<span class=\"value\"></span></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div\r\n        class=\"markdown\"\r\n        v-if=\"keyPointsFormatted || entitiesFormatted\"\r\n        :style=\"{\r\n          height: keyPointsFormatted && entitiesFormatted ? '810px' : '430px',\r\n        }\"\r\n      >\r\n        <div class=\"right_title\">\r\n          <span style=\"color: #971231\">小信解读</span>\r\n          <span style=\"color: #838484\">【内容由大模型生成】</span>\r\n        </div>\r\n        <el-divider class=\"el-divider-markdown\"></el-divider>\r\n        <div\r\n          class=\"markdown-content\"\r\n          style=\"margin-top: 10px\"\r\n          v-if=\"keyPointsFormatted\"\r\n        >\r\n          <div class=\"markdown-content-title\">\r\n            内容要点\r\n            <span class=\"view-markmap\" @click=\"openMarkmap('keyPoints')\"\r\n              >点击放大查看</span\r\n            >\r\n          </div>\r\n          <div\r\n            class=\"markdown-content-text\"\r\n            v-html=\"keyPointsFormatted\"\r\n            style=\"background-color: #e8d9cc\"\r\n          ></div>\r\n        </div>\r\n        <div\r\n          class=\"markdown-content\"\r\n          :style=\"{ marginTop: keyPointsFormatted ? '20px' : '10px' }\"\r\n          v-if=\"entitiesFormatted\"\r\n        >\r\n          <div class=\"markdown-content-title\">\r\n            人员/机构/技术/产品\r\n            <span class=\"view-markmap\" @click=\"openMarkmap('entities')\"\r\n              >点击放大查看</span\r\n            >\r\n          </div>\r\n          <div\r\n            class=\"markdown-content-text\"\r\n            v-html=\"entitiesFormatted\"\r\n            style=\"background-color: #dce4d4\"\r\n          ></div>\r\n        </div>\r\n      </div>\r\n      <div class=\"recommendedArticle\">\r\n        <div class=\"right_title\">推荐文章</div>\r\n        <el-divider class=\"el-divider-right\"></el-divider>\r\n        <div class=\"articleBox\" v-for=\"item in articleList\" :key=\"item.id\">\r\n          <div class=\"article\" @click=\"openNewView(item)\">\r\n            {{ item.cnTitle }}\r\n          </div>\r\n          <div class=\"bottom\">\r\n            <div class=\"time\">{{ item.publishTime }}</div>\r\n            <div class=\"sourceName\">{{ item.sourceName }}</div>\r\n            <span class=\"count\">{{ `推荐数量：${item.count}` }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- <div class=\"keyword\">\r\n        <div class=\"right_title\">关键词</div>\r\n        <el-divider class=\"el-divider-right\"></el-divider>\r\n        <div class=\"chartBox\">\r\n          <el-image style=\"width: 100%; height: 290px\" :src=\"url\">\r\n          </el-image>\r\n        </div>\r\n      </div> -->\r\n      <!-- <div class=\"articleEntity\">\r\n        <div class=\"right_title\">文章实体</div>\r\n        <el-divider class=\"el-divider-right\"></el-divider>\r\n        <div class=\"chartBox\">\r\n          <el-radio-group v-model=\"tabPosition\" style=\"margin-bottom: 30px; position: absolute;   z-index: 3;\">\r\n            <el-radio-button label=\"1\">通用</el-radio-button>\r\n            <el-radio-button label=\"2\">人物</el-radio-button>\r\n            <el-radio-button label=\"3\">地域</el-radio-button>\r\n            <el-radio-button label=\"4\">机构</el-radio-button>\r\n          </el-radio-group>\r\n          <el-image style=\"width: 100%; height: 290px\" :src=\"url\">\r\n          </el-image>\r\n        </div>\r\n      </div> -->\r\n    </div>\r\n    <div class=\"tabs-all\">\r\n      <div class=\"tabs\" v-if=\"translationBtnShow\">\r\n        <span @click=\"viewOriginal()\">{{\r\n          this.originalArticleShow ? \"查看原文\" : \"查看中文\"\r\n        }}</span>\r\n      </div>\r\n      <div class=\"tabs\">\r\n        <span @click=\"viewOriginalArticle(drawerInfo)\">原文链接</span>\r\n      </div>\r\n      <div\r\n        class=\"tabs\"\r\n        v-hasPermi=\"['article:articleList:monitoringAndSpecial']\"\r\n        v-if=\"translationBtnShow\"\r\n      >\r\n        <span @click=\"translateEvent(drawerInfo)\">机器翻译</span>\r\n      </div>\r\n      <!-- <div class=\"tabs\" v-if=\"translationBtnShow\">\r\n        <span @click=\"\">人工翻译</span>\r\n      </div> -->\r\n\r\n      <div class=\"tabs\">\r\n        <span @click=\"backToTop()\">返回顶部</span>\r\n      </div>\r\n    </div>\r\n    <markmap-dialog\r\n      :visible.sync=\"markmapVisible\"\r\n      :content=\"markmapContent\"\r\n      :title=\"markmapTitle\"\r\n      :loading=\"markmapLoading\"\r\n      @close=\"handleMarkmapClose\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from \"vuex\";\r\nimport API from \"@/api/ScienceApi/index.js\";\r\nimport { feedbackAdd } from \"@/api/article/leaveMessage\";\r\nimport {\r\n  containsHtmlTags,\r\n  extractHtmlTags,\r\n  hasValidHtmlStructure,\r\n} from \"@/utils/htmlUtils\";\r\nimport { formatMarkdown } from \"@/utils/markdownUtils\";\r\nimport MarkmapDialog from \"./MarkmapDialog.vue\";\r\nimport TextEllipsis from \"@/components/TextEllipsis/index.vue\";\r\n\r\nexport default {\r\n  dicts: [],\r\n  components: {\r\n    MarkmapDialog,\r\n    TextEllipsis,\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      contentReady: false,\r\n      translationBtnShow: true,\r\n      titleShow: true,\r\n      drawerInfo: {},\r\n      htmlJson: \"\",\r\n      processedHtml: \"\",\r\n      originalArticleShow: true,\r\n      articleList: [],\r\n      type: true,\r\n      textarea: \"\",\r\n      url: \"\",\r\n      tabPosition: 1,\r\n      totalImages: 0,\r\n      loadedImages: 0,\r\n      imageLoadTimeout: null,\r\n      markdownContent: {\r\n        keyPoints: \"\",\r\n        entities: \"\",\r\n      },\r\n      markmapVisible: false,\r\n      markmapContent: \"\",\r\n      markmapTitle: \"\",\r\n      markmapLoading: false,\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"roles\", \"name\", \"avatar\"]),\r\n    isWeixinArticle() {\r\n      return (\r\n        this.drawerInfo.originalUrl &&\r\n        this.drawerInfo.originalUrl.includes(\"https://mp.weixin.qq.com\")\r\n      );\r\n    },\r\n    keyPointsFormatted() {\r\n      return formatMarkdown(this.markdownContent.keyPoints);\r\n    },\r\n    entitiesFormatted() {\r\n      return formatMarkdown(this.markdownContent.entities);\r\n    },\r\n  },\r\n  mounted() {\r\n    this.loading = true;\r\n    this.translationBtnShow = true;\r\n\r\n    // 添加全局meta标签禁用referrer\r\n    this.addNoReferrerMeta();\r\n\r\n    this.details();\r\n    this.getIndexData();\r\n    // this.hanldeBrowseAdd();\r\n  },\r\n  watch: {\r\n    tabPosition(newVal, oldVal) {\r\n      console.log(`Tab position changed from ${oldVal} to ${newVal}`);\r\n    },\r\n  },\r\n  methods: {\r\n    // 添加全局meta标签来禁用所有引用头\r\n    addNoReferrerMeta() {\r\n      // 检查是否已经存在meta标签\r\n      if (\r\n        document.querySelector('meta[name=\"referrer\"][content=\"no-referrer\"]')\r\n      ) {\r\n        return;\r\n      }\r\n\r\n      // 创建并添加meta标签\r\n      const meta = document.createElement(\"meta\");\r\n      meta.name = \"referrer\";\r\n      meta.content = \"no-referrer\";\r\n      document.head.appendChild(meta);\r\n      console.log(\"已添加no-referrer meta标签\");\r\n    },\r\n\r\n    // 辅助方法：修复图片的referrer设置\r\n    fixImageReferrer(content) {\r\n      // 确保每个微信图片标签都有referrerpolicy=\"no-referrer\"属性\r\n      if (!content) return content;\r\n\r\n      // 替换所有微信图片标签，确保它们有referrerpolicy属性\r\n      return content.replace(\r\n        /<img([^>]*?src=[\"']https?:\\/\\/mmbiz\\.q(?:logo|pic)\\.cn\\/[^\"']+[\"'][^>]*?)>/gi,\r\n        (match, attrPart) => {\r\n          // 如果已经有referrerpolicy属性，不再添加\r\n          if (attrPart.includes(\"referrerpolicy\")) {\r\n            return match;\r\n          }\r\n          // 添加referrerpolicy属性\r\n          return `<img${attrPart} referrerpolicy=\"no-referrer\">`;\r\n        }\r\n      );\r\n    },\r\n\r\n    // 使用canvas或图片代理增强微信图片的方法（不替换DOM节点）\r\n    replaceAllWechatImages() {\r\n      // 如果不是微信文章，直接返回\r\n      if (!this.isWeixinArticle) {\r\n        return Promise.resolve();\r\n      }\r\n\r\n      return new Promise((resolve) => {\r\n        try {\r\n          // 找到所有微信域名的图片\r\n          const wechatImages = document.querySelectorAll(\r\n            'img[src*=\"mmbiz.qpic.cn\"], img[src*=\"mmbiz.qlogo.cn\"], img[data-src*=\"mmbiz\"], img[src*=\"mmsns.qpic.cn\"]'\r\n          );\r\n\r\n          if (wechatImages.length === 0) {\r\n            resolve();\r\n            return;\r\n          }\r\n\r\n          console.log(`开始增强${wechatImages.length}张微信图片，保留现有图片`);\r\n          let processedCount = 0;\r\n\r\n          // 处理每一张图片\r\n          wechatImages.forEach((img, index) => {\r\n            // 如果图片已经被替换过且非空白，则跳过\r\n            if (\r\n              img.hasAttribute(\"data-wx-replaced\") &&\r\n              img.complete &&\r\n              img.naturalWidth > 0\r\n            ) {\r\n              processedCount++;\r\n              if (processedCount >= wechatImages.length) {\r\n                resolve();\r\n              }\r\n              return;\r\n            }\r\n\r\n            // 记录原始尺寸和样式\r\n            const originalWidth = img.style.width || img.width || \"auto\";\r\n            const originalHeight = img.style.height || img.height || \"auto\";\r\n\r\n            // 只有当图片无法显示时才进行处理\r\n            if (!img.complete || img.naturalWidth === 0) {\r\n              // 获取图片源\r\n              const originalSrc =\r\n                img.getAttribute(\"data-original-src\") ||\r\n                img.getAttribute(\"src\");\r\n              if (!originalSrc) {\r\n                // 无法获取源，跳过\r\n                processedCount++;\r\n                if (processedCount >= wechatImages.length) {\r\n                  resolve();\r\n                }\r\n                return;\r\n              }\r\n\r\n              // 添加必要的属性\r\n              img.setAttribute(\"referrerpolicy\", \"no-referrer\");\r\n              img.classList.add(\"wx-img\");\r\n              img.style.maxWidth = \"100%\";\r\n              img.style.height = \"auto\";\r\n\r\n              // 设置标识\r\n              img.setAttribute(\"data-wx-replaced\", \"true\");\r\n\r\n              // 尝试使用HTTPS加载\r\n              if (originalSrc.startsWith(\"http:\")) {\r\n                const httpsUrl = originalSrc.replace(/^http:/, \"https:\");\r\n                console.log(`尝试使用HTTPS协议: ${httpsUrl}`);\r\n                img.src = httpsUrl;\r\n              }\r\n              // 尝试添加格式参数\r\n              else if (!originalSrc.includes(\"wx_fmt=\")) {\r\n                const separator = originalSrc.includes(\"?\") ? \"&\" : \"?\";\r\n                const srcWithFormat = `${originalSrc}${separator}wx_fmt=jpeg`;\r\n                console.log(`尝试添加格式参数: ${srcWithFormat}`);\r\n                img.src = srcWithFormat;\r\n              }\r\n            }\r\n\r\n            // 无论是否处理，都计数\r\n            processedCount++;\r\n            if (processedCount >= wechatImages.length) {\r\n              resolve();\r\n            }\r\n          });\r\n\r\n          // 设置超时保障\r\n          setTimeout(() => {\r\n            resolve();\r\n          }, 3000);\r\n        } catch (e) {\r\n          console.error(\"增强微信图片出错:\", e);\r\n          resolve();\r\n        }\r\n      });\r\n    },\r\n\r\n    // 强制重新加载内容区域，避免清空导致图片丢失\r\n    forceReloadContent() {\r\n      // 如果没有内容或不是微信文章，直接返回\r\n      if (!this.htmlJson || !this.isWeixinArticle) {\r\n        return;\r\n      }\r\n\r\n      console.log(\"开始增强图片显示处理，保留现有DOM结构\");\r\n\r\n      // 不再清空内容，直接在现有DOM上处理图片\r\n      this.$nextTick(() => {\r\n        // 先进行基础修复\r\n        this.fixWechatImages();\r\n\r\n        // 延迟执行彻底替换\r\n        setTimeout(() => {\r\n          this.replaceAllWechatImages().then(() => {\r\n            console.log(\"完成彻底替换微信图片\");\r\n            // 最后强制更新一次视图\r\n            this.$forceUpdate();\r\n          });\r\n        }, 100);\r\n      });\r\n    },\r\n\r\n    // 预处理微信图片\r\n    preloadWechatImages(content) {\r\n      return new Promise((resolve) => {\r\n        try {\r\n          // 先应用全局referrer策略\r\n          this.addNoReferrerMeta();\r\n\r\n          // 先修复内容中包含转义字符的图片标签\r\n          content = this.fixEscapedImageTags(content);\r\n\r\n          // 修复内容中的图片标签\r\n          content = this.handleWechatImages(content);\r\n\r\n          // 处理相对路径图片\r\n          content = this.handleRelativeImagePaths(content);\r\n\r\n          // 标记内容准备好了\r\n          this.contentReady = true;\r\n          resolve(content);\r\n\r\n          // 在内容加载后，下一个事件循环再应用修复\r\n          setTimeout(() => {\r\n            this.fixWechatImages();\r\n          }, 0);\r\n        } catch (e) {\r\n          console.error(\"预处理微信图片出错:\", e);\r\n          this.contentReady = true;\r\n          resolve(content);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 修复包含转义字符的图片标签 - 更彻底的处理\r\n    fixEscapedImageTags(content) {\r\n      try {\r\n        if (!content) return content;\r\n\r\n        // 先处理全局的转义字符，简化后续处理\r\n        content = content\r\n          .replace(/\\\\\"/g, '\"')\r\n          .replace(/\\\\'/g, \"'\")\r\n          .replace(/\\\\\\\\/g, \"\\\\\")\r\n          .replace(/\\\\&quot;/g, \"&quot;\")\r\n          .replace(/&amp;/g, \"&\");\r\n\r\n        // 扩展匹配模式，捕获所有可能的问题格式\r\n        const escapedTagRegex =\r\n          /<img[^>]*?(?:src|data-src)=[\"']?(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")([^\"']+?)(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")[\"']?[^>]*?>/gi;\r\n        const badStyleRegex =\r\n          /<img[^>]*?style=[\"'][^\"']*?(?:16px|white-space)[^\"']*?[\"'][^>]*?>/gi;\r\n        const brokenPathRegex =\r\n          /<img[^>]*?src=[\"'][^\"']*?mmbiz[^\"']*?[\"'][^>]*?>/gi;\r\n\r\n        // 合并所有匹配结果\r\n        const escapedTags = content.match(escapedTagRegex) || [];\r\n        const badStyleTags = content.match(badStyleRegex) || [];\r\n        const brokenPathTags = content.match(brokenPathRegex) || [];\r\n\r\n        // 去重 - 转换为Set然后再转回数组\r\n        const allTags = [\r\n          ...new Set([...escapedTags, ...badStyleTags, ...brokenPathTags]),\r\n        ];\r\n\r\n        if (allTags.length === 0) {\r\n          return content; // 没有找到需要修复的标签\r\n        }\r\n\r\n        console.log(`找到${allTags.length}个可能有问题的图片标签`);\r\n\r\n        // 处理每个问题标签\r\n        for (const tag of allTags) {\r\n          // 提取图片URL - 尝试多种模式\r\n          let imgUrl = \"\";\r\n\r\n          // 尝试匹配各种可能的src格式\r\n          const patterns = [\r\n            /src=[\"']?(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")?([^\"'<>\\s]+?mmbiz[^\"'<>\\s]+)(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")?[\"']?/i,\r\n            /data-src=[\"']?(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")?([^\"'<>\\s]+?mmbiz[^\"'<>\\s]+)(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")?[\"']?/i,\r\n            /original-src=[\"']?(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")?([^\"'<>\\s]+?mmbiz[^\"'<>\\s]+)(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")?[\"']?/i,\r\n          ];\r\n\r\n          // 尝试所有模式直到找到匹配项\r\n          for (const pattern of patterns) {\r\n            const match = tag.match(pattern);\r\n            if (match && match[1]) {\r\n              imgUrl = match[1];\r\n              break;\r\n            }\r\n          }\r\n\r\n          if (!imgUrl) {\r\n            // 如果仍然无法提取URL，跳过此标签\r\n            continue;\r\n          }\r\n\r\n          // 清理URL并添加必要的参数\r\n          imgUrl = imgUrl.replace(\r\n            /\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\"/g,\r\n            \"\"\r\n          );\r\n          if (!imgUrl.includes(\"wx_fmt=\") && imgUrl.includes(\"mmbiz\")) {\r\n            const separator = imgUrl.includes(\"?\") ? \"&\" : \"?\";\r\n            imgUrl += `${separator}wx_fmt=jpeg`;\r\n          }\r\n\r\n          // 处理可能存在的协议问题\r\n          if (!imgUrl.startsWith(\"http\")) {\r\n            if (imgUrl.startsWith(\"//\")) {\r\n              imgUrl = \"https:\" + imgUrl;\r\n            } else {\r\n              imgUrl = \"https://\" + imgUrl;\r\n            }\r\n          }\r\n\r\n          // 创建干净的替换标签 - 更简洁但完整\r\n          const newTag = `<img referrerpolicy=\"no-referrer\" class=\"wx-img\" src=\"${imgUrl}\" data-src=\"${imgUrl}\" data-original-src=\"${imgUrl}\" style=\"max-width:100%;height:auto!important;\" />`;\r\n\r\n          // 替换原始标签\r\n          content = content.replace(tag, newTag);\r\n        }\r\n\r\n        return content;\r\n      } catch (e) {\r\n        console.error(\"修复图片标签出错:\", e);\r\n        return content; // 出错时返回原始内容\r\n      }\r\n    },\r\n\r\n    // 动态修复图片方法 - 增强版，实现DOM重新渲染\r\n    fixWechatImages() {\r\n      try {\r\n        // 添加全局样式，确保所有微信图片都有no-referrer\r\n        const style = document.createElement(\"style\");\r\n        style.textContent = `\r\n          img[src*=\"mmbiz.qpic.cn\"], img[src*=\"mmbiz.qlogo.cn\"] {\r\n            max-width: 100% !important;\r\n            height: auto !important;\r\n            display: block !important;\r\n            margin: 10px auto !important;\r\n            object-fit: contain !important;\r\n            -webkit-referrer: no-referrer !important;\r\n            referrerpolicy: no-referrer !important;\r\n          }\r\n        `;\r\n        document.head.appendChild(style);\r\n\r\n        // 找到所有微信域名的图片\r\n        const wechatImages = document.querySelectorAll(\r\n          'img[src*=\"mmbiz.qpic.cn\"], img[src*=\"mmbiz.qlogo.cn\"], img[src*=\"mmsns.qpic.cn\"]'\r\n        );\r\n\r\n        if (wechatImages.length > 0) {\r\n          console.log(\r\n            `页面中找到${wechatImages.length}张微信图片，应用全局修复`\r\n          );\r\n\r\n          wechatImages.forEach((img) => {\r\n            // 添加必要的属性\r\n            img.setAttribute(\"referrerpolicy\", \"no-referrer\");\r\n            img.classList.add(\"wx-img\");\r\n\r\n            // 如果图片尚未进行错误处理，添加错误处理\r\n            if (!img.hasAttribute(\"data-error-handled\")) {\r\n              img.setAttribute(\"data-error-handled\", \"true\");\r\n\r\n              // 添加错误处理\r\n              img.onerror = function () {\r\n                console.log(\"图片加载失败，应用占位样式\");\r\n                this.style.border = \"1px dashed #ccc\";\r\n                this.style.padding = \"10px\";\r\n                this.style.width = \"auto\";\r\n                this.style.height = \"auto\";\r\n                this.style.minHeight = \"100px\";\r\n                this.alt = \"图片加载失败\";\r\n              };\r\n            }\r\n          });\r\n        }\r\n      } catch (e) {\r\n        console.error(\"修复微信图片出错:\", e);\r\n      }\r\n\r\n      // 返回一个Promise，方便外部检测完成状态\r\n      return Promise.resolve();\r\n    },\r\n\r\n    // 处理相对路径图片的方法\r\n    handleRelativeImagePaths(content) {\r\n      if (!content || !this.drawerInfo?.originalUrl) {\r\n        return content;\r\n      }\r\n\r\n      // 从原文链接中提取基础URL\r\n      const originalUrl = this.drawerInfo.originalUrl;\r\n      const urlParts = originalUrl.split(\"/\");\r\n      urlParts.pop(); // 移除文件名\r\n      const baseUrl = urlParts.join(\"/\") + \"/\";\r\n\r\n      // 处理所有相对路径图片\r\n      return content.replace(\r\n        /<img([^>]*?)src\\s*=\\s*[\"']([^\"']+)[\"']([^>]*?)>/gi,\r\n        (match, before, src, after) => {\r\n          // 跳过绝对路径\r\n          if (src.startsWith(\"http\") || src.startsWith(\"//\")) {\r\n            return match;\r\n          }\r\n\r\n          // 跳过非图片文件\r\n          if (!/\\.(png|jpg|jpeg|gif|webp|bmp|svg)(\\?.*)?$/i.test(src)) {\r\n            return match;\r\n          }\r\n\r\n          // 转换相对路径\r\n          let newSrc = \"\";\r\n          if (src.startsWith(\"./\")) {\r\n            newSrc = baseUrl + src.substring(2);\r\n          } else if (src.startsWith(\"/\")) {\r\n            const urlObj = new URL(originalUrl);\r\n            newSrc = urlObj.protocol + \"//\" + urlObj.host + src;\r\n          } else {\r\n            newSrc = baseUrl + src;\r\n          }\r\n\r\n          // 构建新的img标签，移除所有old*属性\r\n          let newTag = `<img${before} src=\"${newSrc}\"${after}>`;\r\n          newTag = newTag.replace(\r\n            /\\s*(oldsrc|oldSrc|old-src)\\s*=\\s*[\"'][^\"']*[\"']/gi,\r\n            \"\"\r\n          );\r\n\r\n          return newTag;\r\n        }\r\n      );\r\n    },\r\n\r\n    // 处理微信图片的公共方法\r\n    handleWechatImages(content) {\r\n      if (!content) return content;\r\n\r\n      try {\r\n        // 先处理转义字符问题\r\n        content = content\r\n          .replace(/\\\\\"/g, '\"')\r\n          .replace(/\\\\'/g, \"'\")\r\n          .replace(/\\\\\\\\/g, \"\\\\\");\r\n\r\n        // 将所有微信图片URL进行提取和替换\r\n        const regex =\r\n          /<img[^>]*?src=[\"'](https?:\\/\\/mmbiz\\.q(?:logo|pic)\\.cn\\/[^\"']+)[\"'][^>]*?>/gi;\r\n\r\n        // 简单直接的替换，确保每个微信图片都有正确属性\r\n        let newContent = content.replace(\r\n          regex,\r\n          '<img src=\"$1\" referrerpolicy=\"no-referrer\" class=\"wx-img\" style=\"max-width:100%;height:auto;\" />'\r\n        );\r\n\r\n        // 还需要处理已被转义的图片URL\r\n        const escapedRegex =\r\n          /<img[^>]*?src=[\"']?(\\\\?&quot;|\\\\?\"|&quot;|%22)(https?:\\/\\/mmbiz[^\"'&]+)(\\\\?&quot;|\\\\?\"|&quot;|%22)[\"']?[^>]*?>/gi;\r\n        newContent = newContent.replace(\r\n          escapedRegex,\r\n          '<img src=\"$2\" referrerpolicy=\"no-referrer\" class=\"wx-img\" style=\"max-width:100%;height:auto;\" />'\r\n        );\r\n\r\n        return newContent;\r\n      } catch (e) {\r\n        console.error(\"处理微信图片HTML出错:\", e);\r\n        return content;\r\n      }\r\n    },\r\n\r\n    async getIndexData() {\r\n      await API.recommendHot().then((response) => {\r\n        if (response.code == 200) {\r\n          this.articleList = response.data.slice(0, 5).map((item) => {\r\n            item.cnTitle = item.title;\r\n            item.id = item.articleId;\r\n            return item;\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    async details() {\r\n      let params;\r\n      if (this.$route.query.id) {\r\n        params = {\r\n          id: this.$route.query.id,\r\n        };\r\n      } else {\r\n        params = { articleSn: this.$route.query.articleSn };\r\n      }\r\n      // await API.AreaInfo(this.$route.query.id || this.$route.query.articleSn).then(async (res) => {\r\n      await API.articleDetail(params).then(async (res) => {\r\n        if (res.code == 200) {\r\n          this.hanldeBrowseAdd(res.data.id);\r\n          this.drawerInfo = res.data;\r\n          this.drawerInfo.sourceType != \"1\" && this.drawerInfo.sourceType != \"3\"\r\n            ? (this.translationBtnShow = true)\r\n            : (this.translationBtnShow = false);\r\n\r\n          // 如果是微信文章，先显示加载状态\r\n          if (\r\n            this.drawerInfo.originalUrl &&\r\n            this.drawerInfo.originalUrl.includes(\"https://mp.weixin.qq.com\")\r\n          ) {\r\n            this.contentReady = false;\r\n          } else {\r\n            this.contentReady = true;\r\n          }\r\n\r\n          // 预处理内容\r\n          const rawContent =\r\n            this.drawerInfo.cnContent || this.drawerInfo.content;\r\n          let processedContent = this.formattingJson(rawContent);\r\n\r\n          // 处理swdt数据\r\n          let keyPoints = \"\";\r\n          let entities = \"\";\r\n\r\n          if (this.drawerInfo.swdt && Array.isArray(this.drawerInfo.swdt)) {\r\n            this.drawerInfo.swdt.forEach((item) => {\r\n              if (item.swdtTaskid === \"1\" && item.swdtContent) {\r\n                // 内容要点\r\n                keyPoints = item.swdtContent;\r\n              } else if (item.swdtTaskid === \"2\" && item.swdtContent) {\r\n                // 人员/机构/技术/产品\r\n                entities = item.swdtContent;\r\n              }\r\n            });\r\n          }\r\n\r\n          // 预处理 markdown 内容，确保换行符正确处理\r\n          const preprocessMarkdown = (text) => {\r\n            if (!text) return \"\";\r\n            // 处理各种转义字符和格式问题\r\n            return text\r\n              .replace(/```markdown\\s*|```\\s*/g, \"\") // 移除 markdown 代码块标记\r\n              .replace(/```[a-zA-Z]*\\s*/g, \"\") // 移除带有语言标记的代码块标记\r\n              .replace(/\\\\n/g, \"\\n\") // 处理双反斜杠换行符\r\n              .replace(/\\\\\\n/g, \"\\n\") // 处理三反斜杠换行符\r\n              .replace(/\\r\\n/g, \"\\n\") // 处理 Windows 风格换行符\r\n              .replace(/\\$\\{[^}]+\\}/g, \"\"); // 移除模板字符串占位符\r\n          };\r\n\r\n          // 设置 markdown 内容，使用预处理函数\r\n          this.markdownContent.keyPoints = preprocessMarkdown(keyPoints);\r\n          this.markdownContent.entities = preprocessMarkdown(entities);\r\n\r\n          // 如果是微信公众号文章，进行预加载处理\r\n          if (\r\n            this.drawerInfo.originalUrl &&\r\n            this.drawerInfo.originalUrl.includes(\"https://mp.weixin.qq.com\")\r\n          ) {\r\n            try {\r\n              // 预加载微信图片 - 不会造成闪烁\r\n              processedContent = await this.preloadWechatImages(\r\n                processedContent\r\n              );\r\n              console.log(\"微信图片预处理完成\");\r\n            } catch (e) {\r\n              console.error(\"微信图片预处理失败:\", e);\r\n              // 出错时，显示内容\r\n              this.contentReady = true;\r\n            }\r\n          }\r\n\r\n          // 设置处理后的内容\r\n          this.$set(this, \"htmlJson\", processedContent);\r\n\r\n          if (this.drawerInfo.title == this.drawerInfo.cnTitle) {\r\n            this.titleShow = false;\r\n          }\r\n\r\n          document.title = this.drawerInfo.cnTitle || this.drawerInfo.title;\r\n          this.loading = false;\r\n\r\n          // 内容显示后，应用全局修复\r\n          if (this.isWeixinArticle) {\r\n            this.$nextTick(() => {\r\n              // 应用图片修复\r\n              this.fixWechatImages();\r\n\r\n              // 2秒后再检查一次\r\n              setTimeout(() => {\r\n                this.fixWechatImages();\r\n              }, 2000);\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    formattingJson(content) {\r\n      if (content) {\r\n        if (containsHtmlTags(content)) {\r\n          content = content.replace(/<br>/g, \"\");\r\n          content = content.replace(/\\n/g, \"\");\r\n          content = content.replace(/\\\\n/g, \"\");\r\n          content = content.replace(/\\\\\\n/g, \"\");\r\n          content = content.replace(\"|xa0\", \"\");\r\n          content = content.replace(\"opacity: 0\", \"\");\r\n\r\n          // 处理微信公众号图片防盗链问题\r\n          if (\r\n            this.drawerInfo.originalUrl &&\r\n            this.drawerInfo.originalUrl.includes(\"https://mp.weixin.qq.com\")\r\n          ) {\r\n            content = this.handleWechatImages(content);\r\n          }\r\n\r\n          // 处理相对路径图片\r\n          content = this.handleRelativeImagePaths(content);\r\n\r\n          console.log(\"包含的HTML标签\", extractHtmlTags(content));\r\n          console.log(\"HTML是否结构正确\", hasValidHtmlStructure(content));\r\n        } else {\r\n          content = content.replace(/\\n/g, \"<br>\");\r\n          content = content.replace(/\\\\n/g, \"<br>\");\r\n          content = content.replace(/\\\\\\n/g, \"<br>\");\r\n          content = content.replace(/\\${[^}]+}/g, \"<br>\");\r\n          content = content.replace(\"|xa0\", \"\");\r\n          content = content.replace(\"opacity: 0\", \"\");\r\n\r\n          // 处理微信公众号图片防盗链问题\r\n          if (\r\n            this.drawerInfo.originalUrl &&\r\n            this.drawerInfo.originalUrl.includes(\"https://mp.weixin.qq.com\")\r\n          ) {\r\n            content = this.handleWechatImages(content);\r\n          }\r\n\r\n          // 处理相对路径图片\r\n          content = this.handleRelativeImagePaths(content);\r\n        }\r\n      }\r\n      return content;\r\n    },\r\n    // 翻译文章\r\n    translateEvent(row) {\r\n      const fun = () => {\r\n        const loading = this.$loading({\r\n          lock: true,\r\n          text: \"Loading\",\r\n          spinner: \"el-icon-loading\",\r\n          background: \"rgba(0, 0, 0, 0.7)\",\r\n        });\r\n        API.translationTitle({\r\n          originalText: row.content,\r\n          docId: this.$route.query.id,\r\n          id: row.id,\r\n          translationField: \"content\",\r\n          translationType: 1,\r\n        })\r\n          .then((res) => {\r\n            this.drawerInfo.cnContent = res.data;\r\n            this.$set(\r\n              this,\r\n              \"htmlJson\",\r\n              this.formattingJson(\r\n                this.drawerInfo.cnContent || this.drawerInfo.content\r\n              )\r\n            );\r\n            this.originalArticleShow = true;\r\n            loading.close();\r\n          })\r\n          .catch((err) => {\r\n            loading.close();\r\n          });\r\n      };\r\n\r\n      if (row.cnContent) {\r\n        // 提示是否确认再次翻译\r\n        this.$confirm(\"是否确认再次翻译?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        })\r\n          .then(() => {\r\n            fun();\r\n          })\r\n          .catch(() => {\r\n            this.$set(\r\n              this,\r\n              \"htmlJson\",\r\n              this.formattingJson(\r\n                this.drawerInfo.cnContent || this.drawerInfo.content\r\n              )\r\n            );\r\n            this.originalArticleShow = true;\r\n          });\r\n      } else {\r\n        fun();\r\n      }\r\n    },\r\n    viewOriginal() {\r\n      if (this.originalArticleShow) {\r\n        this.$set(\r\n          this,\r\n          \"htmlJson\",\r\n          this.formattingJson(this.drawerInfo.content)\r\n        );\r\n      } else {\r\n        this.$set(\r\n          this,\r\n          \"htmlJson\",\r\n          this.formattingJson(\r\n            this.drawerInfo.cnContent || this.drawerInfo.content\r\n          )\r\n        );\r\n      }\r\n      this.originalArticleShow = !this.originalArticleShow;\r\n    },\r\n    backToTop() {\r\n      window.scrollTo({ top: 0, behavior: \"smooth\" });\r\n    },\r\n    close() {\r\n      this.type = true;\r\n      this.textarea = \"\";\r\n    },\r\n    submit() {\r\n      feedbackAdd({\r\n        articleId: this.$route.query.id,\r\n        docId: this.$route.query.id,\r\n        cnTitle: this.drawerInfo.cnTitle || this.drawerInfo.title,\r\n        content: this.textarea,\r\n      }).then((res) => {\r\n        if (res.code == 200) {\r\n          this.$message({\r\n            message: \"留言成功\",\r\n            type: \"success\",\r\n          });\r\n          this.close();\r\n        }\r\n      });\r\n    },\r\n    openNewView(item) {\r\n      window.open(`/expressDetails?id=${item.id}&docId=${item.id}`, \"_blank\");\r\n    },\r\n    handleCollections(item) {\r\n      if (!item.collection) {\r\n        API.collectApi([item.id]).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$message({\r\n              message: \"收藏成功,请前往个人中心查看\",\r\n              type: \"success\",\r\n            });\r\n            this.$set(this.drawerInfo, \"collection\", !item.collection);\r\n          } else {\r\n            this.$message({ message: \"收藏失败\", type: \"info\" });\r\n          }\r\n        });\r\n      } else {\r\n        API.cocelCollect([item.id]).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$message({ message: \"已取消收藏\", type: \"success\" });\r\n            this.$set(this.drawerInfo, \"collection\", !item.collection);\r\n          } else {\r\n            this.$message({ message: \"取消收藏失败\", type: \"info\" });\r\n          }\r\n        });\r\n      }\r\n    },\r\n    handleRecommend(item) {\r\n      let query = new FormData();\r\n      query.append(\"articleId\", item.id);\r\n      if (!item.recommend) {\r\n        API.recommendAdd(query).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$message({\r\n              message: \"推荐成功,请前往个人中心查看\",\r\n              type: \"success\",\r\n            });\r\n            this.$set(this.drawerInfo, \"recommend\", !item.recommend);\r\n          } else {\r\n            this.$message({ message: \"推荐失败\", type: \"info\" });\r\n          }\r\n        });\r\n      } else {\r\n        API.recommendCancel(query).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$message({ message: \"已取消推荐\", type: \"success\" });\r\n            this.$set(this.drawerInfo, \"recommend\", !item.recommend);\r\n          } else {\r\n            this.$message({ message: \"取消推荐失败\", type: \"info\" });\r\n          }\r\n        });\r\n      }\r\n    },\r\n    hanldeBrowseAdd(id) {\r\n      let query = new FormData();\r\n      query.append(\"articleId\", id);\r\n      API.browseAdd(query);\r\n    },\r\n    viewOriginalArticle(item) {\r\n      window.open(item.originalUrl);\r\n    },\r\n    // 打开思维导图弹窗\r\n    openMarkmap(type) {\r\n      if (!this.markdownContent[type]) {\r\n        this.$message.warning(\"暂无思维导图数据\");\r\n        return;\r\n      }\r\n\r\n      // 设置标题和内容\r\n      let title = \"\";\r\n      if (type === \"keyPoints\") {\r\n        title = \"内容要点思维导图\";\r\n      } else if (type === \"entities\") {\r\n        title = \"人员/机构/技术/产品思维导图\";\r\n      }\r\n\r\n      // 设置内容\r\n      const content = this.markdownContent[type];\r\n\r\n      // 使用localStorage存储大型内容，而不是通过URL传递\r\n      const storageKey = `markmap_data_${Date.now()}`;\r\n      localStorage.setItem(storageKey, content);\r\n\r\n      // 构建URL参数 - 只传递标题和存储键，不传递大型内容\r\n      const params = new URLSearchParams();\r\n      params.append(\"title\", title);\r\n      params.append(\r\n        \"articleTitle\",\r\n        this.drawerInfo.cnTitle || this.drawerInfo.title\r\n      );\r\n      params.append(\"storageKey\", storageKey);\r\n\r\n      // 获取屏幕尺寸，制定合适的窗口大小\r\n      const screenWidth = window.screen.width;\r\n      const screenHeight = window.screen.height;\r\n\r\n      // 窗口宽高为屏幕的80%，并居中显示\r\n      const width = Math.round(screenWidth * 0.8);\r\n      const height = Math.round(screenHeight * 0.8);\r\n      const left = Math.round((screenWidth - width) / 2);\r\n      const top = Math.round((screenHeight - height) / 2);\r\n\r\n      // 尝试打开新窗口\r\n      const windowFeatures = `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes,status=yes`;\r\n      const newWindow = window.open(\r\n        `/markmap?${params.toString()}`,\r\n        \"_blank\",\r\n        windowFeatures\r\n      );\r\n\r\n      // 检查是否成功打开窗口，如果被浏览器阻止则打开新标签页\r\n      if (\r\n        !newWindow ||\r\n        newWindow.closed ||\r\n        typeof newWindow.closed === \"undefined\"\r\n      ) {\r\n        // 浏览器可能阻止了弹窗，使用新标签页\r\n        window.open(`/markmap?${params.toString()}`, \"_blank\");\r\n        this.$message.info(\"新窗口被浏览器阻止，已在新标签页打开\");\r\n      }\r\n    },\r\n    // 关闭思维导图弹窗 - 保留以防需要\r\n    handleMarkmapClose() {\r\n      this.markmapVisible = false;\r\n      this.markmapContent = \"\";\r\n    },\r\n    // 处理发布时间的显示\r\n    formatPublishTime(publishTime, publishLocaltime) {\r\n      // 格式化publishTime为年月日\r\n      const formattedPublishTime = this.parseTime(publishTime, \"{y}-{m}-{d}\");\r\n\r\n      // 如果webstePublishTime不存在，直接返回publishTime\r\n      if (!publishLocaltime) {\r\n        return \"[北京]\" + formattedPublishTime;\r\n      }\r\n\r\n      let formattedWebsteTime = \"\";\r\n      // 处理不同格式的webstePublishTime\r\n      if (publishLocaltime) {\r\n        formattedWebsteTime = this.parseTime(publishLocaltime, \"{y}-{m}-{d}\");\r\n      }\r\n\r\n      // 比较年月日是否相同\r\n      if (formattedPublishTime === formattedWebsteTime) {\r\n        return \"[北京]\" + formattedPublishTime;\r\n      } else {\r\n        return `[当地]${formattedWebsteTime} / [北京]${formattedPublishTime}`;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n/* 全局样式 */\r\n::v-deep img[src*=\"mmbiz\"] {\r\n  max-width: 100% !important;\r\n  height: auto !important;\r\n  object-fit: contain !important;\r\n  margin: 10px auto !important;\r\n  display: block !important;\r\n  -webkit-referrer: no-referrer !important;\r\n  referrerpolicy: no-referrer !important;\r\n}\r\n\r\n.drawer_box {\r\n  display: flex;\r\n  user-select: text !important;\r\n  background: #f5f7fa;\r\n  min-height: 100vh;\r\n  justify-content: center;\r\n\r\n  .drawer_Style {\r\n    position: relative;\r\n    z-index: 2;\r\n    margin: 0px 20px 0px;\r\n    width: 800px;\r\n    background: #ffffff;\r\n    padding: 10px 30px;\r\n\r\n    .title {\r\n      font-size: 22px;\r\n      font-weight: 500px;\r\n      // text-align: center;\r\n    }\r\n\r\n    .source {\r\n      color: #0798f8;\r\n      // text-align: center;\r\n      font-size: 14px;\r\n    }\r\n\r\n    .time {\r\n      font-size: 14px;\r\n      // text-align: center;\r\n      margin-left: 10px;\r\n      color: #9b9b9b;\r\n    }\r\n\r\n    .author {\r\n      color: #1a0997;\r\n      // text-align: center;\r\n      margin-left: 10px;\r\n      font-size: 14px;\r\n    }\r\n\r\n    .summary {\r\n      background-color: #f5f7fa;\r\n      padding: 10px;\r\n      border-radius: 8px;\r\n      margin-bottom: 20px;\r\n    }\r\n    .summary .summary-item1 {\r\n      color: #971231;\r\n      font-weight: 600;\r\n      margin-bottom: 10px;\r\n    }\r\n    .summary .summary-item2 {\r\n      line-height: 30px;\r\n      word-break: break-word;\r\n    }\r\n\r\n    /* 文本展开折叠组件样式调整 */\r\n    .summary ::v-deep .text-ellipsis {\r\n      width: 100%;\r\n    }\r\n\r\n    .summary ::v-deep .text-content {\r\n      line-height: 30px;\r\n      font-size: 16px;\r\n      word-break: break-word;\r\n    }\r\n\r\n    .summary ::v-deep .text-limited {\r\n      -webkit-line-clamp: 10 !important;\r\n    }\r\n\r\n    .summary ::v-deep .ellipsis-actions {\r\n      text-align: right;\r\n      // margin-top: 8px;\r\n    }\r\n\r\n    .summary ::v-deep .show-more,\r\n    .summary ::v-deep .show-less {\r\n      color: #0798f8;\r\n      font-size: 14px;\r\n      transition: color 0.3s ease;\r\n    }\r\n\r\n    .summary ::v-deep .show-more:hover,\r\n    .summary ::v-deep .show-less:hover {\r\n      color: #0071ce;\r\n    }\r\n\r\n    .summary ::v-deep .show-more i,\r\n    .summary ::v-deep .show-less i {\r\n      margin-left: 3px;\r\n      font-size: 12px;\r\n    }\r\n  }\r\n}\r\n\r\n.right {\r\n  width: 500px;\r\n\r\n  .sisterDetails {\r\n    background-color: #ffffff;\r\n    padding: 10px 20px;\r\n    width: 100%;\r\n    color: #000000;\r\n\r\n    .media-info {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      .media-info-item {\r\n        flex: 1;\r\n      }\r\n    }\r\n\r\n    .name {\r\n      color: #9b9b9b;\r\n      line-height: 1.8;\r\n      font-size: 14px;\r\n\r\n      .value {\r\n        margin-left: 15px;\r\n        color: #000000;\r\n      }\r\n    }\r\n  }\r\n\r\n  .markdown {\r\n    margin-top: 20px;\r\n    background-color: #f2e3c5;\r\n    padding: 10px 20px;\r\n    width: 100%;\r\n    color: #000000;\r\n    height: 810px;\r\n\r\n    ::v-deep .el-divider {\r\n      background-color: #838484;\r\n      margin: 8px 0;\r\n    }\r\n\r\n    .markdown-content {\r\n      margin-bottom: 10px;\r\n      .markdown-content-title {\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n        margin-bottom: 10px;\r\n      }\r\n      .markdown-content-text {\r\n        height: 325px;\r\n        overflow-y: auto;\r\n        padding: 10px;\r\n        line-height: 1.6;\r\n        font-size: 16px;\r\n        color: #333333;\r\n        border-radius: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .recommendedArticle {\r\n    margin-top: 20px;\r\n    background-color: #ffffff;\r\n    padding: 10px 20px;\r\n    width: 100%;\r\n    color: #000000;\r\n\r\n    .articleBox {\r\n      font-size: 14px;\r\n      margin: 10px 0;\r\n\r\n      .article {\r\n        margin-bottom: 5px;\r\n        cursor: pointer;\r\n        position: relative;\r\n        // width: calc(100% - 75px);\r\n        line-height: 1.8;\r\n\r\n        &:before {\r\n          content: \"•\";\r\n          color: red;\r\n          font-weight: bold;\r\n        }\r\n      }\r\n\r\n      .bottom {\r\n        display: flex;\r\n        color: #9b9b9b;\r\n        align-items: baseline;\r\n\r\n        .sourceName {\r\n          margin-left: 10px;\r\n        }\r\n\r\n        .count {\r\n          margin-left: 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .keyword {\r\n    margin-top: 20px;\r\n    background-color: #ffffff;\r\n    padding: 10px 20px;\r\n    width: 100%;\r\n    color: #000000;\r\n    height: 350px;\r\n\r\n    .chartBox {\r\n      height: 300px;\r\n    }\r\n  }\r\n\r\n  .articleEntity {\r\n    margin-top: 20px;\r\n    background-color: #ffffff;\r\n    padding: 10px 20px;\r\n    width: 100%;\r\n    color: #000000;\r\n    height: 350px;\r\n\r\n    .chartBox {\r\n      height: 300px;\r\n      position: relative;\r\n    }\r\n  }\r\n}\r\n\r\n.right_title {\r\n  font-size: 20px;\r\n}\r\n\r\n.el-divider-right {\r\n  margin: 8px 0;\r\n}\r\n\r\n.liuyanBox {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: calc(50vw - 653px);\r\n  width: 800px;\r\n  z-index: 99;\r\n  background-color: #e3e3ef;\r\n  padding: 5px 10px;\r\n  border-radius: 5px;\r\n\r\n  .morenzhuangtai {\r\n    height: 40px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .shuruzhuangtai {\r\n    height: 90px;\r\n\r\n    .top {\r\n      height: 40px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n    }\r\n\r\n    .bottom {\r\n      height: 50px;\r\n    }\r\n  }\r\n}\r\n\r\n.uesr {\r\n  display: flex;\r\n  align-items: center;\r\n  height: 30px;\r\n\r\n  .avatar {\r\n    cursor: pointer;\r\n    width: 30px;\r\n    height: 30px;\r\n    border-radius: 50%;\r\n  }\r\n\r\n  .name {\r\n    margin-left: 10px;\r\n  }\r\n}\r\n\r\n.tabs-all {\r\n  position: fixed;\r\n  left: calc(50vw - 693px);\r\n  top: 0px;\r\n  z-index: 99;\r\n  height: 800px;\r\n  width: 40px;\r\n\r\n  @media screen and (max-width: 1400px) {\r\n    left: 0;\r\n  }\r\n\r\n  .tabs {\r\n    writing-mode: vertical-rl;\r\n    /* 文字从上到下，从右到左 */\r\n    height: 120px;\r\n    width: 40px;\r\n    font-weight: 800;\r\n    font-size: 16px;\r\n    color: #ffffff;\r\n    line-height: 35px;\r\n    text-align: center;\r\n    font-style: normal;\r\n    background: url(\"../../assets/bigScreenTwo/tab-active.png\") no-repeat 0px\r\n      0px !important;\r\n    background-size: 100% 100% !important;\r\n    letter-spacing: 2px;\r\n    margin-bottom: 10px;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.content-loading {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 100px;\r\n  width: 100%;\r\n  margin: 20px 0;\r\n}\r\n\r\n.loading-progress {\r\n  width: 80%;\r\n  max-width: 500px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.loading-text {\r\n  text-align: center;\r\n  margin-bottom: 10px;\r\n  color: #409eff;\r\n  font-size: 14px;\r\n}\r\n\r\n.preloading-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 300px;\r\n  width: 100%;\r\n}\r\n\r\n::v-deep img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: contain;\r\n}\r\n\r\n::v-deep video {\r\n  width: 100%;\r\n}\r\n\r\n/* 添加微信图片特殊样式 */\r\n::v-deep .wx-img {\r\n  max-width: 100%;\r\n  height: auto !important;\r\n  display: block;\r\n  margin: 10px auto;\r\n}\r\n\r\n/* 替换后的图片样式 */\r\n::v-deep .wx-img-replaced {\r\n  max-width: 100%;\r\n  height: auto !important;\r\n  display: block;\r\n  margin: 10px auto;\r\n  border: 1px solid transparent;\r\n}\r\n\r\n/* Canvas创建的图片样式 */\r\n::v-deep .wx-img-canvas {\r\n  max-width: 100%;\r\n  height: auto !important;\r\n  display: block;\r\n  margin: 10px auto;\r\n  border: 1px solid transparent;\r\n}\r\n\r\n/* 占位图样式 */\r\n::v-deep .wx-img-placeholder {\r\n  max-width: 100%;\r\n  min-height: 100px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 10px auto;\r\n  border: 1px dashed #ccc;\r\n  padding: 20px;\r\n  font-size: 12px;\r\n  color: #999;\r\n  text-align: center;\r\n}\r\n\r\n/* markdown显示样式 */\r\n::v-deep .md-h1,\r\n::v-deep h1.md-h1 {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin: 12px 0 8px 0;\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .md-h2,\r\n::v-deep h2.md-h2 {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin: 10px 0 6px 0;\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .md-h3,\r\n::v-deep h3.md-h3 {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin: 12px 0 6px 0;\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .md-li {\r\n  margin: 5px 0;\r\n  padding-left: 10px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n::v-deep .md-bullet,\r\n::v-deep .md-number {\r\n  margin-right: 8px;\r\n  color: #333333;\r\n  font-weight: bold;\r\n  flex-shrink: 0;\r\n}\r\n\r\n::v-deep .md-blockquote,\r\n::v-deep blockquote.md-blockquote {\r\n  border-left: 4px solid #d0d0d0;\r\n  padding: 5px 10px;\r\n  margin: 10px 0;\r\n  background-color: #f9f9f9;\r\n  font-style: italic;\r\n  color: #555;\r\n}\r\n\r\n::v-deep .markdown-content-text strong {\r\n  font-weight: bold;\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .markdown-content-text em {\r\n  font-style: italic;\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .md-code-block,\r\n::v-deep pre.md-code-block {\r\n  background-color: #f6f8fa;\r\n  border-radius: 3px;\r\n  padding: 10px;\r\n  margin: 10px 0;\r\n  font-family: monospace;\r\n  overflow-x: auto;\r\n  white-space: pre;\r\n  font-size: 13px;\r\n}\r\n\r\n::v-deep .md-code-inline,\r\n::v-deep code.md-code-inline {\r\n  background-color: #f6f8fa;\r\n  border-radius: 3px;\r\n  padding: 2px 4px;\r\n  font-family: monospace;\r\n  font-size: 13px;\r\n}\r\n\r\n/* 添加列表和段落样式 */\r\n::v-deep .md-paragraph {\r\n  margin: 5px 0;\r\n  line-height: 1.6;\r\n}\r\n\r\n::v-deep .md-paragraph-space {\r\n  height: 6px; /* 仅在需要的情况下调整为更小的值 */\r\n}\r\n\r\n::v-deep .md-ul,\r\n::v-deep .md-ol {\r\n  margin: 10px 0;\r\n  padding-left: 25px;\r\n}\r\n\r\n::v-deep .md-ul {\r\n  list-style-type: disc;\r\n}\r\n\r\n::v-deep .md-ul li {\r\n  margin: 5px 0;\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .md-ul li::marker {\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .md-ol {\r\n  list-style-type: decimal;\r\n}\r\n\r\n::v-deep .md-ol li {\r\n  margin: 5px 0;\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .md-ol li::marker {\r\n  color: #333333;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 添加滚动条样式： */\r\n/* 自定义滚动条样式 */\r\n::v-deep .markdown-content-text::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n::v-deep .markdown-content-text::-webkit-scrollbar-track {\r\n  background: transparent; /* 去掉滚动条背景色 */\r\n}\r\n\r\n::v-deep .markdown-content-text::-webkit-scrollbar-thumb {\r\n  background-color: #ffffff; /* 中等灰色 */\r\n  border-radius: 3px;\r\n}\r\n\r\n::v-deep .markdown-content-text::-webkit-scrollbar-thumb:hover {\r\n  background-color: #f7f7f7; /* 悬停时的颜色 */\r\n}\r\n\r\n/* 点击看大图按钮样式 */\r\n.view-markmap {\r\n  display: inline-block;\r\n  margin-left: 10px;\r\n  font-size: 14px;\r\n  color: #1890ff;\r\n  cursor: pointer;\r\n  background-color: rgba(24, 144, 255, 0.1);\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.view-markmap:hover {\r\n  background-color: rgba(24, 144, 255, 0.2);\r\n  text-decoration: underline;\r\n}\r\n\r\n.markdown-content-title {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n::v-deep pre {\r\n  white-space: normal;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiRA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,aAAA,GAAAH,OAAA;AACA,IAAAI,UAAA,GAAAJ,OAAA;AAKA,IAAAK,cAAA,GAAAL,OAAA;AACA,IAAAM,cAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,OAAA,GAAAL,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAQ,KAAA;EACAC,UAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,YAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,YAAA;MACAC,kBAAA;MACAC,SAAA;MACAC,UAAA;MACAC,QAAA;MACAC,aAAA;MACAC,mBAAA;MACAC,WAAA;MACAC,IAAA;MACAC,QAAA;MACAC,GAAA;MACAC,WAAA;MACAC,WAAA;MACAC,YAAA;MACAC,gBAAA;MACAC,eAAA;QACAC,SAAA;QACAC,QAAA;MACA;MACAC,cAAA;MACAC,cAAA;MACAC,YAAA;MACAC,cAAA;IACA;EACA;EACAC,QAAA,MAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,IAAAC,gBAAA;IACAC,eAAA,WAAAA,gBAAA;MACA,OACA,KAAAvB,UAAA,CAAAwB,WAAA,IACA,KAAAxB,UAAA,CAAAwB,WAAA,CAAAC,QAAA;IAEA;IACAC,kBAAA,WAAAA,mBAAA;MACA,WAAAC,6BAAA,OAAAf,eAAA,CAAAC,SAAA;IACA;IACAe,iBAAA,WAAAA,kBAAA;MACA,WAAAD,6BAAA,OAAAf,eAAA,CAAAE,QAAA;IACA;EAAA,EACA;EACAe,OAAA,WAAAA,QAAA;IACA,KAAAjC,OAAA;IACA,KAAAE,kBAAA;;IAEA;IACA,KAAAgC,iBAAA;IAEA,KAAAC,OAAA;IACA,KAAAC,YAAA;IACA;EACA;EACAC,KAAA;IACAzB,WAAA,WAAAA,YAAA0B,MAAA,EAAAC,MAAA;MACAC,OAAA,CAAAC,GAAA,yOAAAC,MAAA,CAAAH,MAAA,UAAAG,MAAA,CAAAJ,MAAA;IACA;EACA;EACAK,OAAA;IACA;IACAT,iBAAA,WAAAA,kBAAA;MACA;MACA,IACAU,QAAA,CAAAC,aAAA,kDACA;QACA;MACA;;MAEA;MACA,IAAAC,IAAA,GAAAF,QAAA,CAAAG,aAAA;MACAD,IAAA,CAAAE,IAAA;MACAF,IAAA,CAAAG,OAAA;MACAL,QAAA,CAAAM,IAAA,CAAAC,WAAA,CAAAL,IAAA;MACAN,OAAA,CAAAC,GAAA;IACA;IAEA;IACAW,gBAAA,WAAAA,iBAAAH,OAAA;MACA;MACA,KAAAA,OAAA,SAAAA,OAAA;;MAEA;MACA,OAAAA,OAAA,CAAAI,OAAA,CACA,gFACA,UAAAC,KAAA,EAAAC,QAAA;QACA;QACA,IAAAA,QAAA,CAAA1B,QAAA;UACA,OAAAyB,KAAA;QACA;QACA;QACA,cAAAZ,MAAA,CAAAa,QAAA;MACA,CACA;IACA;IAEA;IACAC,sBAAA,WAAAA,uBAAA;MACA;MACA,UAAA7B,eAAA;QACA,OAAA8B,OAAA,CAAAC,OAAA;MACA;MAEA,WAAAD,OAAA,WAAAC,OAAA;QACA;UACA;UACA,IAAAC,YAAA,GAAAf,QAAA,CAAAgB,gBAAA,CACA,0GACA;UAEA,IAAAD,YAAA,CAAAE,MAAA;YACAH,OAAA;YACA;UACA;UAEAlB,OAAA,CAAAC,GAAA,uOAAAC,MAAA,CAAAiB,YAAA,CAAAE,MAAA;UACA,IAAAC,cAAA;;UAEA;UACAH,YAAA,CAAAI,OAAA,WAAAC,GAAA,EAAAC,KAAA;YACA;YACA,IACAD,GAAA,CAAAE,YAAA,wBACAF,GAAA,CAAAG,QAAA,IACAH,GAAA,CAAAI,YAAA,MACA;cACAN,cAAA;cACA,IAAAA,cAAA,IAAAH,YAAA,CAAAE,MAAA;gBACAH,OAAA;cACA;cACA;YACA;;YAEA;YACA,IAAAW,aAAA,GAAAL,GAAA,CAAAM,KAAA,CAAAC,KAAA,IAAAP,GAAA,CAAAO,KAAA;YACA,IAAAC,cAAA,GAAAR,GAAA,CAAAM,KAAA,CAAAG,MAAA,IAAAT,GAAA,CAAAS,MAAA;;YAEA;YACA,KAAAT,GAAA,CAAAG,QAAA,IAAAH,GAAA,CAAAI,YAAA;cACA;cACA,IAAAM,WAAA,GACAV,GAAA,CAAAW,YAAA,yBACAX,GAAA,CAAAW,YAAA;cACA,KAAAD,WAAA;gBACA;gBACAZ,cAAA;gBACA,IAAAA,cAAA,IAAAH,YAAA,CAAAE,MAAA;kBACAH,OAAA;gBACA;gBACA;cACA;;cAEA;cACAM,GAAA,CAAAY,YAAA;cACAZ,GAAA,CAAAa,SAAA,CAAAC,GAAA;cACAd,GAAA,CAAAM,KAAA,CAAAS,QAAA;cACAf,GAAA,CAAAM,KAAA,CAAAG,MAAA;;cAEA;cACAT,GAAA,CAAAY,YAAA;;cAEA;cACA,IAAAF,WAAA,CAAAM,UAAA;gBACA,IAAAC,QAAA,GAAAP,WAAA,CAAArB,OAAA;gBACAb,OAAA,CAAAC,GAAA,0PAAAC,MAAA,CAAAuC,QAAA;gBACAjB,GAAA,CAAAkB,GAAA,GAAAD,QAAA;cACA;cACA;cAAA,KACA,KAAAP,WAAA,CAAA7C,QAAA;gBACA,IAAAsD,SAAA,GAAAT,WAAA,CAAA7C,QAAA;gBACA,IAAAuD,aAAA,MAAA1C,MAAA,CAAAgC,WAAA,EAAAhC,MAAA,CAAAyC,SAAA;gBACA3C,OAAA,CAAAC,GAAA,iQAAAC,MAAA,CAAA0C,aAAA;gBACApB,GAAA,CAAAkB,GAAA,GAAAE,aAAA;cACA;YACA;;YAEA;YACAtB,cAAA;YACA,IAAAA,cAAA,IAAAH,YAAA,CAAAE,MAAA;cACAH,OAAA;YACA;UACA;;UAEA;UACA2B,UAAA;YACA3B,OAAA;UACA;QACA,SAAA4B,CAAA;UACA9C,OAAA,CAAA+C,KAAA,cAAAD,CAAA;UACA5B,OAAA;QACA;MACA;IACA;IAEA;IACA8B,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,KAAA;MACA;MACA,UAAApF,QAAA,UAAAsB,eAAA;QACA;MACA;MAEAa,OAAA,CAAAC,GAAA;;MAEA;MACA,KAAAiD,SAAA;QACA;QACAD,KAAA,CAAAE,eAAA;;QAEA;QACAN,UAAA;UACAI,KAAA,CAAAjC,sBAAA,GAAAoC,IAAA;YACApD,OAAA,CAAAC,GAAA;YACA;YACAgD,KAAA,CAAAI,YAAA;UACA;QACA;MACA;IACA;IAEA;IACAC,mBAAA,WAAAA,oBAAA7C,OAAA;MAAA,IAAA8C,MAAA;MACA,WAAAtC,OAAA,WAAAC,OAAA;QACA;UACA;UACAqC,MAAA,CAAA7D,iBAAA;;UAEA;UACAe,OAAA,GAAA8C,MAAA,CAAAC,mBAAA,CAAA/C,OAAA;;UAEA;UACAA,OAAA,GAAA8C,MAAA,CAAAE,kBAAA,CAAAhD,OAAA;;UAEA;UACAA,OAAA,GAAA8C,MAAA,CAAAG,wBAAA,CAAAjD,OAAA;;UAEA;UACA8C,MAAA,CAAA9F,YAAA;UACAyD,OAAA,CAAAT,OAAA;;UAEA;UACAoC,UAAA;YACAU,MAAA,CAAAJ,eAAA;UACA;QACA,SAAAL,CAAA;UACA9C,OAAA,CAAA+C,KAAA,eAAAD,CAAA;UACAS,MAAA,CAAA9F,YAAA;UACAyD,OAAA,CAAAT,OAAA;QACA;MACA;IACA;IAEA;IACA+C,mBAAA,WAAAA,oBAAA/C,OAAA;MACA;QACA,KAAAA,OAAA,SAAAA,OAAA;;QAEA;QACAA,OAAA,GAAAA,OAAA,CACAI,OAAA,cACAA,OAAA,cACAA,OAAA,gBACAA,OAAA,wBACAA,OAAA;;QAEA;QACA,IAAA8C,eAAA,GACA;QACA,IAAAC,aAAA,GACA;QACA,IAAAC,eAAA,GACA;;QAEA;QACA,IAAAC,WAAA,GAAArD,OAAA,CAAAK,KAAA,CAAA6C,eAAA;QACA,IAAAI,YAAA,GAAAtD,OAAA,CAAAK,KAAA,CAAA8C,aAAA;QACA,IAAAI,cAAA,GAAAvD,OAAA,CAAAK,KAAA,CAAA+C,eAAA;;QAEA;QACA,IAAAI,OAAA,OAAAC,mBAAA,CAAAjF,OAAA,EACA,IAAAkF,GAAA,IAAAjE,MAAA,KAAAgE,mBAAA,CAAAjF,OAAA,EAAA6E,WAAA,OAAAI,mBAAA,CAAAjF,OAAA,EAAA8E,YAAA,OAAAG,mBAAA,CAAAjF,OAAA,EAAA+E,cAAA,IACA;QAEA,IAAAC,OAAA,CAAA5C,MAAA;UACA,OAAAZ,OAAA;QACA;QAEAT,OAAA,CAAAC,GAAA,2NAAAC,MAAA,CAAA+D,OAAA,CAAA5C,MAAA;;QAEA;QAAA,IAAA+C,SAAA,OAAAC,2BAAA,CAAApF,OAAA,EACAgF,OAAA;UAAAK,KAAA;QAAA;UAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;YAAA,IAAAC,GAAA,GAAAJ,KAAA,CAAAK,KAAA;YACA;YACA,IAAAC,MAAA;;YAEA;YACA,IAAAC,QAAA,IACA,yIACA,8IACA,iJACA;;YAEA;YACA,SAAAC,EAAA,MAAAC,SAAA,GAAAF,QAAA,EAAAC,EAAA,GAAAC,SAAA,CAAA1D,MAAA,EAAAyD,EAAA;cAAA,IAAAE,OAAA,GAAAD,SAAA,CAAAD,EAAA;cACA,IAAAhE,KAAA,GAAA4D,GAAA,CAAA5D,KAAA,CAAAkE,OAAA;cACA,IAAAlE,KAAA,IAAAA,KAAA;gBACA8D,MAAA,GAAA9D,KAAA;gBACA;cACA;YACA;YAEA,KAAA8D,MAAA;cACA;cACA;YACA;;YAEA;YACAA,MAAA,GAAAA,MAAA,CAAA/D,OAAA,CACA,6CACA,EACA;YACA,KAAA+D,MAAA,CAAAvF,QAAA,eAAAuF,MAAA,CAAAvF,QAAA;cACA,IAAAsD,SAAA,GAAAiC,MAAA,CAAAvF,QAAA;cACAuF,MAAA,OAAA1E,MAAA,CAAAyC,SAAA;YACA;;YAEA;YACA,KAAAiC,MAAA,CAAApC,UAAA;cACA,IAAAoC,MAAA,CAAApC,UAAA;gBACAoC,MAAA,cAAAA,MAAA;cACA;gBACAA,MAAA,gBAAAA,MAAA;cACA;YACA;;YAEA;YACA,IAAAK,MAAA,iEAAA/E,MAAA,CAAA0E,MAAA,oBAAA1E,MAAA,CAAA0E,MAAA,6BAAA1E,MAAA,CAAA0E,MAAA;;YAEA;YACAnE,OAAA,GAAAA,OAAA,CAAAI,OAAA,CAAA6D,GAAA,EAAAO,MAAA;UACA;QAAA,SAAAC,GAAA;UAAAd,SAAA,CAAAtB,CAAA,CAAAoC,GAAA;QAAA;UAAAd,SAAA,CAAAe,CAAA;QAAA;QAEA,OAAA1E,OAAA;MACA,SAAAqC,CAAA;QACA9C,OAAA,CAAA+C,KAAA,cAAAD,CAAA;QACA,OAAArC,OAAA;MACA;IACA;IAEA;IACA0C,eAAA,WAAAA,gBAAA;MACA;QACA;QACA,IAAArB,KAAA,GAAA1B,QAAA,CAAAG,aAAA;QACAuB,KAAA,CAAAsD,WAAA,8ZAUA;QACAhF,QAAA,CAAAM,IAAA,CAAAC,WAAA,CAAAmB,KAAA;;QAEA;QACA,IAAAX,YAAA,GAAAf,QAAA,CAAAgB,gBAAA,CACA,kFACA;QAEA,IAAAD,YAAA,CAAAE,MAAA;UACArB,OAAA,CAAAC,GAAA,6OAAAC,MAAA,CACAiB,YAAA,CAAAE,MAAA,6EACA;UAEAF,YAAA,CAAAI,OAAA,WAAAC,GAAA;YACA;YACAA,GAAA,CAAAY,YAAA;YACAZ,GAAA,CAAAa,SAAA,CAAAC,GAAA;;YAEA;YACA,KAAAd,GAAA,CAAAE,YAAA;cACAF,GAAA,CAAAY,YAAA;;cAEA;cACAZ,GAAA,CAAA6D,OAAA;gBACArF,OAAA,CAAAC,GAAA;gBACA,KAAA6B,KAAA,CAAAwD,MAAA;gBACA,KAAAxD,KAAA,CAAAyD,OAAA;gBACA,KAAAzD,KAAA,CAAAC,KAAA;gBACA,KAAAD,KAAA,CAAAG,MAAA;gBACA,KAAAH,KAAA,CAAA0D,SAAA;gBACA,KAAAC,GAAA;cACA;YACA;UACA;QACA;MACA,SAAA3C,CAAA;QACA9C,OAAA,CAAA+C,KAAA,cAAAD,CAAA;MACA;;MAEA;MACA,OAAA7B,OAAA,CAAAC,OAAA;IACA;IAEA;IACAwC,wBAAA,WAAAA,yBAAAjD,OAAA;MAAA,IAAAiF,gBAAA;MACA,KAAAjF,OAAA,OAAAiF,gBAAA,QAAA9H,UAAA,cAAA8H,gBAAA,eAAAA,gBAAA,CAAAtG,WAAA;QACA,OAAAqB,OAAA;MACA;;MAEA;MACA,IAAArB,WAAA,QAAAxB,UAAA,CAAAwB,WAAA;MACA,IAAAuG,QAAA,GAAAvG,WAAA,CAAAwG,KAAA;MACAD,QAAA,CAAAE,GAAA;MACA,IAAAC,OAAA,GAAAH,QAAA,CAAAI,IAAA;;MAEA;MACA,OAAAtF,OAAA,CAAAI,OAAA,CACA,qDACA,UAAAC,KAAA,EAAAkF,MAAA,EAAAtD,GAAA,EAAAuD,KAAA;QACA;QACA,IAAAvD,GAAA,CAAAF,UAAA,YAAAE,GAAA,CAAAF,UAAA;UACA,OAAA1B,KAAA;QACA;;QAEA;QACA,kDAAAoF,IAAA,CAAAxD,GAAA;UACA,OAAA5B,KAAA;QACA;;QAEA;QACA,IAAAqF,MAAA;QACA,IAAAzD,GAAA,CAAAF,UAAA;UACA2D,MAAA,GAAAL,OAAA,GAAApD,GAAA,CAAA0D,SAAA;QACA,WAAA1D,GAAA,CAAAF,UAAA;UACA,IAAA6D,MAAA,OAAAC,GAAA,CAAAlH,WAAA;UACA+G,MAAA,GAAAE,MAAA,CAAAE,QAAA,UAAAF,MAAA,CAAAG,IAAA,GAAA9D,GAAA;QACA;UACAyD,MAAA,GAAAL,OAAA,GAAApD,GAAA;QACA;;QAEA;QACA,IAAAuC,MAAA,UAAA/E,MAAA,CAAA8F,MAAA,aAAA9F,MAAA,CAAAiG,MAAA,QAAAjG,MAAA,CAAA+F,KAAA;QACAhB,MAAA,GAAAA,MAAA,CAAApE,OAAA,CACA,qDACA,EACA;QAEA,OAAAoE,MAAA;MACA,CACA;IACA;IAEA;IACAxB,kBAAA,WAAAA,mBAAAhD,OAAA;MACA,KAAAA,OAAA,SAAAA,OAAA;MAEA;QACA;QACAA,OAAA,GAAAA,OAAA,CACAI,OAAA,cACAA,OAAA,cACAA,OAAA;;QAEA;QACA,IAAA4F,KAAA,GACA;;QAEA;QACA,IAAAC,UAAA,GAAAjG,OAAA,CAAAI,OAAA,CACA4F,KAAA,EACA,kGACA;;QAEA;QACA,IAAAE,YAAA,GACA;QACAD,UAAA,GAAAA,UAAA,CAAA7F,OAAA,CACA8F,YAAA,EACA,kGACA;QAEA,OAAAD,UAAA;MACA,SAAA5D,CAAA;QACA9C,OAAA,CAAA+C,KAAA,kBAAAD,CAAA;QACA,OAAArC,OAAA;MACA;IACA;IAEAb,YAAA,WAAAA,aAAA;MAAA,IAAAgH,MAAA;MAAA,WAAAC,kBAAA,CAAA5H,OAAA,mBAAA6H,oBAAA,CAAA7H,OAAA,IAAA8H,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAA7H,OAAA,IAAAgI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACAC,cAAA,CAAAC,YAAA,GAAAnE,IAAA,WAAAoE,QAAA;gBACA,IAAAA,QAAA,CAAAC,IAAA;kBACAb,MAAA,CAAA5I,WAAA,GAAAwJ,QAAA,CAAAjK,IAAA,CAAAmK,KAAA,OAAAC,GAAA,WAAAC,IAAA;oBACAA,IAAA,CAAAC,OAAA,GAAAD,IAAA,CAAAE,KAAA;oBACAF,IAAA,CAAAG,EAAA,GAAAH,IAAA,CAAAI,SAAA;oBACA,OAAAJ,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAT,QAAA,CAAAc,IAAA;UAAA;QAAA,GAAAjB,OAAA;MAAA;IACA;IAEArH,OAAA,WAAAA,QAAA;MAAA,IAAAuI,MAAA;MAAA,WAAArB,kBAAA,CAAA5H,OAAA,mBAAA6H,oBAAA,CAAA7H,OAAA,IAAA8H,IAAA,UAAAoB,SAAA;QAAA,IAAAC,MAAA;QAAA,WAAAtB,oBAAA,CAAA7H,OAAA,IAAAgI,IAAA,UAAAoB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlB,IAAA,GAAAkB,SAAA,CAAAjB,IAAA;YAAA;cAEA,IAAAa,MAAA,CAAAK,MAAA,CAAAC,KAAA,CAAAT,EAAA;gBACAK,MAAA;kBACAL,EAAA,EAAAG,MAAA,CAAAK,MAAA,CAAAC,KAAA,CAAAT;gBACA;cACA;gBACAK,MAAA;kBAAAK,SAAA,EAAAP,MAAA,CAAAK,MAAA,CAAAC,KAAA,CAAAC;gBAAA;cACA;cACA;cAAAH,SAAA,CAAAjB,IAAA;cAAA,OACAC,cAAA,CAAAoB,aAAA,CAAAN,MAAA,EAAAhF,IAAA;gBAAA,IAAAuF,IAAA,OAAA9B,kBAAA,CAAA5H,OAAA,mBAAA6H,oBAAA,CAAA7H,OAAA,IAAA8H,IAAA,UAAA6B,SAAAC,GAAA;kBAAA,IAAAC,UAAA,EAAAC,gBAAA,EAAAtK,SAAA,EAAAC,QAAA,EAAAsK,kBAAA;kBAAA,WAAAlC,oBAAA,CAAA7H,OAAA,IAAAgI,IAAA,UAAAgC,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAA9B,IAAA,GAAA8B,SAAA,CAAA7B,IAAA;sBAAA;wBAAA,MACAwB,GAAA,CAAApB,IAAA;0BAAAyB,SAAA,CAAA7B,IAAA;0BAAA;wBAAA;wBACAa,MAAA,CAAAiB,eAAA,CAAAN,GAAA,CAAAtL,IAAA,CAAAwK,EAAA;wBACAG,MAAA,CAAAtK,UAAA,GAAAiL,GAAA,CAAAtL,IAAA;wBACA2K,MAAA,CAAAtK,UAAA,CAAAwL,UAAA,WAAAlB,MAAA,CAAAtK,UAAA,CAAAwL,UAAA,UACAlB,MAAA,CAAAxK,kBAAA,UACAwK,MAAA,CAAAxK,kBAAA;;wBAEA;wBACA,IACAwK,MAAA,CAAAtK,UAAA,CAAAwB,WAAA,IACA8I,MAAA,CAAAtK,UAAA,CAAAwB,WAAA,CAAAC,QAAA,8BACA;0BACA6I,MAAA,CAAAzK,YAAA;wBACA;0BACAyK,MAAA,CAAAzK,YAAA;wBACA;;wBAEA;wBACAqL,UAAA,GACAZ,MAAA,CAAAtK,UAAA,CAAAyL,SAAA,IAAAnB,MAAA,CAAAtK,UAAA,CAAA6C,OAAA;wBACAsI,gBAAA,GAAAb,MAAA,CAAAoB,cAAA,CAAAR,UAAA,GAEA;wBACArK,SAAA;wBACAC,QAAA;wBAEA,IAAAwJ,MAAA,CAAAtK,UAAA,CAAA2L,IAAA,IAAAC,KAAA,CAAAC,OAAA,CAAAvB,MAAA,CAAAtK,UAAA,CAAA2L,IAAA;0BACArB,MAAA,CAAAtK,UAAA,CAAA2L,IAAA,CAAAhI,OAAA,WAAAqG,IAAA;4BACA,IAAAA,IAAA,CAAA8B,UAAA,YAAA9B,IAAA,CAAA+B,WAAA;8BACA;8BACAlL,SAAA,GAAAmJ,IAAA,CAAA+B,WAAA;4BACA,WAAA/B,IAAA,CAAA8B,UAAA,YAAA9B,IAAA,CAAA+B,WAAA;8BACA;8BACAjL,QAAA,GAAAkJ,IAAA,CAAA+B,WAAA;4BACA;0BACA;wBACA;;wBAEA;wBACAX,kBAAA,YAAAA,mBAAAY,IAAA;0BACA,KAAAA,IAAA;0BACA;0BACA,OAAAA,IAAA,CACA/I,OAAA;0BAAA,CACAA,OAAA;0BAAA,CACAA,OAAA;0BAAA,CACAA,OAAA;0BAAA,CACAA,OAAA;0BAAA,CACAA,OAAA;wBACA,GAEA;;wBACAqH,MAAA,CAAA1J,eAAA,CAAAC,SAAA,GAAAuK,kBAAA,CAAAvK,SAAA;wBACAyJ,MAAA,CAAA1J,eAAA,CAAAE,QAAA,GAAAsK,kBAAA,CAAAtK,QAAA;;wBAEA;wBAAA,MAEAwJ,MAAA,CAAAtK,UAAA,CAAAwB,WAAA,IACA8I,MAAA,CAAAtK,UAAA,CAAAwB,WAAA,CAAAC,QAAA;0BAAA6J,SAAA,CAAA7B,IAAA;0BAAA;wBAAA;wBAAA6B,SAAA,CAAA9B,IAAA;wBAAA8B,SAAA,CAAA7B,IAAA;wBAAA,OAIAa,MAAA,CAAA5E,mBAAA,CACAyF,gBACA;sBAAA;wBAFAA,gBAAA,GAAAG,SAAA,CAAAW,IAAA;wBAGA7J,OAAA,CAAAC,GAAA;wBAAAiJ,SAAA,CAAA7B,IAAA;wBAAA;sBAAA;wBAAA6B,SAAA,CAAA9B,IAAA;wBAAA8B,SAAA,CAAAY,EAAA,GAAAZ,SAAA;wBAEAlJ,OAAA,CAAA+C,KAAA,eAAAmG,SAAA,CAAAY,EAAA;wBACA;wBACA5B,MAAA,CAAAzK,YAAA;sBAAA;wBAIA;wBACAyK,MAAA,CAAA6B,IAAA,CAAA7B,MAAA,cAAAa,gBAAA;wBAEA,IAAAb,MAAA,CAAAtK,UAAA,CAAAkK,KAAA,IAAAI,MAAA,CAAAtK,UAAA,CAAAiK,OAAA;0BACAK,MAAA,CAAAvK,SAAA;wBACA;wBAEAyC,QAAA,CAAA0H,KAAA,GAAAI,MAAA,CAAAtK,UAAA,CAAAiK,OAAA,IAAAK,MAAA,CAAAtK,UAAA,CAAAkK,KAAA;wBACAI,MAAA,CAAA1K,OAAA;;wBAEA;wBACA,IAAA0K,MAAA,CAAA/I,eAAA;0BACA+I,MAAA,CAAAhF,SAAA;4BACA;4BACAgF,MAAA,CAAA/E,eAAA;;4BAEA;4BACAN,UAAA;8BACAqF,MAAA,CAAA/E,eAAA;4BACA;0BACA;wBACA;sBAAA;sBAAA;wBAAA,OAAA+F,SAAA,CAAAjB,IAAA;oBAAA;kBAAA,GAAAW,QAAA;gBAAA,CAEA;gBAAA,iBAAAoB,EAAA;kBAAA,OAAArB,IAAA,CAAAsB,KAAA,OAAAC,SAAA;gBAAA;cAAA;YAAA;YAAA;cAAA,OAAA5B,SAAA,CAAAL,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IACA;IACAmB,cAAA,WAAAA,eAAA7I,OAAA;MACA,IAAAA,OAAA;QACA,QAAA0J,2BAAA,EAAA1J,OAAA;UACAA,OAAA,GAAAA,OAAA,CAAAI,OAAA;UACAJ,OAAA,GAAAA,OAAA,CAAAI,OAAA;UACAJ,OAAA,GAAAA,OAAA,CAAAI,OAAA;UACAJ,OAAA,GAAAA,OAAA,CAAAI,OAAA;UACAJ,OAAA,GAAAA,OAAA,CAAAI,OAAA;UACAJ,OAAA,GAAAA,OAAA,CAAAI,OAAA;;UAEA;UACA,IACA,KAAAjD,UAAA,CAAAwB,WAAA,IACA,KAAAxB,UAAA,CAAAwB,WAAA,CAAAC,QAAA,8BACA;YACAoB,OAAA,QAAAgD,kBAAA,CAAAhD,OAAA;UACA;;UAEA;UACAA,OAAA,QAAAiD,wBAAA,CAAAjD,OAAA;UAEAT,OAAA,CAAAC,GAAA,+PAAAmK,0BAAA,EAAA3J,OAAA;UACAT,OAAA,CAAAC,GAAA,sQAAAoK,gCAAA,EAAA5J,OAAA;QACA;UACAA,OAAA,GAAAA,OAAA,CAAAI,OAAA;UACAJ,OAAA,GAAAA,OAAA,CAAAI,OAAA;UACAJ,OAAA,GAAAA,OAAA,CAAAI,OAAA;UACAJ,OAAA,GAAAA,OAAA,CAAAI,OAAA;UACAJ,OAAA,GAAAA,OAAA,CAAAI,OAAA;UACAJ,OAAA,GAAAA,OAAA,CAAAI,OAAA;;UAEA;UACA,IACA,KAAAjD,UAAA,CAAAwB,WAAA,IACA,KAAAxB,UAAA,CAAAwB,WAAA,CAAAC,QAAA,8BACA;YACAoB,OAAA,QAAAgD,kBAAA,CAAAhD,OAAA;UACA;;UAEA;UACAA,OAAA,QAAAiD,wBAAA,CAAAjD,OAAA;QACA;MACA;MACA,OAAAA,OAAA;IACA;IACA;IACA6J,cAAA,WAAAA,eAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,GAAA,YAAAA,IAAA;QACA,IAAAjN,OAAA,GAAAgN,MAAA,CAAAE,QAAA;UACAC,IAAA;UACAf,IAAA;UACAgB,OAAA;UACAC,UAAA;QACA;QACAvD,cAAA,CAAAwD,gBAAA;UACAC,YAAA,EAAAR,GAAA,CAAA9J,OAAA;UACAuK,KAAA,EAAAR,MAAA,CAAAjC,MAAA,CAAAC,KAAA,CAAAT,EAAA;UACAA,EAAA,EAAAwC,GAAA,CAAAxC,EAAA;UACAkD,gBAAA;UACAC,eAAA;QACA,GACA9H,IAAA,WAAAyF,GAAA;UACA2B,MAAA,CAAA5M,UAAA,CAAAyL,SAAA,GAAAR,GAAA,CAAAtL,IAAA;UACAiN,MAAA,CAAAT,IAAA,CACAS,MAAA,EACA,YACAA,MAAA,CAAAlB,cAAA,CACAkB,MAAA,CAAA5M,UAAA,CAAAyL,SAAA,IAAAmB,MAAA,CAAA5M,UAAA,CAAA6C,OACA,CACA;UACA+J,MAAA,CAAAzM,mBAAA;UACAP,OAAA,CAAA2N,KAAA;QACA,GACAC,KAAA,WAAAlG,GAAA;UACA1H,OAAA,CAAA2N,KAAA;QACA;MACA;MAEA,IAAAZ,GAAA,CAAAlB,SAAA;QACA;QACA,KAAAgC,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAtN,IAAA;QACA,GACAmF,IAAA;UACAqH,GAAA;QACA,GACAW,KAAA;UACAZ,MAAA,CAAAT,IAAA,CACAS,MAAA,EACA,YACAA,MAAA,CAAAlB,cAAA,CACAkB,MAAA,CAAA5M,UAAA,CAAAyL,SAAA,IAAAmB,MAAA,CAAA5M,UAAA,CAAA6C,OACA,CACA;UACA+J,MAAA,CAAAzM,mBAAA;QACA;MACA;QACA0M,GAAA;MACA;IACA;IACAe,YAAA,WAAAA,aAAA;MACA,SAAAzN,mBAAA;QACA,KAAAgM,IAAA,CACA,MACA,YACA,KAAAT,cAAA,MAAA1L,UAAA,CAAA6C,OAAA,CACA;MACA;QACA,KAAAsJ,IAAA,CACA,MACA,YACA,KAAAT,cAAA,CACA,KAAA1L,UAAA,CAAAyL,SAAA,SAAAzL,UAAA,CAAA6C,OACA,CACA;MACA;MACA,KAAA1C,mBAAA,SAAAA,mBAAA;IACA;IACA0N,SAAA,WAAAA,UAAA;MACAC,MAAA,CAAAC,QAAA;QAAAC,GAAA;QAAAC,QAAA;MAAA;IACA;IACAV,KAAA,WAAAA,MAAA;MACA,KAAAlN,IAAA;MACA,KAAAC,QAAA;IACA;IACA4N,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,yBAAA;QACAhE,SAAA,OAAAO,MAAA,CAAAC,KAAA,CAAAT,EAAA;QACAiD,KAAA,OAAAzC,MAAA,CAAAC,KAAA,CAAAT,EAAA;QACAF,OAAA,OAAAjK,UAAA,CAAAiK,OAAA,SAAAjK,UAAA,CAAAkK,KAAA;QACArH,OAAA,OAAAvC;MACA,GAAAkF,IAAA,WAAAyF,GAAA;QACA,IAAAA,GAAA,CAAApB,IAAA;UACAsE,MAAA,CAAAE,QAAA;YACAC,OAAA;YACAjO,IAAA;UACA;UACA8N,MAAA,CAAAZ,KAAA;QACA;MACA;IACA;IACAgB,WAAA,WAAAA,YAAAvE,IAAA;MACA8D,MAAA,CAAAU,IAAA,uBAAAlM,MAAA,CAAA0H,IAAA,CAAAG,EAAA,aAAA7H,MAAA,CAAA0H,IAAA,CAAAG,EAAA;IACA;IACAsE,iBAAA,WAAAA,kBAAAzE,IAAA;MAAA,IAAA0E,MAAA;MACA,KAAA1E,IAAA,CAAA2E,UAAA;QACAjF,cAAA,CAAAkF,UAAA,EAAA5E,IAAA,CAAAG,EAAA,GAAA3E,IAAA,WAAAyF,GAAA;UACA,IAAAA,GAAA,CAAApB,IAAA;YACA6E,MAAA,CAAAL,QAAA;cACAC,OAAA;cACAjO,IAAA;YACA;YACAqO,MAAA,CAAAvC,IAAA,CAAAuC,MAAA,CAAA1O,UAAA,iBAAAgK,IAAA,CAAA2E,UAAA;UACA;YACAD,MAAA,CAAAL,QAAA;cAAAC,OAAA;cAAAjO,IAAA;YAAA;UACA;QACA;MACA;QACAqJ,cAAA,CAAAmF,YAAA,EAAA7E,IAAA,CAAAG,EAAA,GAAA3E,IAAA,WAAAyF,GAAA;UACA,IAAAA,GAAA,CAAApB,IAAA;YACA6E,MAAA,CAAAL,QAAA;cAAAC,OAAA;cAAAjO,IAAA;YAAA;YACAqO,MAAA,CAAAvC,IAAA,CAAAuC,MAAA,CAAA1O,UAAA,iBAAAgK,IAAA,CAAA2E,UAAA;UACA;YACAD,MAAA,CAAAL,QAAA;cAAAC,OAAA;cAAAjO,IAAA;YAAA;UACA;QACA;MACA;IACA;IACAyO,eAAA,WAAAA,gBAAA9E,IAAA;MAAA,IAAA+E,MAAA;MACA,IAAAnE,KAAA,OAAAoE,QAAA;MACApE,KAAA,CAAAqE,MAAA,cAAAjF,IAAA,CAAAG,EAAA;MACA,KAAAH,IAAA,CAAAkF,SAAA;QACAxF,cAAA,CAAAyF,YAAA,CAAAvE,KAAA,EAAApF,IAAA,WAAAyF,GAAA;UACA,IAAAA,GAAA,CAAApB,IAAA;YACAkF,MAAA,CAAAV,QAAA;cACAC,OAAA;cACAjO,IAAA;YACA;YACA0O,MAAA,CAAA5C,IAAA,CAAA4C,MAAA,CAAA/O,UAAA,gBAAAgK,IAAA,CAAAkF,SAAA;UACA;YACAH,MAAA,CAAAV,QAAA;cAAAC,OAAA;cAAAjO,IAAA;YAAA;UACA;QACA;MACA;QACAqJ,cAAA,CAAA0F,eAAA,CAAAxE,KAAA,EAAApF,IAAA,WAAAyF,GAAA;UACA,IAAAA,GAAA,CAAApB,IAAA;YACAkF,MAAA,CAAAV,QAAA;cAAAC,OAAA;cAAAjO,IAAA;YAAA;YACA0O,MAAA,CAAA5C,IAAA,CAAA4C,MAAA,CAAA/O,UAAA,gBAAAgK,IAAA,CAAAkF,SAAA;UACA;YACAH,MAAA,CAAAV,QAAA;cAAAC,OAAA;cAAAjO,IAAA;YAAA;UACA;QACA;MACA;IACA;IACAkL,eAAA,WAAAA,gBAAApB,EAAA;MACA,IAAAS,KAAA,OAAAoE,QAAA;MACApE,KAAA,CAAAqE,MAAA,cAAA9E,EAAA;MACAT,cAAA,CAAA2F,SAAA,CAAAzE,KAAA;IACA;IACA0E,mBAAA,WAAAA,oBAAAtF,IAAA;MACA8D,MAAA,CAAAU,IAAA,CAAAxE,IAAA,CAAAxI,WAAA;IACA;IACA;IACA+N,WAAA,WAAAA,YAAAlP,IAAA;MACA,UAAAO,eAAA,CAAAP,IAAA;QACA,KAAAgO,QAAA,CAAAmB,OAAA;QACA;MACA;;MAEA;MACA,IAAAtF,KAAA;MACA,IAAA7J,IAAA;QACA6J,KAAA;MACA,WAAA7J,IAAA;QACA6J,KAAA;MACA;;MAEA;MACA,IAAArH,OAAA,QAAAjC,eAAA,CAAAP,IAAA;;MAEA;MACA,IAAAoP,UAAA,mBAAAnN,MAAA,CAAAoN,IAAA,CAAAC,GAAA;MACAC,YAAA,CAAAC,OAAA,CAAAJ,UAAA,EAAA5M,OAAA;;MAEA;MACA,IAAA2H,MAAA,OAAAsF,eAAA;MACAtF,MAAA,CAAAyE,MAAA,UAAA/E,KAAA;MACAM,MAAA,CAAAyE,MAAA,CACA,gBACA,KAAAjP,UAAA,CAAAiK,OAAA,SAAAjK,UAAA,CAAAkK,KACA;MACAM,MAAA,CAAAyE,MAAA,eAAAQ,UAAA;;MAEA;MACA,IAAAM,WAAA,GAAAjC,MAAA,CAAAkC,MAAA,CAAA7L,KAAA;MACA,IAAA8L,YAAA,GAAAnC,MAAA,CAAAkC,MAAA,CAAA3L,MAAA;;MAEA;MACA,IAAAF,KAAA,GAAA+L,IAAA,CAAAC,KAAA,CAAAJ,WAAA;MACA,IAAA1L,MAAA,GAAA6L,IAAA,CAAAC,KAAA,CAAAF,YAAA;MACA,IAAAG,IAAA,GAAAF,IAAA,CAAAC,KAAA,EAAAJ,WAAA,GAAA5L,KAAA;MACA,IAAA6J,GAAA,GAAAkC,IAAA,CAAAC,KAAA,EAAAF,YAAA,GAAA5L,MAAA;;MAEA;MACA,IAAAgM,cAAA,YAAA/N,MAAA,CAAA6B,KAAA,cAAA7B,MAAA,CAAA+B,MAAA,YAAA/B,MAAA,CAAA8N,IAAA,WAAA9N,MAAA,CAAA0L,GAAA;MACA,IAAAsC,SAAA,GAAAxC,MAAA,CAAAU,IAAA,aAAAlM,MAAA,CACAkI,MAAA,CAAA+F,QAAA,KACA,UACAF,cACA;;MAEA;MACA,IACA,CAAAC,SAAA,IACAA,SAAA,CAAAE,MAAA,IACA,OAAAF,SAAA,CAAAE,MAAA,kBACA;QACA;QACA1C,MAAA,CAAAU,IAAA,aAAAlM,MAAA,CAAAkI,MAAA,CAAA+F,QAAA;QACA,KAAAlC,QAAA,CAAAoC,IAAA;MACA;IACA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MACA,KAAA3P,cAAA;MACA,KAAAC,cAAA;IACA;IACA;IACA2P,iBAAA,WAAAA,kBAAAC,WAAA,EAAAC,gBAAA;MACA;MACA,IAAAC,oBAAA,QAAAC,SAAA,CAAAH,WAAA;;MAEA;MACA,KAAAC,gBAAA;QACA,gBAAAC,oBAAA;MACA;MAEA,IAAAE,mBAAA;MACA;MACA,IAAAH,gBAAA;QACAG,mBAAA,QAAAD,SAAA,CAAAF,gBAAA;MACA;;MAEA;MACA,IAAAC,oBAAA,KAAAE,mBAAA;QACA,gBAAAF,oBAAA;MACA;QACA,wBAAAxO,MAAA,CAAA0O,mBAAA,uBAAA1O,MAAA,CAAAwO,oBAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
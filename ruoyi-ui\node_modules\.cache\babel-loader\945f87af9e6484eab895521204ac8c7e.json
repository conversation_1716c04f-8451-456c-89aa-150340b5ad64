{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\tabOne.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\tabOne.vue", "mtime": 1754397030175}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\babel.config.js", "mtime": 1745890588273}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_usaMap", "_interopRequireDefault", "require", "_timeLine", "_graphEcharts", "_technologyArticles", "_trumpViewTree", "_viewTree", "_policyRisk", "_articleDetails", "_suppressionOfRisks", "_enterpriseInformation", "_comparisonChart", "_hotTechnology", "_baarTreeEcharts", "_technologyDetails", "_techBubbleDialog", "_ArticleNotification", "_sanhao", "_index", "_zhiku", "_renwu", "_MarkmapDialog", "_htmlUtils", "_markmapLib", "_markmap<PERSON>iew", "name", "components", "usaMap", "timeLine", "graphEcharts", "technologyArticles", "trumpViewTree", "viewTree", "policyRisk", "articleDetails", "suppressionOfRisks", "enterpriseInformation", "comparisonChart", "hotTechnology", "baarTreeEcharts", "technologyDetails", "techBubbleDialog", "MarkmapDialog", "ArticleNotification", "props", "notificationArticles", "type", "Array", "default", "showNotification", "Boolean", "data", "policyRiskShowModal", "comparisonChartShowModal", "hotTechnologyShowModal", "hotTechnologytTitle", "hotTechnologytID", "baarTreeEchartsShowModal", "baarTreeEchartsType", "technologyDetailsShowModal", "technologyDetailsTitle", "technologyDetailsItem", "suppressionOfRisksShowModal", "enterpriseInformationShowModal", "enterpriseInformationTitle", "articleDetailsShowModal", "articleDetailsTitle", "articleDetailsContent", "articleDetailsContentEn", "suppressListData", "riskBarChartData", "riskEnterpriseList", "riskEnterpriseListTotal", "enterpriseInformationContent", "patentList", "softwareList", "patentTotal", "softwareTotal", "policyRiskList1", "policyRiskList2", "policyRiskList1Total", "usaMapData", "articleDetailsItem", "remengwenzhangList", "scrollTimer", "scrollTimer1", "scrollTimer2", "isHovered", "isHovered1", "isHovered2", "scrollStep", "drawerInfo", "articleDialogVisible", "fontSize", "oriFontSize", "characterViewData", "thinkTankViewData", "remengwenzhangList1", "activeButton", "qianyankejiList", "gatherTotal", "gatherDayNumber", "markmapVisible", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "markmapTitle", "aiLoading", "frontLoginParams", "username", "password", "frontToken", "activeTechButton", "currentTechScreenSn", "techBubbleDialogVisible", "techBubbleDialogTitle", "techBubbleDialogScreenSn", "activeTab", "domainMarkdown", "sinaUrl", "zhikuActive", "computed", "remengwenzhangBoxStyle", "notificationHeight", "height", "concat", "mounted", "_this", "loginSINA", "then", "res", "console", "log", "catch", "error", "getSuppressData", "initHotList", "initHotList1", "updateScrollbar", "updateScrollbar1", "fetchUsaMapData", "largeGatherQueryGatherData", "<PERSON><PERSON><PERSON><PERSON>", "clearScrollTimer", "clearScrollTimer1", "clearScrollTimer2", "handleMarkmapClose", "methods", "_this2", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "response", "wrap", "_callee$", "_context", "prev", "next", "proposalsCount", "projectSn", "screenSn", "columnSn", "sent", "t0", "stop", "openArticleDetails", "item", "_this3", "technicalArticleDetail", "id", "title", "content", "enContent", "openNewView", "openEnterpriseInformation", "_this4", "suppressPatentList", "suppressSn", "pageNum", "pageSize", "rows", "total", "suppressSoftwareList", "_objectSpread2", "enterpriseName", "patentPagination", "queryParams", "_this5", "softwarePagination", "_this6", "_this7", "suppressData", "Object", "keys", "for<PERSON>ach", "key", "push", "date", "description", "length", "slice", "reverse", "getRiskDetail", "_this8", "suppressEnterpriseList", "map", "index", "suppressLevelCount", "year", "product", "严重", "一般", "较轻", "riskEnterpriseListPagination", "_this9", "getPolicyRiskDetail", "_this10", "proposalsList", "proposalsToChinaData", "policyRiskPagination", "_this11", "openHotTechnology", "reportSn", "openbaarTreeEcharts", "markdownData", "trump", "$refs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType", "msk", "ws", "openTechnologyDetails", "_this12", "largeHotList2", "$nextTick", "startScroll", "_this13", "handleButtonClick", "startScroll2", "_this14", "wrapper", "scrollWrapper", "scrollContent", "setInterval", "scrollTop", "scrollHeight", "clientHeight", "clearInterval", "handleMouseEnter", "handleMouseLeave", "startScroll1", "_this15", "scrollWrapper1", "scrollContent1", "_this16", "tabOrder", "currentIndex", "indexOf", "nextIndex", "handleMouseEnter1", "handleMouseLeave1", "handleMouseEnter2", "handleMouseLeave2", "scrollPercent", "scrollbarHeight", "Math", "max", "scrollbarTop", "document", "documentElement", "style", "setProperty", "_this17", "_callee2", "_callee2$", "_context2", "largeHotQueryById", "cnTitle", "cnC<PERSON>nt", "formattingJson", "openNewView1", "_this18", "_callee3", "_callee3$", "_context3", "getLargeFTT", "sn", "containsHtmlTags", "replace", "extractHtmlTags", "hasValidHtmlStructure", "handleClose", "increaseFontSize", "decreaseFontSize", "handleNodeClick", "currentCharacter", "rawData", "JSON", "parse", "stringify", "treeData2", "limitLevel3Children", "_this19", "isArray", "children", "_this20", "obj", "脑机接口", "量子信息", "人形机器人", "生成式人工智能", "生物制造", "未来显示", "未来网络", "新型储能", "其他", "kjdtArticleList", "labelSn", "deduplicatedData", "deduplicateArticles", "qykjdtOpenNewTab", "window", "open", "articles", "seen", "Set", "result", "article", "normalizedTitle", "has", "add", "padWithZeros", "num", "targetLength", "numStr", "toString", "padding", "repeat", "openNewTab", "url", "updateHotArticlesList", "_this21", "_callee4", "newArticles", "_callee4$", "_context4", "isArticleDataChanged", "i", "newArticle", "oldArticle", "publishTime", "sourceName", "handleTechButtonClick", "buttonName", "handleTechBubbleDialogClose", "handleNotificationClose", "$emit", "handleViewArticle", "switchTab", "tabName", "markdownType", "_this22", "<PERSON><PERSON><PERSON><PERSON>", "renderMarkmap", "allTypes", "_this23", "_callee5", "svg", "processedContent", "transformer", "_transformer$transfor", "root", "mm", "resize<PERSON><PERSON>ler", "_callee5$", "_context5", "loading", "abrupt", "markmap", "Error", "innerHTML", "Transformer", "transform", "Markmap", "create", "autoFit", "duration", "nodeMinHeight", "spacingVertical", "spacingHorizontal", "paddingX", "color", "node", "colors", "depth", "nodeFont", "fonts", "max<PERSON><PERSON><PERSON>", "initialExpandLevel", "zoom", "pan", "linkShape", "linkWidth", "linkColor", "setTimeout", "fit", "fitRatio", "_mm$state", "state", "minX", "maxX", "minY", "maxY", "width", "containerWidth", "clientWidth", "containerHeight", "scale", "min", "setData", "initialScale", "initialPosition", "addEventListener", "$once", "removeEventListener", "$message", "finish", "openSina"], "sources": ["src/views/bigScreenSanhao/tabOne.vue"], "sourcesContent": ["<template>\r\n  <div style=\"height: 100%; display: flex\" class=\"two\">\r\n    <div class=\"left\">\r\n      <div class=\"bsContentBox\" style=\"width: 516px; height: 380px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">热点推荐</div>\r\n          <!-- <div class=\"bsContentTitleHelp\"></div> -->\r\n\r\n          <div\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"openNewTab('MonitorUse?id=1')\"\r\n            style=\"right: 80px\"\r\n          >\r\n            更多\r\n          </div>\r\n          <div class=\"bsContentTitleMore\" @click=\"openSina\">舆情通</div>\r\n        </div>\r\n        <div class=\"bsContentContent\">\r\n          <div class=\"remengwenzhang-box\" :style=\"remengwenzhangBoxStyle\">\r\n            <div\r\n              class=\"scroll-wrapper\"\r\n              ref=\"scrollWrapper\"\r\n              @mouseenter=\"handleMouseEnter\"\r\n              @mouseleave=\"handleMouseLeave\"\r\n              @scroll=\"updateScrollbar\"\r\n            >\r\n              <div class=\"scroll-content\" ref=\"scrollContent\">\r\n                <div\r\n                  class=\"remengwenzhang-list\"\r\n                  v-for=\"(item, index) in remengwenzhangList\"\r\n                  :key=\"index\"\r\n                  @click=\"openNewView(item)\"\r\n                >\r\n                  <div\r\n                    class=\"block\"\r\n                    :style=\"{\r\n                      background: item.isShow === '3' ? '#F48200' : '#1bdcff',\r\n                    }\"\r\n                  ></div>\r\n                  <div class=\"title\">{{ item.title }}</div>\r\n                  <div class=\"sourceName\">{{ item.sourceName }}</div>\r\n                  <div class=\"time\">\r\n                    {{ parseTime(item.publishTime, \"{y}-{m}-{d}\") }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"scroll-bar\"></div>\r\n          </div>\r\n\r\n          <!-- 文章通知组件 -->\r\n          <article-notification\r\n            ref=\"articleNotification\"\r\n            :articles=\"notificationArticles\"\r\n            :visible=\"showNotification\"\r\n            @close=\"handleNotificationClose\"\r\n            @view-article=\"handleViewArticle\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <div class=\"bsContentBox\" style=\"width: 516px; height: 290px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">政策风险</div>\r\n          <div class=\"bsContentTitleHelp\" @click=\"getPolicyRiskDetail\"></div>\r\n          <div\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"openNewTab('http://61.149.6.16:21001/bigScreen')\"\r\n          >\r\n            更多\r\n          </div>\r\n        </div>\r\n        <div class=\"bsContentContent\">\r\n          <usaMap\r\n            style=\"width: 516px; height: 247px\"\r\n            :external-data=\"usaMapData\"\r\n          ></usaMap>\r\n        </div>\r\n      </div>\r\n      <div class=\"bsContentBox\" style=\"width: 516px; height: 290px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">打压风险</div>\r\n          <div class=\"bsContentTitleHelp\" @click=\"getRiskDetail\"></div>\r\n          <div\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"openNewTab('http://61.149.6.16:21001/bigScreen')\"\r\n          >\r\n            更多\r\n          </div>\r\n        </div>\r\n        <div class=\"bsContentContent\">\r\n          <timeLine\r\n            :timelineEvents=\"suppressListData\"\r\n            @openEnterpriseInformation=\"openEnterpriseInformation\"\r\n            style=\"width: 516px; height: 247px\"\r\n          ></timeLine>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"center\">\r\n      <div class=\"center-top\">\r\n        <div class=\"top-content\">\r\n          <div class=\"bg1\"></div>\r\n          <div class=\"top-content-number\">\r\n            {{ padWithZeros(gatherTotal, 6) }}\r\n          </div>\r\n          <div class=\"top-content-name\">有效采集量</div>\r\n        </div>\r\n        <div class=\"top-content\">\r\n          <div class=\"bg1\"></div>\r\n          <div class=\"top-content-number\">\r\n            {{ padWithZeros(gatherDayNumber, 6) }}\r\n          </div>\r\n          <div class=\"top-content-name\">当日采集数量</div>\r\n        </div>\r\n      </div>\r\n      <div\r\n        class=\"bsContentBox1\"\r\n        @mouseenter=\"handleMouseEnter2\"\r\n        @mouseleave=\"handleMouseLeave2\"\r\n      >\r\n        <div class=\"bsContentTitle1\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\" style=\"display: flex\">\r\n            <div\r\n              @click=\"zhikuActive = 0\"\r\n              :style=\"{ fontWeight: zhikuActive === 0 ? '800' : '400' }\"\r\n              style=\"cursor: pointer\"\r\n            >\r\n              重点人物分析\r\n            </div>\r\n            <div style=\"margin: 0 4px\">/</div>\r\n            <div\r\n              @click=\"zhikuActive = 1\"\r\n              :style=\"{ fontWeight: zhikuActive === 1 ? '800' : '400' }\"\r\n              style=\"cursor: pointer\"\r\n            >\r\n              产业与技术专题分析\r\n            </div>\r\n          </div>\r\n          <!-- <div class=\"bsContentTitleHelp\" ></div> -->\r\n        </div>\r\n        <div\r\n          class=\"bsContentContent\"\r\n          style=\"display: flex; flex-direction: column; gap: 8px; padding: 8px\"\r\n        >\r\n          <!-- Tab 切换按钮 -->\r\n          <div class=\"tab-buttons\" v-if=\"zhikuActive === 0\">\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'trump' }\"\r\n              @click=\"switchTab('trump')\"\r\n            >\r\n              特朗普\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'msk' }\"\r\n              @click=\"switchTab('msk')\"\r\n            >\r\n              埃隆·里夫·马斯克\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'ws' }\"\r\n              @click=\"switchTab('ws')\"\r\n            >\r\n              詹姆斯·唐纳德·万斯\r\n            </div>\r\n          </div>\r\n          <div class=\"tab-buttons\" v-if=\"zhikuActive === 1\">\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'bdt' }\"\r\n              @click=\"switchTab('bdt', 7)\"\r\n            >\r\n              半导体领域\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'gdzb' }\"\r\n              @click=\"switchTab('gdzb', 8)\"\r\n            >\r\n              高端装备与材料\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'xnyqc' }\"\r\n              @click=\"switchTab('xnyqc', 9)\"\r\n            >\r\n              新能源汽车与电池\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'szhzx' }\"\r\n              @click=\"switchTab('szhzx', 10)\"\r\n            >\r\n              数字化转型与工业软件\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'lszz' }\"\r\n              @click=\"switchTab('lszz', 11)\"\r\n            >\r\n              绿色制造与新能源\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'swyy' }\"\r\n              @click=\"switchTab('swyy', 12)\"\r\n            >\r\n              生物医药与医疗器械\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 人物观点内容（特朗普/马斯克公用） -->\r\n          <div\r\n            v-show=\"\r\n              activeTab === 'trump' || activeTab === 'msk' || activeTab === 'ws'\r\n            \"\r\n            class=\"tab-content\"\r\n          >\r\n            <!-- 上方人物观点树状图 -->\r\n            <div class=\"trump-view-container\">\r\n              <trumpViewTree\r\n                ref=\"characterViewTree\"\r\n                @handleNodeClick=\"handleNodeClick\"\r\n                style=\"width: 100%; height: 300px\"\r\n                :move=\"isHovered2\"\r\n                :currentCharacter=\"activeTab\"\r\n              >\r\n              </trumpViewTree>\r\n            </div>\r\n            <div class=\"view-tree-container\">\r\n              <viewTree\r\n                :treeData=\"characterViewData\"\r\n                :title=\"'renwu'\"\r\n                :visible=\"\r\n                  activeTab === 'trump' ||\r\n                  activeTab === 'msk' ||\r\n                  activeTab === 'ws'\r\n                \"\r\n                style=\"width: 100%; height: 100%\"\r\n                @openNewView=\"openNewView1\"\r\n                @openbaarTreeEcharts=\"openbaarTreeEcharts()\"\r\n              ></viewTree>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 中国制造短板分析内容 -->\r\n          <div\r\n            v-show=\"\r\n              activeTab !== 'trump' && activeTab !== 'msk' && activeTab !== 'ws'\r\n            \"\r\n            class=\"tab-content\"\r\n          >\r\n            <svg ref=\"markmap\" class=\"markmap-svg\"></svg>\r\n          </div>\r\n\r\n          <div\r\n            style=\"\"\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"openbaarTreeEcharts()\"\r\n          >\r\n            更多\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"right\">\r\n      <div class=\"bsContentBox\" style=\"width: 516px; height: 380px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">前沿科技动态</div>\r\n          <div class=\"bsContentTitleMore\" @click=\"qykjdtOpenNewTab()\">更多</div>\r\n        </div>\r\n        <div\r\n          class=\"bsContentContent\"\r\n          @mouseleave=\"handleMouseLeave1\"\r\n          @scroll=\"updateScrollbar1\"\r\n        >\r\n          <div class=\"kejidongtai-box\">\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '脑机接口' }\"\r\n              @click=\"handleButtonClick('脑机接口')\"\r\n            >\r\n              脑机接口\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '量子信息' }\"\r\n              @click=\"handleButtonClick('量子信息')\"\r\n            >\r\n              量子信息\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '人形机器人' }\"\r\n              @click=\"handleButtonClick('人形机器人')\"\r\n            >\r\n              人形机器人\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '生成式人工智能' }\"\r\n              @click=\"handleButtonClick('生成式人工智能')\"\r\n            >\r\n              生成式人工智能\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '生物制造' }\"\r\n              @click=\"handleButtonClick('生物制造')\"\r\n            >\r\n              生物制造\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '未来显示' }\"\r\n              @click=\"handleButtonClick('未来显示')\"\r\n            >\r\n              未来显示\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '未来网络' }\"\r\n              @click=\"handleButtonClick('未来网络')\"\r\n            >\r\n              未来网络\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '新型储能' }\"\r\n              @click=\"handleButtonClick('新型储能')\"\r\n            >\r\n              新型储能\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '其他' }\"\r\n              @click=\"handleButtonClick('其他')\"\r\n            >\r\n              其他\r\n            </div>\r\n          </div>\r\n          <div class=\"remengwenzhang-box1\">\r\n            <div\r\n              class=\"scroll-wrapper\"\r\n              ref=\"scrollWrapper1\"\r\n              @mouseenter=\"handleMouseEnter1\"\r\n            >\r\n              <div class=\"scroll-content\" ref=\"scrollContent1\">\r\n                <div\r\n                  class=\"remengwenzhang-list\"\r\n                  v-for=\"(item, index) in remengwenzhangList1\"\r\n                  :key=\"index\"\r\n                  @click=\"openNewView1(item)\"\r\n                >\r\n                  <div class=\"block\"></div>\r\n                  <div class=\"title\">{{ item.cnTitle }}</div>\r\n                  <div class=\"sourceName\">{{ item.sourceName }}</div>\r\n                  <div class=\"time\">\r\n                    {{ parseTime(item.publishTime, \"{y}-{m}-{d}\") }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- <div class=\"scroll-bar\"></div> -->\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"bsContentBox2\" style=\"width: 516px; height: 600px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">国内外前沿热点技术</div>\r\n          <div\r\n            class=\"bsContentTitleHelp\"\r\n            @click=\"comparisonChartShowModal = true\"\r\n          ></div>\r\n          <div\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"\r\n              openNewTab(\r\n                'http://36.110.223.95:8080/analysis/#/infoQuery/queryManage'\r\n              )\r\n            \"\r\n          >\r\n            更多\r\n          </div>\r\n        </div>\r\n        <div class=\"kejidongtai-box\">\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '11' }\"\r\n            @click=\"handleTechButtonClick('11', '新能源')\"\r\n          >\r\n            新能源\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '12' }\"\r\n            @click=\"handleTechButtonClick('12', '新材料')\"\r\n          >\r\n            新材料\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '13' }\"\r\n            @click=\"handleTechButtonClick('13', '高端装备')\"\r\n          >\r\n            高端装备\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '14' }\"\r\n            @click=\"handleTechButtonClick('14', '新能源汽车')\"\r\n          >\r\n            新能源汽车\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '17' }\"\r\n            @click=\"handleTechButtonClick('17', '船舶与海洋工程装备')\"\r\n          >\r\n            船舶与海洋工程装备\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '16' }\"\r\n            @click=\"handleTechButtonClick('16', '民用航空')\"\r\n          >\r\n            民用航空\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '15' }\"\r\n            @click=\"handleTechButtonClick('15', '绿色环保')\"\r\n          >\r\n            绿色环保\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '18' }\"\r\n            @click=\"handleTechButtonClick('18', '新一代信息技术')\"\r\n          >\r\n            新一代信息技术\r\n          </div>\r\n        </div>\r\n        <!-- <div class=\"bsContentContent\" style=\"height: 188px\">\r\n          <technologyArticles\r\n            :sccenId=\"1\"\r\n            :screenSn=\"currentTechScreenSn\"\r\n            @openHotTechnology=\"openHotTechnology\"\r\n          ></technologyArticles>\r\n        </div> -->\r\n        <div class=\"bsContentContent\" style=\"padding-top: 0px; height: 450px\">\r\n          <graphEcharts\r\n            :sccenId=\"1\"\r\n            :screenSn=\"currentTechScreenSn\"\r\n            @openTechnologyDetails=\"openTechnologyDetails\"\r\n          ></graphEcharts>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <policyRisk\r\n      :visible=\"policyRiskShowModal\"\r\n      :list1=\"policyRiskList1\"\r\n      :list2=\"policyRiskList2\"\r\n      :total=\"policyRiskList1Total\"\r\n      :usa-map-data=\"usaMapData\"\r\n      title=\"美国相关提案\"\r\n      @update:visible=\"policyRiskShowModal = $event\"\r\n      @pagination=\"policyRiskPagination\"\r\n      @openArticleDetail=\"openArticleDetails('policyRisk-news', $event)\"\r\n    >\r\n    </policyRisk>\r\n    <suppressionOfRisks\r\n      :visible=\"suppressionOfRisksShowModal\"\r\n      :levelCount=\"riskBarChartData\"\r\n      :enterpriseList=\"riskEnterpriseList\"\r\n      :total=\"riskEnterpriseListTotal\"\r\n      title=\"打压风险\"\r\n      @update:visible=\"suppressionOfRisksShowModal = $event\"\r\n      @openEnterpriseInformation=\"openEnterpriseInformation\"\r\n      @pagination=\"riskEnterpriseListPagination\"\r\n    >\r\n    </suppressionOfRisks>\r\n    <technologyDetails\r\n      :visible=\"technologyDetailsShowModal\"\r\n      @update:visible=\"technologyDetailsShowModal = $event\"\r\n      @openArticleDetail=\"(e) => openArticleDetails('technology-article', e)\"\r\n      :title=\"technologyDetailsTitle\"\r\n      :item=\"technologyDetailsItem\"\r\n    ></technologyDetails>\r\n    <articleDetails\r\n      :visible=\"articleDetailsShowModal\"\r\n      :title=\"articleDetailsTitle\"\r\n      :content=\"articleDetailsContent\"\r\n      :contentEn=\"articleDetailsContentEn\"\r\n      :item=\"articleDetailsItem\"\r\n      @update:visible=\"articleDetailsShowModal = $event\"\r\n    >\r\n    </articleDetails>\r\n    <enterpriseInformation\r\n      :visible=\"enterpriseInformationShowModal\"\r\n      :title=\"enterpriseInformationTitle\"\r\n      :content=\"enterpriseInformationContent\"\r\n      :patentList=\"patentList\"\r\n      :softwareList=\"softwareList\"\r\n      :total1=\"patentTotal\"\r\n      :total2=\"softwareTotal\"\r\n      @update:visible=\"enterpriseInformationShowModal = $event\"\r\n      @pagination1=\"patentPagination\"\r\n      @pagination2=\"softwarePagination\"\r\n      @openArticleDetail=\"\r\n        (e) => openArticleDetails('enterpriseInformation-news', e)\r\n      \"\r\n    >\r\n    </enterpriseInformation>\r\n    <comparisonChart\r\n      :visible=\"comparisonChartShowModal\"\r\n      @update:visible=\"comparisonChartShowModal = $event\"\r\n      @openHotTechnology=\"openHotTechnology\"\r\n      title=\"前沿技术热点对比图详情\"\r\n    ></comparisonChart>\r\n    <hotTechnology\r\n      :visible=\"hotTechnologyShowModal\"\r\n      :title=\"hotTechnologytTitle\"\r\n      :id=\"hotTechnologytID\"\r\n      @update:visible=\"hotTechnologyShowModal = $event\"\r\n    ></hotTechnology>\r\n    <baarTreeEcharts\r\n      :visible=\"baarTreeEchartsShowModal\"\r\n      :type=\"baarTreeEchartsType\"\r\n      title=\"智库观点\"\r\n      @update:visible=\"baarTreeEchartsShowModal = $event\"\r\n      @openNewView=\"openNewView1\"\r\n    ></baarTreeEcharts>\r\n\r\n    <!-- 热点推荐文章详情弹窗 -->\r\n    <el-dialog\r\n      :title=\"drawerInfo.cnTitle || drawerInfo.title\"\r\n      :visible.sync=\"articleDialogVisible\"\r\n      width=\"80%\"\r\n      :before-close=\"handleClose\"\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\"\r\n    >\r\n      <div class=\"fz\">\r\n        <div class=\"text\">字号：</div>\r\n        <div class=\"btns\">\r\n          <div class=\"btn-minus\" @click=\"decreaseFontSize\">-</div>\r\n          <div class=\"font-size\">{{ fontSize }}px</div>\r\n          <div class=\"btn-plus\" @click=\"increaseFontSize\">+</div>\r\n        </div>\r\n      </div>\r\n      <div\r\n        class=\"dialog-art\"\r\n        :style=\"{ fontSize: fontSize + 'px' }\"\r\n        v-html=\"drawerInfo.cnContent\"\r\n      ></div>\r\n      <el-empty\r\n        description=\"当前文章暂无数据\"\r\n        v-if=\"!drawerInfo.cnContent\"\r\n      ></el-empty>\r\n    </el-dialog>\r\n    <markmap-dialog\r\n      :visible.sync=\"markmapVisible\"\r\n      :content=\"markmapContent\"\r\n      :title=\"markmapTitle\"\r\n      :loading=\"aiLoading\"\r\n      @close=\"handleMarkmapClose\"\r\n    />\r\n\r\n    <!-- 技术领域泡泡图弹窗 -->\r\n    <techBubbleDialog\r\n      :visible=\"techBubbleDialogVisible\"\r\n      :title=\"techBubbleDialogTitle\"\r\n      :screenSn=\"techBubbleDialogScreenSn\"\r\n      @update:visible=\"techBubbleDialogVisible = $event\"\r\n      @openTechnologyDetails=\"openTechnologyDetails\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport usaMap from \"./components/usaMap\";\r\nimport timeLine from \"./components/timeLine\";\r\nimport graphEcharts from \"./components/graphEcharts\";\r\nimport technologyArticles from \"./components/technologyArticles\";\r\nimport trumpViewTree from \"./components/trumpViewTree\";\r\nimport viewTree from \"./components/viewTree\";\r\nimport policyRisk from \"./secondLevel/policyRisk\";\r\nimport articleDetails from \"./secondLevel/articleDetails\";\r\nimport suppressionOfRisks from \"./secondLevel/suppressionOfRisks\";\r\nimport enterpriseInformation from \"./secondLevel/enterpriseInformation\";\r\nimport comparisonChart from \"./secondLevel/comparisonChart\";\r\nimport hotTechnology from \"./secondLevel/hotTechnology\";\r\nimport baarTreeEcharts from \"./components/baarTreeEcharts\";\r\nimport technologyDetails from \"./secondLevel/technologyDetails\";\r\nimport techBubbleDialog from \"./secondLevel/techBubbleDialog\";\r\nimport ArticleNotification from \"@/components/ArticleNotification\";\r\nimport {\r\n  technicalArticleDetail,\r\n  suppressData,\r\n  suppressLevelCount,\r\n  suppressEnterpriseList,\r\n  suppressPatentList,\r\n  suppressSoftwareList,\r\n  proposalsList,\r\n  proposalsToChinaData,\r\n  proposalsCount,\r\n  kjdtArticleList,\r\n  loginSINA,\r\n} from \"@/api/bigScreen/sanhao.js\";\r\nimport {\r\n  largeHotQueryById,\r\n  largeGatherQueryGatherData,\r\n  getLargeFTT,\r\n  largeHotList2,\r\n} from \"@/api/bigScreen/index1\";\r\nimport { markObj } from \"./data/zhiku.js\";\r\nimport { treeData2, markdownData } from \"./data/renwu.js\";\r\nimport MarkmapDialog from \"../bigScreenThree/components/MarkmapDialog.vue\";\r\nimport {\r\n  containsHtmlTags,\r\n  extractHtmlTags,\r\n  hasValidHtmlStructure,\r\n} from \"@/utils/htmlUtils\";\r\nimport { Transformer } from \"markmap-lib\";\r\nimport { Markmap } from \"markmap-view\";\r\n\r\nexport default {\r\n  name: \"TabOne\",\r\n  components: {\r\n    usaMap,\r\n    timeLine,\r\n    graphEcharts,\r\n    technologyArticles,\r\n    trumpViewTree,\r\n    viewTree,\r\n    policyRisk,\r\n    articleDetails,\r\n    suppressionOfRisks,\r\n    enterpriseInformation,\r\n    comparisonChart,\r\n    hotTechnology,\r\n    baarTreeEcharts,\r\n    technologyDetails,\r\n    techBubbleDialog,\r\n    MarkmapDialog,\r\n    ArticleNotification,\r\n  },\r\n  props: {\r\n    notificationArticles: {\r\n      type: Array,\r\n      default: () => [],\r\n    },\r\n    showNotification: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      policyRiskShowModal: false,\r\n      comparisonChartShowModal: false,\r\n      hotTechnologyShowModal: false,\r\n      hotTechnologytTitle: \"\",\r\n      hotTechnologytID: null,\r\n      baarTreeEchartsShowModal: false,\r\n      baarTreeEchartsType: null,\r\n      technologyDetailsShowModal: false,\r\n      technologyDetailsTitle: \"\",\r\n      technologyDetailsItem: null,\r\n      suppressionOfRisksShowModal: false,\r\n      enterpriseInformationShowModal: false,\r\n      enterpriseInformationTitle: \"\",\r\n      articleDetailsShowModal: false,\r\n      articleDetailsTitle: \"\",\r\n      articleDetailsContent: \"\",\r\n      articleDetailsContentEn: \"\",\r\n      suppressListData: [],\r\n      riskBarChartData: [],\r\n      riskEnterpriseList: [],\r\n      riskEnterpriseListTotal: 0,\r\n      enterpriseInformationContent: {},\r\n      patentList: [],\r\n      softwareList: [],\r\n      patentTotal: 0,\r\n      softwareTotal: 0,\r\n      policyRiskList1: [],\r\n      policyRiskList2: [],\r\n      policyRiskList1Total: 0,\r\n      // 美国地图数据\r\n      usaMapData: null,\r\n      articleDetailsItem: {},\r\n      // 热点推荐相关数据\r\n      remengwenzhangList: [],\r\n      scrollTimer: null,\r\n      scrollTimer1: null,\r\n      scrollTimer2: null,\r\n      isHovered: false,\r\n      isHovered1: false,\r\n      isHovered2: false,\r\n      scrollStep: 1,\r\n      drawerInfo: {},\r\n      articleDialogVisible: false,\r\n      fontSize: 16,\r\n      oriFontSize: 20,\r\n      // 人物观点数据\r\n      characterViewData: [],\r\n      // 智库观点数据\r\n      thinkTankViewData: [],\r\n      remengwenzhangList1: [],\r\n      activeButton: null,\r\n      qianyankejiList: [],\r\n      gatherTotal: 0,\r\n      gatherDayNumber: 0,\r\n      markmapVisible: false,\r\n      markmapContent: \"\",\r\n      markmapTitle: \"智库观点\",\r\n      aiLoading: false,\r\n      frontLoginParams: {\r\n        username: \"guanliyuan\",\r\n        password: \"123456\",\r\n      },\r\n      frontToken: \"\",\r\n      // 技术领域相关\r\n      activeTechButton: \"11\", // 默认选中新能源\r\n      currentTechScreenSn: \"11\", // 当前技术领域的screenSn\r\n      // 技术领域泡泡图弹窗相关\r\n      techBubbleDialogVisible: false,\r\n      techBubbleDialogTitle: \"\",\r\n      techBubbleDialogScreenSn: \"\",\r\n      // 智库观点tab切换\r\n      activeTab: \"trump\", // 默认显示特朗普tab\r\n      domainMarkdown: \"\",\r\n      sinaUrl: \"\",\r\n      zhikuActive: 0,\r\n    };\r\n  },\r\n  computed: {\r\n    // 动态计算热门文章列表框的样式\r\n    remengwenzhangBoxStyle() {\r\n      const notificationHeight = 110; // 通知组件的高度\r\n\r\n      if (this.showNotification) {\r\n        return {\r\n          height: `calc(100% - ${notificationHeight}px)`,\r\n        };\r\n      } else {\r\n        return {\r\n          height: `100%`,\r\n        };\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    // 调用登录新浪接口\r\n    loginSINA()\r\n      .then((res) => {\r\n        console.log(\"新浪登录成功\");\r\n        this.sinaUrl = res;\r\n      })\r\n      .catch((error) => {\r\n        console.error(\"新浪登录失败:\", error);\r\n      });\r\n\r\n    this.getSuppressData();\r\n    this.initHotList();\r\n    this.initHotList1();\r\n    this.updateScrollbar();\r\n    this.updateScrollbar1();\r\n    this.fetchUsaMapData();\r\n    largeGatherQueryGatherData({}).then((res) => {\r\n      this.gatherTotal = res.data.gatherTotal;\r\n      this.gatherDayNumber = res.data.gatherDayNumber;\r\n    });\r\n  },\r\n  beforeDestroy() {\r\n    this.clearScrollTimer();\r\n    this.clearScrollTimer1();\r\n    this.clearScrollTimer2();\r\n    this.handleMarkmapClose();\r\n  },\r\n  methods: {\r\n    // 获取美国地图数据\r\n    async fetchUsaMapData() {\r\n      try {\r\n        const response = await proposalsCount({\r\n          projectSn: \"1\",\r\n          screenSn: \"1\",\r\n          columnSn: \"1\",\r\n        });\r\n        this.usaMapData = response;\r\n      } catch (error) {\r\n        console.error(\"获取美国地图数据失败:\", error);\r\n      }\r\n    },\r\n\r\n    openArticleDetails(type, item) {\r\n      this.articleDetailsItem = item;\r\n      switch (type) {\r\n        case \"technology-article\":\r\n          technicalArticleDetail({ id: item.id }).then((res) => {\r\n            this.articleDetailsTitle = item.title;\r\n            this.articleDetailsContent = res.data.content;\r\n            this.articleDetailsContentEn = res.data.enContent;\r\n            this.articleDetailsShowModal = true;\r\n          });\r\n          break;\r\n        case \"enterpriseInformation-news\":\r\n          this.openNewView(item);\r\n          break;\r\n        case \"policyRisk-news\":\r\n          this.openNewView(item);\r\n          break;\r\n      }\r\n    },\r\n    openEnterpriseInformation(item) {\r\n      suppressPatentList({\r\n        suppressSn: item.suppressSn,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      }).then((res) => {\r\n        this.patentList = res.rows;\r\n        this.patentTotal = res.total;\r\n      });\r\n      suppressSoftwareList({\r\n        suppressSn: item.suppressSn,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      }).then((res) => {\r\n        this.softwareList = res.rows;\r\n        this.softwareTotal = res.total;\r\n      });\r\n      this.enterpriseInformationContent = { ...item };\r\n      this.enterpriseInformationTitle = item.enterpriseName;\r\n      this.enterpriseInformationShowModal = true;\r\n    },\r\n\r\n    patentPagination(suppressSn, queryParams) {\r\n      suppressPatentList({\r\n        suppressSn: suppressSn,\r\n        ...queryParams,\r\n      }).then((res) => {\r\n        this.patentList = res.rows;\r\n      });\r\n    },\r\n\r\n    softwarePagination(suppressSn, queryParams) {\r\n      suppressSoftwareList({\r\n        suppressSn: suppressSn,\r\n        ...queryParams,\r\n      }).then((res) => {\r\n        this.softwareList = res.rows;\r\n      });\r\n    },\r\n\r\n    getSuppressData() {\r\n      suppressData({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n      }).then((res) => {\r\n        let data = [];\r\n        Object.keys(res.data).forEach((key) => {\r\n          data.push({\r\n            date: key,\r\n            description:\r\n              res.data[key].length <= 3\r\n                ? res.data[key]\r\n                : res.data[key].slice(\r\n                    res.data[key].length - 3,\r\n                    res.data[key].length\r\n                  ),\r\n          });\r\n        });\r\n        this.suppressListData = data.reverse();\r\n      });\r\n    },\r\n\r\n    getRiskDetail() {\r\n      suppressEnterpriseList({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      }).then((res) => {\r\n        this.riskEnterpriseList = res.rows.map((item, index) => ({\r\n          ...item,\r\n          type: (index % 3) + 1,\r\n        }));\r\n        this.riskEnterpriseListTotal = res.total;\r\n      });\r\n      suppressLevelCount({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n      }).then((res) => {\r\n        // 将对象格式转换为数组格式\r\n        const data = Object.keys(res.data).map((year) => ({\r\n          product: year,\r\n          严重: res.data[year].严重,\r\n          一般: res.data[year].一般,\r\n          较轻: res.data[year].较轻,\r\n        }));\r\n        this.riskBarChartData = data;\r\n        this.suppressionOfRisksShowModal = true;\r\n      });\r\n    },\r\n\r\n    riskEnterpriseListPagination(queryParams) {\r\n      suppressEnterpriseList({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n        ...queryParams,\r\n      }).then((res) => {\r\n        this.riskEnterpriseList = res.rows.map((item, index) => ({\r\n          ...item,\r\n          type: (index % 3) + 1,\r\n        }));\r\n      });\r\n    },\r\n\r\n    getPolicyRiskDetail() {\r\n      proposalsList({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      }).then((res) => {\r\n        this.policyRiskList1 = res.rows;\r\n        this.policyRiskList1Total = res.total;\r\n      });\r\n      proposalsToChinaData({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n      }).then((res) => {\r\n        this.policyRiskList2 = res.data;\r\n        this.policyRiskShowModal = true;\r\n      });\r\n    },\r\n\r\n    policyRiskPagination(queryParams) {\r\n      proposalsList({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n        ...queryParams,\r\n      }).then((res) => {\r\n        this.policyRiskList1 = res.rows;\r\n      });\r\n    },\r\n\r\n    openHotTechnology(data) {\r\n      this.hotTechnologyShowModal = true;\r\n      this.hotTechnologytTitle = data.title;\r\n      this.hotTechnologytID = data.reportSn;\r\n    },\r\n    openbaarTreeEcharts(data) {\r\n      // this.baarTreeEchartsType = parseInt(this.$refs.trumpViewTree.nodeType.replace(/\\D+/g, ''), 10)\r\n      // this.baarTreeEchartsShowModal = true;\r\n\r\n      // 根据当前选中的tab来决定使用哪个markObj\r\n      if (this.activeTab === \"trump\") {\r\n        // 使用导入的markdownData\r\n        this.markmapContent =\r\n          markdownData.trump[this.$refs.characterViewTree.nodeType];\r\n        this.markmapTitle = \"特朗普\";\r\n      } else if (this.activeTab === \"msk\") {\r\n        // 马斯克的markdownData\r\n        this.markmapContent =\r\n          markdownData.msk[this.$refs.characterViewTree.nodeType];\r\n        this.markmapTitle = \"埃隆·里夫·马斯克\";\r\n      } else if (this.activeTab === \"ws\") {\r\n        // 万斯的markdownData\r\n        this.markmapContent =\r\n          markdownData.ws[this.$refs.characterViewTree.nodeType];\r\n        this.markmapTitle = \"詹姆斯·唐纳德·万斯\";\r\n      } else {\r\n        this.markmapContent = this.domainMarkdown;\r\n        switch (this.activeTab) {\r\n          case \"bdt\":\r\n            this.markmapTitle = \"半导体领域\";\r\n            break;\r\n          case \"gdzb\":\r\n            this.markmapTitle = \"高端装备与材料\";\r\n            break;\r\n          case \"xnyqc\":\r\n            this.markmapTitle = \"新能源汽车与电池\";\r\n            break;\r\n          case \"szhzx\":\r\n            this.markmapTitle = \"数字化转型与工业软件\";\r\n            break;\r\n          case \"lszz\":\r\n            this.markmapTitle = \"绿色制造与新能源\";\r\n            break;\r\n          case \"swyy\":\r\n            this.markmapTitle = \"生物医药与医疗器械\";\r\n            break;\r\n        }\r\n      }\r\n      this.aiLoading = false;\r\n      this.markmapVisible = true;\r\n    },\r\n\r\n    openTechnologyDetails(data) {\r\n      this.technologyDetailsShowModal = true;\r\n      this.technologyDetailsTitle = data.name;\r\n      this.technologyDetailsItem = data.data;\r\n    },\r\n\r\n    // 热点推荐相关方法\r\n    initHotList() {\r\n      // 使用bigScreenThree相同的API接口\r\n      largeHotList2()\r\n        .then((res) => {\r\n          this.remengwenzhangList = res.data || [];\r\n          this.$nextTick(() => {\r\n            this.startScroll();\r\n          });\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"获取热点推荐数据失败:\", error);\r\n          // 如果API调用失败，使用空数组\r\n          this.remengwenzhangList = [];\r\n        });\r\n    },\r\n    initHotList1() {\r\n      this.handleButtonClick(\"脑机接口\");\r\n      this.$nextTick(() => {\r\n        this.startScroll2();\r\n      });\r\n    },\r\n\r\n    startScroll() {\r\n      this.clearScrollTimer();\r\n      const wrapper = this.$refs.scrollWrapper;\r\n      const content = this.$refs.scrollContent;\r\n\r\n      if (!wrapper || !content) return;\r\n\r\n      this.scrollTimer = setInterval(() => {\r\n        if (this.isHovered) return;\r\n\r\n        if (wrapper.scrollTop >= content.scrollHeight - wrapper.clientHeight) {\r\n          wrapper.scrollTop = 0;\r\n        } else {\r\n          wrapper.scrollTop += this.scrollStep;\r\n        }\r\n        this.updateScrollbar();\r\n      }, 40);\r\n    },\r\n\r\n    clearScrollTimer() {\r\n      if (this.scrollTimer) {\r\n        clearInterval(this.scrollTimer);\r\n        this.scrollTimer = null;\r\n      }\r\n    },\r\n\r\n    handleMouseEnter() {\r\n      this.isHovered = true;\r\n    },\r\n\r\n    handleMouseLeave() {\r\n      this.isHovered = false;\r\n      this.startScroll();\r\n    },\r\n    startScroll1() {\r\n      this.clearScrollTimer1();\r\n      const wrapper = this.$refs.scrollWrapper1;\r\n      const content = this.$refs.scrollContent1;\r\n\r\n      if (!wrapper || !content) return;\r\n\r\n      this.scrollTimer1 = setInterval(() => {\r\n        if (this.isHovered1) return;\r\n\r\n        if (wrapper.scrollTop >= content.scrollHeight - wrapper.clientHeight) {\r\n          wrapper.scrollTop = 0;\r\n        } else {\r\n          wrapper.scrollTop += this.scrollStep;\r\n        }\r\n        this.updateScrollbar1();\r\n      }, 20);\r\n    },\r\n    startScroll2() {\r\n      this.clearScrollTimer2();\r\n      this.scrollTimer2 = setInterval(() => {\r\n        if (this.isHovered1) return;\r\n\r\n        // 定义所有tab标签的顺序\r\n        const tabOrder = [\r\n          \"脑机接口\",\r\n          \"量子信息\",\r\n          \"人形机器人\",\r\n          \"生成式人工智能\",\r\n          \"生物制造\",\r\n          \"未来显示\",\r\n          \"未来网络\",\r\n          \"新型储能\",\r\n          \"其他\",\r\n        ];\r\n\r\n        // 找到当前活跃标签的索引\r\n        const currentIndex = tabOrder.indexOf(this.activeButton);\r\n        // 计算下一个标签的索引，如果到最后一个则回到第一个\r\n        const nextIndex = (currentIndex + 1) % tabOrder.length;\r\n        // 切换到下一个标签\r\n        this.handleButtonClick(tabOrder[nextIndex]);\r\n      }, 8000);\r\n    },\r\n    clearScrollTimer1() {\r\n      if (this.scrollTimer1) {\r\n        clearInterval(this.scrollTimer1);\r\n        this.scrollTimer1 = null;\r\n      }\r\n    },\r\n    clearScrollTimer2() {\r\n      if (this.scrollTimer2) {\r\n        clearInterval(this.scrollTimer2);\r\n        this.scrollTimer2 = null;\r\n      }\r\n    },\r\n    handleMouseEnter1() {\r\n      this.isHovered1 = true;\r\n    },\r\n\r\n    handleMouseLeave1() {\r\n      this.isHovered1 = false;\r\n      // this.startScroll1();\r\n      // this.startScroll2();\r\n    },\r\n    handleMouseEnter2() {\r\n      this.isHovered2 = true;\r\n    },\r\n\r\n    handleMouseLeave2() {\r\n      this.isHovered2 = false;\r\n    },\r\n    updateScrollbar() {\r\n      const wrapper = this.$refs.scrollWrapper;\r\n      if (!wrapper) return;\r\n\r\n      const { scrollTop, scrollHeight, clientHeight } = wrapper;\r\n      const scrollPercent = clientHeight / scrollHeight;\r\n      const scrollbarHeight = Math.max(30, scrollPercent * clientHeight);\r\n      const scrollbarTop = (scrollTop / scrollHeight) * clientHeight;\r\n\r\n      document.documentElement.style.setProperty(\r\n        \"--scrollbar-height\",\r\n        `${scrollbarHeight}px`\r\n      );\r\n      document.documentElement.style.setProperty(\r\n        \"--scrollbar-top\",\r\n        `${scrollbarTop}px`\r\n      );\r\n    },\r\n    updateScrollbar1() {\r\n      const wrapper = this.$refs.scrollWrapper1;\r\n      if (!wrapper) return;\r\n\r\n      const { scrollTop, scrollHeight, clientHeight } = wrapper;\r\n      const scrollPercent = clientHeight / scrollHeight;\r\n      const scrollbarHeight = Math.max(30, scrollPercent * clientHeight);\r\n      const scrollbarTop = (scrollTop / scrollHeight) * clientHeight;\r\n\r\n      document.documentElement.style.setProperty(\r\n        \"--scrollbar-height\",\r\n        `${scrollbarHeight}px`\r\n      );\r\n      document.documentElement.style.setProperty(\r\n        \"--scrollbar-top\",\r\n        `${scrollbarTop}px`\r\n      );\r\n    },\r\n\r\n    async openNewView(item) {\r\n      // 使用bigScreenThree相同的API接口\r\n      try {\r\n        const res = await largeHotQueryById(item.id);\r\n        this.drawerInfo = {\r\n          cnTitle:\r\n            item.cnTitle || item.title || res.data.title || res.data.cnTitle,\r\n          title:\r\n            item.title || item.cnTitle || res.data.title || res.data.cnTitle,\r\n          cnContent: res.data.content || res.data.cnContent,\r\n        };\r\n\r\n        // 处理内容格式\r\n        let content = this.formattingJson(this.drawerInfo.cnContent);\r\n        // if (content) {\r\n        //   content = content.replace(/\\n/g, \"<br>\");\r\n        //   content = content.replace(/\\${[^}]+}/g, \"<br>\");\r\n        //   content = content.replace(\"|xa0\", \"\");\r\n        //   content = content.replace(\"opacity: 0\", \"\");\r\n        //   content = content.replace(/<img\\b[^>]*>/gi, \"\");\r\n        //   content = content.replace(/ style=\"[^\"]*\"/g, \"\");\r\n        // }\r\n        this.drawerInfo.cnContent = content;\r\n\r\n        this.articleDialogVisible = true;\r\n        this.oriFontSize = this.fontSize;\r\n      } catch (error) {\r\n        console.error(\"获取文章详情失败:\", error);\r\n        // 如果API调用失败，显示基本信息\r\n        this.drawerInfo = {\r\n          cnTitle: item.title || item.cnTitle,\r\n          title: item.title || item.cnTitle,\r\n          cnContent: \"暂无详细内容\",\r\n        };\r\n        this.articleDialogVisible = true;\r\n        this.oriFontSize = this.fontSize;\r\n      }\r\n    },\r\n    async openNewView1(item) {\r\n      // 使用bigScreenThree相同的API接口\r\n      try {\r\n        const res = await getLargeFTT(item.sn);\r\n        this.drawerInfo = {\r\n          cnTitle:\r\n            item.cnTitle || item.title || res.data.title || res.data.cnTitle,\r\n          title:\r\n            item.title || item.cnTitle || res.data.title || res.data.cnTitle,\r\n          cnContent: res.data.content || res.data.cnContent,\r\n        };\r\n\r\n        // 处理内容格式\r\n        let content = this.formattingJson(this.drawerInfo.cnContent);\r\n        // if (content) {\r\n        //   content = content.replace(/\\n/g, \"<br>\");\r\n        //   content = content.replace(/\\${[^}]+}/g, \"<br>\");\r\n        //   content = content.replace(\"|xa0\", \"\");\r\n        //   content = content.replace(\"opacity: 0\", \"\");\r\n        //   content = content.replace(/<img\\b[^>]*>/gi, \"\");\r\n        //   content = content.replace(/ style=\"[^\"]*\"/g, \"\");\r\n        // }\r\n        this.drawerInfo.cnContent = content;\r\n\r\n        this.articleDialogVisible = true;\r\n        this.oriFontSize = this.fontSize;\r\n      } catch (error) {\r\n        console.error(\"获取文章详情失败:\", error);\r\n        // 如果API调用失败，显示基本信息\r\n        this.drawerInfo = {\r\n          cnTitle: item.title || item.cnTitle,\r\n          title: item.title || item.cnTitle,\r\n          cnContent: \"暂无详细内容\",\r\n        };\r\n        this.articleDialogVisible = true;\r\n        this.oriFontSize = this.fontSize;\r\n      }\r\n    },\r\n    formattingJson(content) {\r\n      if (content) {\r\n        if (containsHtmlTags(content)) {\r\n          content = content.replace(/<br>/g, \"\");\r\n          content = content.replace(/\\n/g, \"\");\r\n          content = content.replace(/\\\\n/g, \"\");\r\n          content = content.replace(/\\\\\\n/g, \"\");\r\n          content = content.replace(\"|xa0\", \"\");\r\n          content = content.replace(\"opacity: 0\", \"\");\r\n          // content = content.replace(/\\${[^}]+}/g, \"\");\r\n          content = content.replace(/<img\\b[^>]*>/gi, \"\");\r\n          // 移除完整的标签（包括开始标签、内容和结束标签）\r\n          content = content.replace(/<figure\\b[^>]*>[\\s\\S]*?<\\/figure>/gi, \"\");\r\n          content = content.replace(/<iframe\\b[^>]*>[\\s\\S]*?<\\/iframe>/gi, \"\");\r\n          content = content.replace(/<video\\b[^>]*>[\\s\\S]*?<\\/video>/gi, \"\");\r\n          // 移除自闭合的iframe和video标签\r\n          content = content.replace(/<iframe\\b[^>]*\\/>/gi, \"\");\r\n          content = content.replace(/<video\\b[^>]*\\/>/gi, \"\");\r\n          // cnx标签（假设也需要完整移除）\r\n          content = content.replace(/<cnx\\b[^>]*>[\\s\\S]*?<\\/cnx>/gi, \"\");\r\n          content = content.replace(/<cnx\\b[^>]*\\/>/gi, \"\");\r\n          // 移除带样式的标签，保留内容\r\n          content = content.replace(\r\n            /<(\\w+)[^>]*style=\"[^\"]*\"[^>]*>(.*?)<\\/\\1>/gi,\r\n            \"$2\"\r\n          );\r\n          // 移除任何其他样式标签\r\n          content = content.replace(\r\n            /<(\\w+)[^>]*class=\"[^\"]*\"[^>]*>(.*?)<\\/\\1>/gi,\r\n            \"$2\"\r\n          );\r\n\r\n          console.log(\"包含的HTML标签\", extractHtmlTags(content));\r\n          console.log(\"HTML是否结构正确\", hasValidHtmlStructure(content));\r\n        } else {\r\n          content = content.replace(/\\n/g, \"<br>\");\r\n          content = content.replace(/\\\\n/g, \"<br>\");\r\n          content = content.replace(/\\\\\\n/g, \"<br>\");\r\n          content = content.replace(/\\${[^}]+}/g, \"<br>\");\r\n          content = content.replace(\"|xa0\", \"\");\r\n          content = content.replace(\"opacity: 0\", \"\");\r\n          content = content.replace(/<img\\b[^>]*>/gi, \"\");\r\n          // 移除完整的标签（包括开始标签、内容和结束标签）\r\n          content = content.replace(/<figure\\b[^>]*>[\\s\\S]*?<\\/figure>/gi, \"\");\r\n          content = content.replace(/<iframe\\b[^>]*>[\\s\\S]*?<\\/iframe>/gi, \"\");\r\n          content = content.replace(/<video\\b[^>]*>[\\s\\S]*?<\\/video>/gi, \"\");\r\n          // 移除自闭合的iframe和video标签\r\n          content = content.replace(/<iframe\\b[^>]*\\/>/gi, \"\");\r\n          content = content.replace(/<video\\b[^>]*\\/>/gi, \"\");\r\n          // cnx标签（假设也需要完整移除）\r\n          content = content.replace(/<cnx\\b[^>]*>[\\s\\S]*?<\\/cnx>/gi, \"\");\r\n          content = content.replace(/<cnx\\b[^>]*\\/>/gi, \"\");\r\n          content = content.replace(\r\n            /<(\\w+)[^>]*style=\"[^\"]*\"[^>]*>(.*?)<\\/\\1>/gi,\r\n            \"$2\"\r\n          );\r\n          // 移除任何其他样式标签\r\n          content = content.replace(\r\n            /<(\\w+)[^>]*class=\"[^\"]*\"[^>]*>(.*?)<\\/\\1>/gi,\r\n            \"$2\"\r\n          );\r\n        }\r\n      }\r\n      return content;\r\n    },\r\n\r\n    handleClose() {\r\n      this.drawerInfo = {};\r\n      this.articleDialogVisible = false;\r\n    },\r\n\r\n    increaseFontSize() {\r\n      if (this.fontSize < 30) {\r\n        this.fontSize += 2;\r\n      }\r\n    },\r\n\r\n    decreaseFontSize() {\r\n      if (this.fontSize > 16) {\r\n        this.fontSize -= 2;\r\n      }\r\n    },\r\n    handleNodeClick(type) {\r\n      // 根据当前activeTab获取对应人物的数据\r\n      let currentCharacter = \"trump\"; // 默认特朗普\r\n      if (this.activeTab === \"msk\") {\r\n        currentCharacter = \"msk\";\r\n      } else if (this.activeTab === \"ws\") {\r\n        currentCharacter = \"ws\";\r\n      }\r\n      let rawData = JSON.parse(\r\n        JSON.stringify(treeData2[currentCharacter][type] || [])\r\n      );\r\n      this.characterViewData = this.limitLevel3Children(rawData);\r\n    },\r\n    limitLevel3Children(data) {\r\n      if (!data || !Array.isArray(data)) return data;\r\n      return data.map((item) => {\r\n        if (\r\n          (item.type == \"level2-1\" ||\r\n            item.type == \"level2-2\" ||\r\n            item.type == \"level2-3\") &&\r\n          Array.isArray(item.children)\r\n        ) {\r\n          item.children = item.children.slice(0, 2); // 只保留前两个\r\n        }\r\n\r\n        if (item.children) {\r\n          item.children = this.limitLevel3Children(item.children);\r\n        }\r\n\r\n        return item;\r\n      });\r\n    },\r\n    handleButtonClick(type) {\r\n      let obj = {\r\n        脑机接口: \"3\",\r\n        量子信息: \"4\",\r\n        人形机器人: \"6\",\r\n        生成式人工智能: \"1\",\r\n        生物制造: \"7\",\r\n        未来显示: \"8\",\r\n        未来网络: \"9\",\r\n        新型储能: \"10\",\r\n        其他: \"2,5,11,12,13,14,15,16,17\",\r\n      };\r\n      this.activeButton = type;\r\n\r\n      // 重置轮播时间\r\n      this.startScroll2();\r\n\r\n      kjdtArticleList({\r\n        labelSn: obj[type],\r\n      }).then((res) => {\r\n        // 对数据进行去重处理，基于cnTitle去除空格后判断\r\n        const deduplicatedData = this.deduplicateArticles(res || []);\r\n        this.remengwenzhangList1 = deduplicatedData;\r\n        this.$nextTick(() => {\r\n          const wrapper = this.$refs.scrollWrapper1;\r\n          wrapper.scrollTop = 0;\r\n        });\r\n      });\r\n    },\r\n    qykjdtOpenNewTab() {\r\n      let obj = {\r\n        脑机接口: \"/qianyankejidongtai/naojijiekou?id=1&domain=3\",\r\n        量子信息: \"/qianyankejidongtai/liangzixinxi?id=1&domain=4\",\r\n        人形机器人: \"/qianyankejidongtai/renxingjiqiren?id=1&domain=6\",\r\n        生成式人工智能: \"/qianyankejidongtai/rengongzhineng?id=1&domain=1\",\r\n        生物制造: \"/qianyankejidongtai/shengwuzhizao?id=1&domain=7\",\r\n        未来显示: \"/qianyankejidongtai/weilaixianshi?id=1&domain=8\",\r\n        未来网络: \"/qianyankejidongtai/weilaiwangluo?id=1&domain=9\",\r\n        新型储能: \"/qianyankejidongtai/xinxingchuneng?id=1&domain=10\",\r\n        其他: \"/qianyankejidongtai/qita?id=1&domain=2,5,11,12,13,14,15,16,17\",\r\n      };\r\n      window.open(obj[this.activeButton], \"_blank\");\r\n    },\r\n    // 文章去重方法，基于cnTitle去除空格后判断\r\n    deduplicateArticles(articles) {\r\n      if (!Array.isArray(articles)) {\r\n        return [];\r\n      }\r\n\r\n      const seen = new Set();\r\n      const result = [];\r\n\r\n      articles.forEach((article) => {\r\n        if (article && article.cnTitle) {\r\n          // 去除cnTitle中的所有空格\r\n          const normalizedTitle = article.cnTitle.replace(/\\s+/g, \"\");\r\n\r\n          if (!seen.has(normalizedTitle)) {\r\n            seen.add(normalizedTitle);\r\n            result.push(article);\r\n          }\r\n        } else {\r\n          // 如果没有cnTitle，也保留这条记录\r\n          result.push(article);\r\n        }\r\n      });\r\n\r\n      return result;\r\n    },\r\n    padWithZeros(num, targetLength) {\r\n      const numStr = num.toString();\r\n      const padding = \"0\".repeat(targetLength - numStr.length);\r\n      return `${padding}${numStr}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\r\n    },\r\n    openNewTab(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n    handleMarkmapClose() {\r\n      this.markmapContent = \"\";\r\n      this.aiLoading = false;\r\n      this.markmapVisible = false;\r\n    },\r\n\r\n    // 更新热点推荐文章列表\r\n    async updateHotArticlesList() {\r\n      try {\r\n        const response = await largeHotList2();\r\n        if (response && response.data) {\r\n          const newArticles = response.data;\r\n          // 对比数据是否一致\r\n          if (this.isArticleDataChanged(newArticles)) {\r\n            this.remengwenzhangList = newArticles;\r\n          } else {\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"更新热点推荐文章列表失败:\", error);\r\n      }\r\n    },\r\n\r\n    // 检查文章数据是否发生变化\r\n    isArticleDataChanged(newArticles) {\r\n      // 如果当前列表为空，直接返回true\r\n      if (this.remengwenzhangList.length === 0) {\r\n        return newArticles.length > 0;\r\n      }\r\n\r\n      // 如果数量不同，说明有变化\r\n      if (this.remengwenzhangList.length !== newArticles.length) {\r\n        return true;\r\n      }\r\n\r\n      // 对比每篇文章的关键信息\r\n      for (let i = 0; i < newArticles.length; i++) {\r\n        const newArticle = newArticles[i];\r\n        const oldArticle = this.remengwenzhangList[i];\r\n\r\n        // 对比文章ID、标题、发布时间等关键字段\r\n        if (\r\n          newArticle.id !== oldArticle.id ||\r\n          newArticle.title !== oldArticle.title ||\r\n          newArticle.publishTime !== oldArticle.publishTime ||\r\n          newArticle.sourceName !== oldArticle.sourceName\r\n        ) {\r\n          return true;\r\n        }\r\n      }\r\n\r\n      // 所有数据都一致\r\n      return false;\r\n    },\r\n\r\n    // 处理技术领域按钮点击\r\n    handleTechButtonClick(screenSn, buttonName) {\r\n      console.log(\"切换技术领域:\", buttonName, \"screenSn:\", screenSn);\r\n      this.activeTechButton = screenSn;\r\n      this.currentTechScreenSn = screenSn;\r\n      // 弹出技术领域泡泡图弹窗\r\n      this.techBubbleDialogVisible = true;\r\n      this.techBubbleDialogTitle = buttonName;\r\n      this.techBubbleDialogScreenSn = screenSn;\r\n\r\n      // 通知子组件更新数据\r\n      this.$nextTick(() => {\r\n        // 可以通过ref直接调用子组件的方法来刷新数据\r\n        // 或者通过watch监听currentTechScreenSn的变化来触发子组件更新\r\n      });\r\n    },\r\n\r\n    // 关闭技术领域泡泡图弹窗\r\n    handleTechBubbleDialogClose() {\r\n      this.techBubbleDialogVisible = false;\r\n      this.techBubbleDialogTitle = \"\";\r\n      this.techBubbleDialogScreenSn = \"\";\r\n    },\r\n\r\n    // 处理通知关闭\r\n    handleNotificationClose() {\r\n      this.$emit(\"notification-close\");\r\n    },\r\n\r\n    // 处理查看单篇文章\r\n    handleViewArticle(article) {\r\n      this.$emit(\"notification-view-article\", article);\r\n    },\r\n\r\n    // 切换智库观点tab\r\n    switchTab(tabName, markdownType) {\r\n      if (markdownType) {\r\n        this.activeTab = tabName;\r\n        this.domainMarkdown = markObj[\"type\" + markdownType];\r\n        this.renderMarkmap();\r\n      } else if (tabName === \"trump\" || tabName === \"msk\" || tabName === \"ws\") {\r\n        // 如果点击的是当前已激活的人物tab，需要重新触发数据加载\r\n        if (this.activeTab === tabName) {\r\n          // 清空数据\r\n          this.characterViewData = [];\r\n          // 通过ref直接调用trumpViewTree组件的方法来重新初始化\r\n          this.$nextTick(() => {\r\n            if (\r\n              this.$refs.characterViewTree &&\r\n              this.$refs.characterViewTree.allTypes.length > 0\r\n            ) {\r\n              this.$refs.characterViewTree.handleNodeClick(\r\n                this.$refs.characterViewTree.allTypes[0]\r\n              );\r\n            }\r\n          });\r\n        } else {\r\n          // 切换到不同的人物tab时，设置activeTab，让watcher自动处理\r\n          this.activeTab = tabName;\r\n          this.characterViewData = [];\r\n        }\r\n      }\r\n    },\r\n    async renderMarkmap() {\r\n      if (!this.domainMarkdown) {\r\n        this.loading = false;\r\n        return;\r\n      }\r\n\r\n      try {\r\n        await this.$nextTick();\r\n        const svg = this.$refs.markmap;\r\n        if (!svg) {\r\n          throw new Error(\"SVG element not found\");\r\n        }\r\n\r\n        // 清空之前的内容\r\n        svg.innerHTML = \"\";\r\n\r\n        // 处理内容，移除 markdown 标记\r\n        let processedContent = this.domainMarkdown\r\n          .replace(/^```markdown\\s*/i, \"\") // 移除开头的 ```markdown\r\n          .replace(/\\s*```\\s*$/, \"\"); // 移除结尾的 ```\r\n\r\n        const transformer = new Transformer();\r\n        const { root } = transformer.transform(processedContent);\r\n\r\n        // 创建思维导图\r\n        const mm = Markmap.create(\r\n          svg,\r\n          {\r\n            autoFit: true,\r\n            duration: 0,\r\n            nodeMinHeight: 20,\r\n            spacingVertical: 10,\r\n            spacingHorizontal: 100,\r\n            paddingX: 20,\r\n            color: (node) => {\r\n              const colors = {\r\n                0: \"#0052ff\", // 亮蓝色\r\n                1: \"#009600\", // 亮绿色\r\n                2: \"#ff6600\", // 亮橙色\r\n                3: \"#8000ff\", // 亮紫色\r\n                4: \"#ff0066\", // 亮粉色\r\n              };\r\n              return colors[node.depth] || \"#0052ff\";\r\n            },\r\n            nodeFont: (node) => {\r\n              const fonts = {\r\n                0: 'bold 20px/1.5 -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto',\r\n                1: '600 18px/1.5 -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto',\r\n                2: '500 16px/1.5 -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto',\r\n              };\r\n              return (\r\n                fonts[node.depth] ||\r\n                '400 14px/1.5 -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto'\r\n              );\r\n            },\r\n            maxWidth: 400,\r\n            initialExpandLevel: -1,\r\n            zoom: true,\r\n            pan: true,\r\n            linkShape: \"diagonal\",\r\n            linkWidth: (node) => 2.5 - node.depth * 0.5,\r\n            linkColor: (node) => {\r\n              const colors = {\r\n                0: \"rgba(0, 82, 255, 0.8)\", // 亮蓝色\r\n                1: \"rgba(0, 150, 0, 0.8)\", // 亮绿色\r\n                2: \"rgba(255, 102, 0, 0.8)\", // 亮橙色\r\n              };\r\n              return colors[node.depth] || \"rgba(128, 0, 255, 0.8)\";\r\n            },\r\n          },\r\n          root\r\n        );\r\n\r\n        // 修改初始化动画部分\r\n        setTimeout(() => {\r\n          mm.fit(); // 适应视图大小\r\n\r\n          // 重新设置数据以触发重绘\r\n          const fitRatio = 0.95; // 留出一些边距\r\n          const { minX, maxX, minY, maxY } = mm.state;\r\n          const width = maxX - minX;\r\n          const height = maxY - minY;\r\n          const containerWidth = svg.clientWidth;\r\n          const containerHeight = svg.clientHeight;\r\n\r\n          // 计算合适的缩放比例\r\n          const scale = Math.min(\r\n            (containerWidth / width) * fitRatio,\r\n            (containerHeight / height) * fitRatio\r\n          );\r\n\r\n          // 更新数据以应用新的缩放\r\n          mm.setData(root, {\r\n            initialScale: scale,\r\n            initialPosition: [\r\n              (containerWidth - width * scale) / 2,\r\n              (containerHeight - height * scale) / 2,\r\n            ],\r\n          });\r\n        }, 100);\r\n\r\n        // 监听窗口大小变化\r\n        const resizeHandler = () => mm.fit();\r\n        window.addEventListener(\"resize\", resizeHandler);\r\n\r\n        // 组件销毁时清理\r\n        this.$once(\"hook:beforeDestroy\", () => {\r\n          window.removeEventListener(\"resize\", resizeHandler);\r\n        });\r\n      } catch (error) {\r\n        console.error(\"Markmap rendering error:\", error);\r\n        this.$message.error(\"思维导图渲染失败\");\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    // 打开新浪舆情通\r\n    openSina() {\r\n      window.open(this.sinaUrl, \"_blank\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.two {\r\n  height: 100%;\r\n  width: 100%;\r\n  padding-bottom: 10px;\r\n\r\n  .left {\r\n    width: 520px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .center {\r\n    margin: 0 11px;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n\r\n    .center-top {\r\n      height: 150px;\r\n      display: flex;\r\n      justify-content: space-around;\r\n      align-items: center;\r\n\r\n      .top-content {\r\n        position: relative;\r\n        width: 315px;\r\n        height: 98px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/centerBg1.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bg1 {\r\n        position: absolute;\r\n        top: 17px;\r\n        left: 43px;\r\n        width: 60px;\r\n        height: 60px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/centerBg2.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .top-content-number {\r\n        font-size: 24px;\r\n        color: #00e5ff;\r\n        position: absolute;\r\n        left: 138px;\r\n        top: 44px;\r\n      }\r\n\r\n      .top-content-name {\r\n        font-size: 18px;\r\n        color: #ffffff;\r\n        position: absolute;\r\n        left: 138px;\r\n        top: 22px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .right {\r\n    width: 520px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n\r\n    .bsContentBox2 {\r\n      background-image: url(\"../../assets/bigScreenSanhao/contentBg1.png\");\r\n\r\n      .bsContentContent {\r\n        height: calc((100% - 43px) / 2);\r\n      }\r\n\r\n      .kejidongtai-box {\r\n        margin-top: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  .bsContentBox,\r\n  .bsContentBox2 {\r\n    background-image: url(\"../../assets/bigScreenSanhao/contentBg.png\");\r\n    background-size: 100% 100%;\r\n\r\n    .bsContentTitle {\r\n      height: 43px;\r\n      display: flex;\r\n      align-items: center;\r\n      padding-left: 10px;\r\n      position: relative;\r\n\r\n      .bsContentTitleIcon {\r\n        width: 22px;\r\n        height: 22px;\r\n        margin-right: 10px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/titleLogo.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bsContentTitleName {\r\n        height: 43px;\r\n        line-height: 43px;\r\n        font-weight: 800;\r\n        font-size: 20px;\r\n        color: #00abf4;\r\n      }\r\n\r\n      .bsContentTitleHelp {\r\n        cursor: pointer;\r\n        width: 21px;\r\n        height: 21px;\r\n        margin-left: 10px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/titleHelp.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bsContentTitleMore {\r\n        position: absolute;\r\n        right: 10px;\r\n        cursor: pointer;\r\n        width: 70px;\r\n        height: 43px;\r\n        line-height: 60px;\r\n        font-size: 17px;\r\n        color: #00c8ff;\r\n\r\n        &:after {\r\n          content: \"\";\r\n          display: inline-block;\r\n          position: absolute;\r\n          top: 22px;\r\n          width: 18px;\r\n          height: 18px;\r\n          margin-left: 5px;\r\n          background-image: url(\"../../assets/bigScreenSanhao/jiantou.png\");\r\n          background-size: 100% 100%;\r\n        }\r\n      }\r\n    }\r\n\r\n    .bsContentContent {\r\n      height: calc(100% - 43px);\r\n    }\r\n  }\r\n\r\n  .bsContentBox1 {\r\n    flex: 1;\r\n\r\n    .bsContentTitle1 {\r\n      height: 43px;\r\n      display: flex;\r\n      align-items: center;\r\n      padding-left: 10px;\r\n      background-image: url(\"../../assets/bigScreenSanhao/title1.png\");\r\n      background-size: 100% 100%;\r\n      position: relative;\r\n\r\n      .bsContentTitleIcon {\r\n        width: 22px;\r\n        height: 22px;\r\n        margin-right: 10px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/titleLogo.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bsContentTitleName {\r\n        height: 43px;\r\n        line-height: 43px;\r\n        font-weight: 800;\r\n        font-size: 20px;\r\n        color: #00abf4;\r\n\r\n        span {\r\n          font-weight: normal;\r\n          cursor: pointer;\r\n          color: rgba(0, 171, 244, 0.5);\r\n        }\r\n\r\n        .titleColor {\r\n          font-weight: 800;\r\n          color: #ffffff;\r\n        }\r\n      }\r\n\r\n      .bsContentTitleHelp {\r\n        cursor: pointer;\r\n        width: 21px;\r\n        height: 21px;\r\n        margin-left: 10px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/titleHelp.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bsContentTitleMore {\r\n        position: absolute;\r\n        right: 10px;\r\n        cursor: pointer;\r\n        width: 70px;\r\n        height: 43px;\r\n        line-height: 60px;\r\n        font-size: 17px;\r\n        color: #00c8ff;\r\n\r\n        &:after {\r\n          content: \"\";\r\n          display: inline-block;\r\n          position: absolute;\r\n          top: 22px;\r\n          width: 18px;\r\n          height: 18px;\r\n          margin-left: 5px;\r\n          background-image: url(\"../../assets/bigScreenSanhao/jiantou.png\");\r\n          background-size: 100% 100%;\r\n        }\r\n      }\r\n    }\r\n\r\n    .bsContentContent {\r\n      height: calc(100% - 43px);\r\n      display: flex;\r\n      flex-direction: column;\r\n      position: relative;\r\n\r\n      .trump-view-container {\r\n        height: 300px;\r\n      }\r\n\r\n      .view-tree-container {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        & > div {\r\n          flex: 1;\r\n        }\r\n      }\r\n\r\n      .bsContentTitleMore {\r\n        position: absolute;\r\n        bottom: 0;\r\n        right: 0;\r\n        z-index: 99;\r\n        cursor: pointer;\r\n        width: 70px;\r\n        height: 43px;\r\n        line-height: 60px;\r\n        font-size: 17px;\r\n        color: #00c8ff;\r\n\r\n        &:after {\r\n          content: \"\";\r\n          display: inline-block;\r\n          position: absolute;\r\n          top: 22px;\r\n          width: 18px;\r\n          height: 18px;\r\n          margin-left: 5px;\r\n          background-image: url(\"../../assets/bigScreenSanhao/jiantou.png\");\r\n          background-size: 100% 100%;\r\n        }\r\n      }\r\n\r\n      // Tab 按钮样式\r\n      .tab-buttons {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        gap: 10px;\r\n\r\n        .tab-button {\r\n          padding: 8px 8px;\r\n          background: rgba(0, 171, 244, 0.2);\r\n          border: 1px solid rgba(0, 171, 244, 0.5);\r\n          border-radius: 4px;\r\n          color: rgba(0, 171, 244, 0.8);\r\n          cursor: pointer;\r\n          font-size: 14px;\r\n          transition: all 0.3s ease;\r\n\r\n          &:hover {\r\n            background: rgba(0, 171, 244, 0.3);\r\n            color: #00abf4;\r\n          }\r\n\r\n          &.active {\r\n            background: rgba(0, 171, 244, 0.5);\r\n            color: #ffffff;\r\n            border-color: #00abf4;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Tab 内容样式\r\n      .tab-content {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        .trump-view-container {\r\n          height: 300px;\r\n        }\r\n\r\n        .view-tree-container {\r\n          flex: 1;\r\n          display: flex;\r\n          flex-direction: column;\r\n\r\n          & > div {\r\n            flex: 1;\r\n          }\r\n        }\r\n\r\n        .markmap-svg {\r\n          width: 100%;\r\n          height: 100%;\r\n          display: block;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 热点推荐滚动列表样式\r\n  .remengwenzhang-box {\r\n    width: 100%;\r\n    height: 100%;\r\n    padding: 20px;\r\n    border: 1px solid rgba(16, 216, 255, 0.4);\r\n    background: rgba(0, 0, 0, 0.15);\r\n    box-shadow: 0px 0px 8px 0px #0056ad;\r\n    overflow: hidden;\r\n    position: relative;\r\n\r\n    .scroll-wrapper {\r\n      height: 100%;\r\n      overflow-y: scroll;\r\n      overflow-x: hidden;\r\n      position: relative;\r\n\r\n      scrollbar-width: none;\r\n      -ms-overflow-style: none;\r\n\r\n      &::-webkit-scrollbar {\r\n        display: none;\r\n      }\r\n    }\r\n\r\n    &::after {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 20px;\r\n      right: 0;\r\n      height: calc(100% - 40px);\r\n      width: 6px;\r\n      background: rgba(16, 216, 255, 0.1);\r\n      opacity: 0;\r\n      transition: opacity 0.3s;\r\n      pointer-events: none;\r\n    }\r\n\r\n    .scroll-bar {\r\n      position: absolute;\r\n      top: 20px;\r\n      right: 0;\r\n      width: 6px;\r\n      height: var(--scrollbar-height, 100px);\r\n      background: rgba(16, 216, 255, 0.4);\r\n      border-radius: 3px;\r\n      opacity: 0;\r\n      transition: opacity 0.3s;\r\n      transform: translateY(var(--scrollbar-top, 0));\r\n      pointer-events: none;\r\n    }\r\n\r\n    &:hover {\r\n      &::after,\r\n      .scroll-bar {\r\n        opacity: 1;\r\n      }\r\n    }\r\n\r\n    .remengwenzhang-list {\r\n      position: relative;\r\n      height: 40px;\r\n      padding-left: 20px;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      cursor: pointer;\r\n\r\n      .title {\r\n        width: 330px;\r\n        overflow: hidden;\r\n        color: rgba(216, 240, 255, 0.8);\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n        font-family: \"Source Han Sans CN\";\r\n        font-size: 18px;\r\n        font-weight: 700;\r\n        line-height: 20px;\r\n      }\r\n\r\n      .time,\r\n      .sourceName {\r\n        width: 150px;\r\n        color: rgba(216, 240, 255, 0.8);\r\n        text-align: right;\r\n        font-family: \"Source Han Sans CN\";\r\n        font-size: 18px;\r\n        font-weight: 400;\r\n        line-height: 20px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .block {\r\n        position: absolute;\r\n        left: 0px;\r\n        top: 6px;\r\n        width: 10px;\r\n        height: 10px;\r\n        border-radius: 1px;\r\n        background: #1bdcff;\r\n      }\r\n    }\r\n  }\r\n\r\n  .remengwenzhang-box1 {\r\n    width: 100%;\r\n    height: 230px;\r\n    padding: 20px;\r\n    border: 1px solid rgba(16, 216, 255, 0.4);\r\n    background: rgba(0, 0, 0, 0.15);\r\n    box-shadow: 0px 0px 8px 0px #0056ad;\r\n    overflow: hidden;\r\n    position: relative;\r\n\r\n    .scroll-wrapper {\r\n      height: 100%;\r\n      overflow-y: scroll;\r\n      overflow-x: hidden;\r\n      position: relative;\r\n\r\n      scrollbar-width: none;\r\n      -ms-overflow-style: none;\r\n\r\n      &::-webkit-scrollbar {\r\n        display: none;\r\n      }\r\n    }\r\n\r\n    &::after {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 20px;\r\n      right: 0;\r\n      height: calc(100% - 40px);\r\n      width: 6px;\r\n      background: rgba(16, 216, 255, 0.1);\r\n      opacity: 0;\r\n      transition: opacity 0.3s;\r\n      pointer-events: none;\r\n    }\r\n\r\n    .scroll-bar {\r\n      position: absolute;\r\n      top: 20px;\r\n      right: 0;\r\n      width: 6px;\r\n      height: var(--scrollbar-height, 100px);\r\n      background: rgba(16, 216, 255, 0.4);\r\n      border-radius: 3px;\r\n      opacity: 0;\r\n      transition: opacity 0.3s;\r\n      transform: translateY(var(--scrollbar-top, 0));\r\n      pointer-events: none;\r\n    }\r\n\r\n    &:hover {\r\n      &::after,\r\n      .scroll-bar {\r\n        opacity: 1;\r\n      }\r\n    }\r\n\r\n    .remengwenzhang-list {\r\n      position: relative;\r\n      height: 40px;\r\n      padding-left: 20px;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      cursor: pointer;\r\n\r\n      .title {\r\n        width: 330px;\r\n        overflow: hidden;\r\n        color: rgba(216, 240, 255, 0.8);\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n        font-family: \"Source Han Sans CN\";\r\n        font-size: 18px;\r\n        font-weight: 700;\r\n        line-height: 20px;\r\n      }\r\n\r\n      .time,\r\n      .sourceName {\r\n        width: 150px;\r\n        color: rgba(216, 240, 255, 0.8);\r\n        text-align: right;\r\n        font-family: \"Source Han Sans CN\";\r\n        font-size: 18px;\r\n        font-weight: 400;\r\n        line-height: 20px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .block {\r\n        position: absolute;\r\n        left: 0px;\r\n        top: 6px;\r\n        width: 10px;\r\n        height: 10px;\r\n        border-radius: 1px;\r\n        background: #1bdcff;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 弹窗样式\r\n::v-deep .el-dialog {\r\n  background: url(\"../../assets/bigScreenTwo/dialogBackground.png\") no-repeat;\r\n  background-size: 100% 100% !important;\r\n  background-size: cover;\r\n  height: 970px;\r\n\r\n  .el-dialog__header {\r\n    background-color: #1d233400;\r\n    font-size: 30px;\r\n    color: #ffffff;\r\n    line-height: 120px;\r\n    text-shadow: 0px 0px 10px rgba(30, 198, 255, 0.8);\r\n    height: 120px;\r\n\r\n    .el-dialog__title {\r\n      display: inline-block;\r\n      width: calc(100% - 100px);\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n    }\r\n  }\r\n\r\n  .el-dialog__body {\r\n    background-color: #2a304000;\r\n    color: #f2f2f2;\r\n    height: calc(100% - 120px);\r\n    overflow: hidden;\r\n    padding: 20px 30px;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    background-color: #1d233400;\r\n    padding: 18px 20px;\r\n  }\r\n\r\n  .el-button {\r\n    background-color: #002766;\r\n    color: #fff;\r\n    border: 0px;\r\n  }\r\n\r\n  .el-dialog__headerbtn .el-dialog__close {\r\n    background: url(\"../../assets/bigScreenTwo/关闭小.png\") no-repeat;\r\n    background-size: 100% 100% !important;\r\n    background-size: cover;\r\n    width: 31px;\r\n    height: 31px;\r\n    top: 32px;\r\n\r\n    &::before {\r\n      content: none;\r\n    }\r\n  }\r\n}\r\n\r\n.dialog-art {\r\n  background: #1d293b;\r\n  padding: 20px;\r\n  height: calc(100% - 60px) !important;\r\n  overflow-y: auto;\r\n  line-height: 1.8em;\r\n  font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n    Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei, Arial,\r\n    sans-serif;\r\n\r\n  ::v-deep p {\r\n    text-indent: 2em;\r\n  }\r\n}\r\n\r\n/* 自定义滚动条样式 */\r\n::v-deep .dialog-art::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n::v-deep .dialog-art::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 4px;\r\n}\r\n\r\n::v-deep .dialog-art::-webkit-scrollbar-thumb {\r\n  background: rgba(14, 194, 244, 0.6);\r\n  border-radius: 4px;\r\n}\r\n\r\n::v-deep .dialog-art::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(14, 194, 244, 0.8);\r\n}\r\n\r\n.fz {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  margin-bottom: 20px;\r\n\r\n  .text {\r\n    font-weight: 400;\r\n    font-size: 20px;\r\n    color: #ffffff;\r\n    margin-right: 10px;\r\n  }\r\n\r\n  .btns {\r\n    display: flex;\r\n    align-items: center;\r\n    background: #1d293b;\r\n    border-radius: 14px;\r\n    padding: 0 10px;\r\n    height: 28px;\r\n\r\n    .btn-minus,\r\n    .btn-plus {\r\n      width: 24px;\r\n      height: 24px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      cursor: pointer;\r\n      font-size: 20px;\r\n      color: #ffffff;\r\n\r\n      &:hover {\r\n        color: #2f7cfe;\r\n      }\r\n    }\r\n\r\n    .font-size {\r\n      margin: 0 15px;\r\n      color: #ffffff;\r\n      font-size: 16px;\r\n      min-width: 45px;\r\n      text-align: center;\r\n    }\r\n  }\r\n}\r\n\r\n.kejidongtai-box {\r\n  width: 100%;\r\n  // height: 45px;\r\n  // padding-top: 11px;\r\n  // margin: 3px 0;\r\n  display: flex;\r\n  // justify-content: space-around;\r\n  flex-wrap: wrap;\r\n  column-gap: 10px;\r\n  row-gap: 10px;\r\n  padding: 10px;\r\n  margin-top: 10px;\r\n\r\n  .kejidongtai-button {\r\n    // width: 111px;\r\n    height: 33px;\r\n    line-height: 33px;\r\n    text-align: center;\r\n    font-size: 14px;\r\n    color: #ffffff;\r\n    background-image: url(\"../../assets/bigScreenSanhao/kejiqianyan1.png\");\r\n    background-size: 100% 100%;\r\n    padding: 0 12px;\r\n  }\r\n\r\n  .active {\r\n    background-image: url(\"../../assets/bigScreenSanhao/kejiqianyan2.png\");\r\n  }\r\n}\r\n\r\n:deep(.markmap-node) {\r\n  cursor: pointer;\r\n\r\n  &:hover {\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n:deep(.markmap-node-circle) {\r\n  fill: transparent; // 修改节点背景为透明\r\n  stroke-width: 2px;\r\n}\r\n\r\n:deep(.markmap-node-text) {\r\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto,\r\n    \"Helvetica Neue\", Arial;\r\n\r\n  tspan {\r\n    fill: #333 !important; // 修改文字颜色为深色\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n:deep(.markmap-link) {\r\n  fill: none;\r\n  stroke-width: 2.5px; // 加粗连线\r\n}\r\n\r\n// 根节点样式\r\n:deep(.markmap-node[data-depth=\"0\"]) {\r\n  .markmap-node-circle {\r\n    stroke: #0052ff; // 亮蓝色\r\n    stroke-width: 3px;\r\n  }\r\n\r\n  .markmap-node-text tspan {\r\n    font-size: 20px !important;\r\n    font-weight: bold !important;\r\n    fill: #333 !important;\r\n  }\r\n}\r\n\r\n// 二级节点样式\r\n:deep(.markmap-node[data-depth=\"1\"]) {\r\n  .markmap-node-circle {\r\n    stroke: #009600; // 亮绿色\r\n    stroke-width: 2.5px;\r\n  }\r\n\r\n  .markmap-node-text tspan {\r\n    font-size: 18px !important;\r\n    font-weight: 600 !important;\r\n    fill: #333 !important;\r\n  }\r\n}\r\n\r\n// 三级节点样式\r\n:deep(.markmap-node[data-depth=\"2\"]) {\r\n  .markmap-node-circle {\r\n    stroke: #ff6600; // 亮橙色\r\n    stroke-width: 2px;\r\n  }\r\n\r\n  .markmap-node-text tspan {\r\n    font-size: 16px !important;\r\n    font-weight: 500 !important;\r\n    fill: #333 !important;\r\n  }\r\n}\r\n\r\n// 其他层级节点样式\r\n:deep(.markmap-node[data-depth=\"3\"]),\r\n:deep(.markmap-node[data-depth=\"4\"]) {\r\n  .markmap-node-circle {\r\n    stroke: #8000ff; // 亮紫色\r\n    stroke-width: 2px;\r\n  }\r\n\r\n  .markmap-node-text tspan {\r\n    font-size: 14px !important;\r\n    font-weight: 500 !important;\r\n    fill: #333 !important;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8kBA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,aAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,mBAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,cAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,SAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,WAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,eAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,mBAAA,GAAAT,sBAAA,CAAAC,OAAA;AACA,IAAAS,sBAAA,GAAAV,sBAAA,CAAAC,OAAA;AACA,IAAAU,gBAAA,GAAAX,sBAAA,CAAAC,OAAA;AACA,IAAAW,cAAA,GAAAZ,sBAAA,CAAAC,OAAA;AACA,IAAAY,gBAAA,GAAAb,sBAAA,CAAAC,OAAA;AACA,IAAAa,kBAAA,GAAAd,sBAAA,CAAAC,OAAA;AACA,IAAAc,iBAAA,GAAAf,sBAAA,CAAAC,OAAA;AACA,IAAAe,oBAAA,GAAAhB,sBAAA,CAAAC,OAAA;AACA,IAAAgB,OAAA,GAAAhB,OAAA;AAaA,IAAAiB,MAAA,GAAAjB,OAAA;AAMA,IAAAkB,MAAA,GAAAlB,OAAA;AACA,IAAAmB,MAAA,GAAAnB,OAAA;AACA,IAAAoB,cAAA,GAAArB,sBAAA,CAAAC,OAAA;AACA,IAAAqB,UAAA,GAAArB,OAAA;AAKA,IAAAsB,WAAA,GAAAtB,OAAA;AACA,IAAAuB,YAAA,GAAAvB,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAwB,IAAA;EACAC,UAAA;IACAC,MAAA,EAAAA,eAAA;IACAC,QAAA,EAAAA,iBAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,kBAAA,EAAAA,2BAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,QAAA,EAAAA,iBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,kBAAA,EAAAA,2BAAA;IACAC,qBAAA,EAAAA,8BAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,gBAAA,EAAAA,yBAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,mBAAA,EAAAA;EACA;EACAC,KAAA;IACAC,oBAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAC,gBAAA;MACAH,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,mBAAA;MACAC,wBAAA;MACAC,sBAAA;MACAC,mBAAA;MACAC,gBAAA;MACAC,wBAAA;MACAC,mBAAA;MACAC,0BAAA;MACAC,sBAAA;MACAC,qBAAA;MACAC,2BAAA;MACAC,8BAAA;MACAC,0BAAA;MACAC,uBAAA;MACAC,mBAAA;MACAC,qBAAA;MACAC,uBAAA;MACAC,gBAAA;MACAC,gBAAA;MACAC,kBAAA;MACAC,uBAAA;MACAC,4BAAA;MACAC,UAAA;MACAC,YAAA;MACAC,WAAA;MACAC,aAAA;MACAC,eAAA;MACAC,eAAA;MACAC,oBAAA;MACA;MACAC,UAAA;MACAC,kBAAA;MACA;MACAC,kBAAA;MACAC,WAAA;MACAC,YAAA;MACAC,YAAA;MACAC,SAAA;MACAC,UAAA;MACAC,UAAA;MACAC,UAAA;MACAC,UAAA;MACAC,oBAAA;MACAC,QAAA;MACAC,WAAA;MACA;MACAC,iBAAA;MACA;MACAC,iBAAA;MACAC,mBAAA;MACAC,YAAA;MACAC,eAAA;MACAC,WAAA;MACAC,eAAA;MACAC,cAAA;MACAC,cAAA;MACAC,YAAA;MACAC,SAAA;MACAC,gBAAA;QACAC,QAAA;QACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,gBAAA;MAAA;MACAC,mBAAA;MAAA;MACA;MACAC,uBAAA;MACAC,qBAAA;MACAC,wBAAA;MACA;MACAC,SAAA;MAAA;MACAC,cAAA;MACAC,OAAA;MACAC,WAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,sBAAA,WAAAA,uBAAA;MACA,IAAAC,kBAAA;;MAEA,SAAAxE,gBAAA;QACA;UACAyE,MAAA,iBAAAC,MAAA,CAAAF,kBAAA;QACA;MACA;QACA;UACAC,MAAA;QACA;MACA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA;IACA,IAAAC,iBAAA,IACAC,IAAA,WAAAC,GAAA;MACAC,OAAA,CAAAC,GAAA;MACAL,KAAA,CAAAR,OAAA,GAAAW,GAAA;IACA,GACAG,KAAA,WAAAC,KAAA;MACAH,OAAA,CAAAG,KAAA,YAAAA,KAAA;IACA;IAEA,KAAAC,eAAA;IACA,KAAAC,WAAA;IACA,KAAAC,YAAA;IACA,KAAAC,eAAA;IACA,KAAAC,gBAAA;IACA,KAAAC,eAAA;IACA,IAAAC,iCAAA,MAAAZ,IAAA,WAAAC,GAAA;MACAH,KAAA,CAAAzB,WAAA,GAAA4B,GAAA,CAAA7E,IAAA,CAAAiD,WAAA;MACAyB,KAAA,CAAAxB,eAAA,GAAA2B,GAAA,CAAA7E,IAAA,CAAAkD,eAAA;IACA;EACA;EACAuC,aAAA,WAAAA,cAAA;IACA,KAAAC,gBAAA;IACA,KAAAC,iBAAA;IACA,KAAAC,iBAAA;IACA,KAAAC,kBAAA;EACA;EACAC,OAAA;IACA;IACAP,eAAA,WAAAA,gBAAA;MAAA,IAAAQ,MAAA;MAAA,WAAAC,kBAAA,CAAAnG,OAAA,mBAAAoG,oBAAA,CAAApG,OAAA,IAAAqG,IAAA,UAAAC,QAAA;QAAA,IAAAC,QAAA;QAAA,WAAAH,oBAAA,CAAApG,OAAA,IAAAwG,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAE,IAAA;cAAA,OAEA,IAAAC,sBAAA;gBACAC,SAAA;gBACAC,QAAA;gBACAC,QAAA;cACA;YAAA;cAJAT,QAAA,GAAAG,QAAA,CAAAO,IAAA;cAKAf,MAAA,CAAAjE,UAAA,GAAAsE,QAAA;cAAAG,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAQ,EAAA,GAAAR,QAAA;cAEAzB,OAAA,CAAAG,KAAA,gBAAAsB,QAAA,CAAAQ,EAAA;YAAA;YAAA;cAAA,OAAAR,QAAA,CAAAS,IAAA;UAAA;QAAA,GAAAb,OAAA;MAAA;IAEA;IAEAc,kBAAA,WAAAA,mBAAAtH,IAAA,EAAAuH,IAAA;MAAA,IAAAC,MAAA;MACA,KAAApF,kBAAA,GAAAmF,IAAA;MACA,QAAAvH,IAAA;QACA;UACA,IAAAyH,8BAAA;YAAAC,EAAA,EAAAH,IAAA,CAAAG;UAAA,GAAAzC,IAAA,WAAAC,GAAA;YACAsC,MAAA,CAAApG,mBAAA,GAAAmG,IAAA,CAAAI,KAAA;YACAH,MAAA,CAAAnG,qBAAA,GAAA6D,GAAA,CAAA7E,IAAA,CAAAuH,OAAA;YACAJ,MAAA,CAAAlG,uBAAA,GAAA4D,GAAA,CAAA7E,IAAA,CAAAwH,SAAA;YACAL,MAAA,CAAArG,uBAAA;UACA;UACA;QACA;UACA,KAAA2G,WAAA,CAAAP,IAAA;UACA;QACA;UACA,KAAAO,WAAA,CAAAP,IAAA;UACA;MACA;IACA;IACAQ,yBAAA,WAAAA,0BAAAR,IAAA;MAAA,IAAAS,MAAA;MACA,IAAAC,0BAAA;QACAC,UAAA,EAAAX,IAAA,CAAAW,UAAA;QACAC,OAAA;QACAC,QAAA;MACA,GAAAnD,IAAA,WAAAC,GAAA;QACA8C,MAAA,CAAApG,UAAA,GAAAsD,GAAA,CAAAmD,IAAA;QACAL,MAAA,CAAAlG,WAAA,GAAAoD,GAAA,CAAAoD,KAAA;MACA;MACA,IAAAC,4BAAA;QACAL,UAAA,EAAAX,IAAA,CAAAW,UAAA;QACAC,OAAA;QACAC,QAAA;MACA,GAAAnD,IAAA,WAAAC,GAAA;QACA8C,MAAA,CAAAnG,YAAA,GAAAqD,GAAA,CAAAmD,IAAA;QACAL,MAAA,CAAAjG,aAAA,GAAAmD,GAAA,CAAAoD,KAAA;MACA;MACA,KAAA3G,4BAAA,OAAA6G,cAAA,CAAAtI,OAAA,MAAAqH,IAAA;MACA,KAAArG,0BAAA,GAAAqG,IAAA,CAAAkB,cAAA;MACA,KAAAxH,8BAAA;IACA;IAEAyH,gBAAA,WAAAA,iBAAAR,UAAA,EAAAS,WAAA;MAAA,IAAAC,MAAA;MACA,IAAAX,0BAAA,MAAAO,cAAA,CAAAtI,OAAA;QACAgI,UAAA,EAAAA;MAAA,GACAS,WAAA,CACA,EAAA1D,IAAA,WAAAC,GAAA;QACA0D,MAAA,CAAAhH,UAAA,GAAAsD,GAAA,CAAAmD,IAAA;MACA;IACA;IAEAQ,kBAAA,WAAAA,mBAAAX,UAAA,EAAAS,WAAA;MAAA,IAAAG,MAAA;MACA,IAAAP,4BAAA,MAAAC,cAAA,CAAAtI,OAAA;QACAgI,UAAA,EAAAA;MAAA,GACAS,WAAA,CACA,EAAA1D,IAAA,WAAAC,GAAA;QACA4D,MAAA,CAAAjH,YAAA,GAAAqD,GAAA,CAAAmD,IAAA;MACA;IACA;IAEA9C,eAAA,WAAAA,gBAAA;MAAA,IAAAwD,MAAA;MACA,IAAAC,oBAAA;QACAhC,SAAA;QACAC,QAAA;QACAC,QAAA;MACA,GAAAjC,IAAA,WAAAC,GAAA;QACA,IAAA7E,IAAA;QACA4I,MAAA,CAAAC,IAAA,CAAAhE,GAAA,CAAA7E,IAAA,EAAA8I,OAAA,WAAAC,GAAA;UACA/I,IAAA,CAAAgJ,IAAA;YACAC,IAAA,EAAAF,GAAA;YACAG,WAAA,EACArE,GAAA,CAAA7E,IAAA,CAAA+I,GAAA,EAAAI,MAAA,QACAtE,GAAA,CAAA7E,IAAA,CAAA+I,GAAA,IACAlE,GAAA,CAAA7E,IAAA,CAAA+I,GAAA,EAAAK,KAAA,CACAvE,GAAA,CAAA7E,IAAA,CAAA+I,GAAA,EAAAI,MAAA,MACAtE,GAAA,CAAA7E,IAAA,CAAA+I,GAAA,EAAAI,MACA;UACA;QACA;QACAT,MAAA,CAAAxH,gBAAA,GAAAlB,IAAA,CAAAqJ,OAAA;MACA;IACA;IAEAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,8BAAA;QACA7C,SAAA;QACAC,QAAA;QACAC,QAAA;QACAiB,OAAA;QACAC,QAAA;MACA,GAAAnD,IAAA,WAAAC,GAAA;QACA0E,MAAA,CAAAnI,kBAAA,GAAAyD,GAAA,CAAAmD,IAAA,CAAAyB,GAAA,WAAAvC,IAAA,EAAAwC,KAAA;UAAA,WAAAvB,cAAA,CAAAtI,OAAA,MAAAsI,cAAA,CAAAtI,OAAA,MACAqH,IAAA;YACAvH,IAAA,EAAA+J,KAAA;UAAA;QAAA,CACA;QACAH,MAAA,CAAAlI,uBAAA,GAAAwD,GAAA,CAAAoD,KAAA;MACA;MACA,IAAA0B,0BAAA;QACAhD,SAAA;QACAC,QAAA;QACAC,QAAA;MACA,GAAAjC,IAAA,WAAAC,GAAA;QACA;QACA,IAAA7E,IAAA,GAAA4I,MAAA,CAAAC,IAAA,CAAAhE,GAAA,CAAA7E,IAAA,EAAAyJ,GAAA,WAAAG,IAAA;UAAA;YACAC,OAAA,EAAAD,IAAA;YACAE,EAAA,EAAAjF,GAAA,CAAA7E,IAAA,CAAA4J,IAAA,EAAAE,EAAA;YACAC,EAAA,EAAAlF,GAAA,CAAA7E,IAAA,CAAA4J,IAAA,EAAAG,EAAA;YACAC,EAAA,EAAAnF,GAAA,CAAA7E,IAAA,CAAA4J,IAAA,EAAAI;UACA;QAAA;QACAT,MAAA,CAAApI,gBAAA,GAAAnB,IAAA;QACAuJ,MAAA,CAAA5I,2BAAA;MACA;IACA;IAEAsJ,4BAAA,WAAAA,6BAAA3B,WAAA;MAAA,IAAA4B,MAAA;MACA,IAAAV,8BAAA,MAAArB,cAAA,CAAAtI,OAAA;QACA8G,SAAA;QACAC,QAAA;QACAC,QAAA;MAAA,GACAyB,WAAA,CACA,EAAA1D,IAAA,WAAAC,GAAA;QACAqF,MAAA,CAAA9I,kBAAA,GAAAyD,GAAA,CAAAmD,IAAA,CAAAyB,GAAA,WAAAvC,IAAA,EAAAwC,KAAA;UAAA,WAAAvB,cAAA,CAAAtI,OAAA,MAAAsI,cAAA,CAAAtI,OAAA,MACAqH,IAAA;YACAvH,IAAA,EAAA+J,KAAA;UAAA;QAAA,CACA;MACA;IACA;IAEAS,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,qBAAA;QACA1D,SAAA;QACAC,QAAA;QACAC,QAAA;QACAiB,OAAA;QACAC,QAAA;MACA,GAAAnD,IAAA,WAAAC,GAAA;QACAuF,OAAA,CAAAzI,eAAA,GAAAkD,GAAA,CAAAmD,IAAA;QACAoC,OAAA,CAAAvI,oBAAA,GAAAgD,GAAA,CAAAoD,KAAA;MACA;MACA,IAAAqC,4BAAA;QACA3D,SAAA;QACAC,QAAA;QACAC,QAAA;MACA,GAAAjC,IAAA,WAAAC,GAAA;QACAuF,OAAA,CAAAxI,eAAA,GAAAiD,GAAA,CAAA7E,IAAA;QACAoK,OAAA,CAAAnK,mBAAA;MACA;IACA;IAEAsK,oBAAA,WAAAA,qBAAAjC,WAAA;MAAA,IAAAkC,OAAA;MACA,IAAAH,qBAAA,MAAAlC,cAAA,CAAAtI,OAAA;QACA8G,SAAA;QACAC,QAAA;QACAC,QAAA;MAAA,GACAyB,WAAA,CACA,EAAA1D,IAAA,WAAAC,GAAA;QACA2F,OAAA,CAAA7I,eAAA,GAAAkD,GAAA,CAAAmD,IAAA;MACA;IACA;IAEAyC,iBAAA,WAAAA,kBAAAzK,IAAA;MACA,KAAAG,sBAAA;MACA,KAAAC,mBAAA,GAAAJ,IAAA,CAAAsH,KAAA;MACA,KAAAjH,gBAAA,GAAAL,IAAA,CAAA0K,QAAA;IACA;IACAC,mBAAA,WAAAA,oBAAA3K,IAAA;MACA;MACA;;MAEA;MACA,SAAAgE,SAAA;QACA;QACA,KAAAZ,cAAA,GACAwH,mBAAA,CAAAC,KAAA,MAAAC,KAAA,CAAAC,iBAAA,CAAAC,QAAA;QACA,KAAA3H,YAAA;MACA,gBAAAW,SAAA;QACA;QACA,KAAAZ,cAAA,GACAwH,mBAAA,CAAAK,GAAA,MAAAH,KAAA,CAAAC,iBAAA,CAAAC,QAAA;QACA,KAAA3H,YAAA;MACA,gBAAAW,SAAA;QACA;QACA,KAAAZ,cAAA,GACAwH,mBAAA,CAAAM,EAAA,MAAAJ,KAAA,CAAAC,iBAAA,CAAAC,QAAA;QACA,KAAA3H,YAAA;MACA;QACA,KAAAD,cAAA,QAAAa,cAAA;QACA,aAAAD,SAAA;UACA;YACA,KAAAX,YAAA;YACA;UACA;YACA,KAAAA,YAAA;YACA;UACA;YACA,KAAAA,YAAA;YACA;UACA;YACA,KAAAA,YAAA;YACA;UACA;YACA,KAAAA,YAAA;YACA;UACA;YACA,KAAAA,YAAA;YACA;QACA;MACA;MACA,KAAAC,SAAA;MACA,KAAAH,cAAA;IACA;IAEAgI,qBAAA,WAAAA,sBAAAnL,IAAA;MACA,KAAAQ,0BAAA;MACA,KAAAC,sBAAA,GAAAT,IAAA,CAAA1B,IAAA;MACA,KAAAoC,qBAAA,GAAAV,IAAA,CAAAA,IAAA;IACA;IAEA;IACAmF,WAAA,WAAAA,YAAA;MAAA,IAAAiG,OAAA;MACA;MACA,IAAAC,oBAAA,IACAzG,IAAA,WAAAC,GAAA;QACAuG,OAAA,CAAApJ,kBAAA,GAAA6C,GAAA,CAAA7E,IAAA;QACAoL,OAAA,CAAAE,SAAA;UACAF,OAAA,CAAAG,WAAA;QACA;MACA,GACAvG,KAAA,WAAAC,KAAA;QACAH,OAAA,CAAAG,KAAA,gBAAAA,KAAA;QACA;QACAmG,OAAA,CAAApJ,kBAAA;MACA;IACA;IACAoD,YAAA,WAAAA,aAAA;MAAA,IAAAoG,OAAA;MACA,KAAAC,iBAAA;MACA,KAAAH,SAAA;QACAE,OAAA,CAAAE,YAAA;MACA;IACA;IAEAH,WAAA,WAAAA,YAAA;MAAA,IAAAI,OAAA;MACA,KAAAjG,gBAAA;MACA,IAAAkG,OAAA,QAAAd,KAAA,CAAAe,aAAA;MACA,IAAAtE,OAAA,QAAAuD,KAAA,CAAAgB,aAAA;MAEA,KAAAF,OAAA,KAAArE,OAAA;MAEA,KAAAtF,WAAA,GAAA8J,WAAA;QACA,IAAAJ,OAAA,CAAAvJ,SAAA;QAEA,IAAAwJ,OAAA,CAAAI,SAAA,IAAAzE,OAAA,CAAA0E,YAAA,GAAAL,OAAA,CAAAM,YAAA;UACAN,OAAA,CAAAI,SAAA;QACA;UACAJ,OAAA,CAAAI,SAAA,IAAAL,OAAA,CAAApJ,UAAA;QACA;QACAoJ,OAAA,CAAAtG,eAAA;MACA;IACA;IAEAK,gBAAA,WAAAA,iBAAA;MACA,SAAAzD,WAAA;QACAkK,aAAA,MAAAlK,WAAA;QACA,KAAAA,WAAA;MACA;IACA;IAEAmK,gBAAA,WAAAA,iBAAA;MACA,KAAAhK,SAAA;IACA;IAEAiK,gBAAA,WAAAA,iBAAA;MACA,KAAAjK,SAAA;MACA,KAAAmJ,WAAA;IACA;IACAe,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,KAAA5G,iBAAA;MACA,IAAAiG,OAAA,QAAAd,KAAA,CAAA0B,cAAA;MACA,IAAAjF,OAAA,QAAAuD,KAAA,CAAA2B,cAAA;MAEA,KAAAb,OAAA,KAAArE,OAAA;MAEA,KAAArF,YAAA,GAAA6J,WAAA;QACA,IAAAQ,OAAA,CAAAlK,UAAA;QAEA,IAAAuJ,OAAA,CAAAI,SAAA,IAAAzE,OAAA,CAAA0E,YAAA,GAAAL,OAAA,CAAAM,YAAA;UACAN,OAAA,CAAAI,SAAA;QACA;UACAJ,OAAA,CAAAI,SAAA,IAAAO,OAAA,CAAAhK,UAAA;QACA;QACAgK,OAAA,CAAAjH,gBAAA;MACA;IACA;IACAoG,YAAA,WAAAA,aAAA;MAAA,IAAAgB,OAAA;MACA,KAAA9G,iBAAA;MACA,KAAAzD,YAAA,GAAA4J,WAAA;QACA,IAAAW,OAAA,CAAArK,UAAA;;QAEA;QACA,IAAAsK,QAAA,IACA,QACA,QACA,SACA,WACA,QACA,QACA,QACA,QACA,KACA;;QAEA;QACA,IAAAC,YAAA,GAAAD,QAAA,CAAAE,OAAA,CAAAH,OAAA,CAAA3J,YAAA;QACA;QACA,IAAA+J,SAAA,IAAAF,YAAA,QAAAD,QAAA,CAAAxD,MAAA;QACA;QACAuD,OAAA,CAAAjB,iBAAA,CAAAkB,QAAA,CAAAG,SAAA;MACA;IACA;IACAnH,iBAAA,WAAAA,kBAAA;MACA,SAAAzD,YAAA;QACAiK,aAAA,MAAAjK,YAAA;QACA,KAAAA,YAAA;MACA;IACA;IACA0D,iBAAA,WAAAA,kBAAA;MACA,SAAAzD,YAAA;QACAgK,aAAA,MAAAhK,YAAA;QACA,KAAAA,YAAA;MACA;IACA;IACA4K,iBAAA,WAAAA,kBAAA;MACA,KAAA1K,UAAA;IACA;IAEA2K,iBAAA,WAAAA,kBAAA;MACA,KAAA3K,UAAA;MACA;MACA;IACA;IACA4K,iBAAA,WAAAA,kBAAA;MACA,KAAA3K,UAAA;IACA;IAEA4K,iBAAA,WAAAA,kBAAA;MACA,KAAA5K,UAAA;IACA;IACA+C,eAAA,WAAAA,gBAAA;MACA,IAAAuG,OAAA,QAAAd,KAAA,CAAAe,aAAA;MACA,KAAAD,OAAA;MAEA,IAAAI,SAAA,GAAAJ,OAAA,CAAAI,SAAA;QAAAC,YAAA,GAAAL,OAAA,CAAAK,YAAA;QAAAC,YAAA,GAAAN,OAAA,CAAAM,YAAA;MACA,IAAAiB,aAAA,GAAAjB,YAAA,GAAAD,YAAA;MACA,IAAAmB,eAAA,GAAAC,IAAA,CAAAC,GAAA,KAAAH,aAAA,GAAAjB,YAAA;MACA,IAAAqB,YAAA,GAAAvB,SAAA,GAAAC,YAAA,GAAAC,YAAA;MAEAsB,QAAA,CAAAC,eAAA,CAAAC,KAAA,CAAAC,WAAA,CACA,yBAAAnJ,MAAA,CACA4I,eAAA,OACA;MACAI,QAAA,CAAAC,eAAA,CAAAC,KAAA,CAAAC,WAAA,CACA,sBAAAnJ,MAAA,CACA+I,YAAA,OACA;IACA;IACAjI,gBAAA,WAAAA,iBAAA;MACA,IAAAsG,OAAA,QAAAd,KAAA,CAAA0B,cAAA;MACA,KAAAZ,OAAA;MAEA,IAAAI,SAAA,GAAAJ,OAAA,CAAAI,SAAA;QAAAC,YAAA,GAAAL,OAAA,CAAAK,YAAA;QAAAC,YAAA,GAAAN,OAAA,CAAAM,YAAA;MACA,IAAAiB,aAAA,GAAAjB,YAAA,GAAAD,YAAA;MACA,IAAAmB,eAAA,GAAAC,IAAA,CAAAC,GAAA,KAAAH,aAAA,GAAAjB,YAAA;MACA,IAAAqB,YAAA,GAAAvB,SAAA,GAAAC,YAAA,GAAAC,YAAA;MAEAsB,QAAA,CAAAC,eAAA,CAAAC,KAAA,CAAAC,WAAA,CACA,yBAAAnJ,MAAA,CACA4I,eAAA,OACA;MACAI,QAAA,CAAAC,eAAA,CAAAC,KAAA,CAAAC,WAAA,CACA,sBAAAnJ,MAAA,CACA+I,YAAA,OACA;IACA;IAEA9F,WAAA,WAAAA,YAAAP,IAAA;MAAA,IAAA0G,OAAA;MAAA,WAAA5H,kBAAA,CAAAnG,OAAA,mBAAAoG,oBAAA,CAAApG,OAAA,IAAAqG,IAAA,UAAA2H,SAAA;QAAA,IAAAhJ,GAAA,EAAA0C,OAAA;QAAA,WAAAtB,oBAAA,CAAApG,OAAA,IAAAwG,IAAA,UAAAyH,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvH,IAAA,GAAAuH,SAAA,CAAAtH,IAAA;YAAA;cAAAsH,SAAA,CAAAvH,IAAA;cAAAuH,SAAA,CAAAtH,IAAA;cAAA,OAGA,IAAAuH,wBAAA,EAAA9G,IAAA,CAAAG,EAAA;YAAA;cAAAxC,GAAA,GAAAkJ,SAAA,CAAAjH,IAAA;cACA8G,OAAA,CAAApL,UAAA;gBACAyL,OAAA,EACA/G,IAAA,CAAA+G,OAAA,IAAA/G,IAAA,CAAAI,KAAA,IAAAzC,GAAA,CAAA7E,IAAA,CAAAsH,KAAA,IAAAzC,GAAA,CAAA7E,IAAA,CAAAiO,OAAA;gBACA3G,KAAA,EACAJ,IAAA,CAAAI,KAAA,IAAAJ,IAAA,CAAA+G,OAAA,IAAApJ,GAAA,CAAA7E,IAAA,CAAAsH,KAAA,IAAAzC,GAAA,CAAA7E,IAAA,CAAAiO,OAAA;gBACAC,SAAA,EAAArJ,GAAA,CAAA7E,IAAA,CAAAuH,OAAA,IAAA1C,GAAA,CAAA7E,IAAA,CAAAkO;cACA;;cAEA;cACA3G,OAAA,GAAAqG,OAAA,CAAAO,cAAA,CAAAP,OAAA,CAAApL,UAAA,CAAA0L,SAAA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACAN,OAAA,CAAApL,UAAA,CAAA0L,SAAA,GAAA3G,OAAA;cAEAqG,OAAA,CAAAnL,oBAAA;cACAmL,OAAA,CAAAjL,WAAA,GAAAiL,OAAA,CAAAlL,QAAA;cAAAqL,SAAA,CAAAtH,IAAA;cAAA;YAAA;cAAAsH,SAAA,CAAAvH,IAAA;cAAAuH,SAAA,CAAAhH,EAAA,GAAAgH,SAAA;cAEAjJ,OAAA,CAAAG,KAAA,cAAA8I,SAAA,CAAAhH,EAAA;cACA;cACA6G,OAAA,CAAApL,UAAA;gBACAyL,OAAA,EAAA/G,IAAA,CAAAI,KAAA,IAAAJ,IAAA,CAAA+G,OAAA;gBACA3G,KAAA,EAAAJ,IAAA,CAAAI,KAAA,IAAAJ,IAAA,CAAA+G,OAAA;gBACAC,SAAA;cACA;cACAN,OAAA,CAAAnL,oBAAA;cACAmL,OAAA,CAAAjL,WAAA,GAAAiL,OAAA,CAAAlL,QAAA;YAAA;YAAA;cAAA,OAAAqL,SAAA,CAAA/G,IAAA;UAAA;QAAA,GAAA6G,QAAA;MAAA;IAEA;IACAO,YAAA,WAAAA,aAAAlH,IAAA;MAAA,IAAAmH,OAAA;MAAA,WAAArI,kBAAA,CAAAnG,OAAA,mBAAAoG,oBAAA,CAAApG,OAAA,IAAAqG,IAAA,UAAAoI,SAAA;QAAA,IAAAzJ,GAAA,EAAA0C,OAAA;QAAA,WAAAtB,oBAAA,CAAApG,OAAA,IAAAwG,IAAA,UAAAkI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhI,IAAA,GAAAgI,SAAA,CAAA/H,IAAA;YAAA;cAAA+H,SAAA,CAAAhI,IAAA;cAAAgI,SAAA,CAAA/H,IAAA;cAAA,OAGA,IAAAgI,kBAAA,EAAAvH,IAAA,CAAAwH,EAAA;YAAA;cAAA7J,GAAA,GAAA2J,SAAA,CAAA1H,IAAA;cACAuH,OAAA,CAAA7L,UAAA;gBACAyL,OAAA,EACA/G,IAAA,CAAA+G,OAAA,IAAA/G,IAAA,CAAAI,KAAA,IAAAzC,GAAA,CAAA7E,IAAA,CAAAsH,KAAA,IAAAzC,GAAA,CAAA7E,IAAA,CAAAiO,OAAA;gBACA3G,KAAA,EACAJ,IAAA,CAAAI,KAAA,IAAAJ,IAAA,CAAA+G,OAAA,IAAApJ,GAAA,CAAA7E,IAAA,CAAAsH,KAAA,IAAAzC,GAAA,CAAA7E,IAAA,CAAAiO,OAAA;gBACAC,SAAA,EAAArJ,GAAA,CAAA7E,IAAA,CAAAuH,OAAA,IAAA1C,GAAA,CAAA7E,IAAA,CAAAkO;cACA;;cAEA;cACA3G,OAAA,GAAA8G,OAAA,CAAAF,cAAA,CAAAE,OAAA,CAAA7L,UAAA,CAAA0L,SAAA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACAG,OAAA,CAAA7L,UAAA,CAAA0L,SAAA,GAAA3G,OAAA;cAEA8G,OAAA,CAAA5L,oBAAA;cACA4L,OAAA,CAAA1L,WAAA,GAAA0L,OAAA,CAAA3L,QAAA;cAAA8L,SAAA,CAAA/H,IAAA;cAAA;YAAA;cAAA+H,SAAA,CAAAhI,IAAA;cAAAgI,SAAA,CAAAzH,EAAA,GAAAyH,SAAA;cAEA1J,OAAA,CAAAG,KAAA,cAAAuJ,SAAA,CAAAzH,EAAA;cACA;cACAsH,OAAA,CAAA7L,UAAA;gBACAyL,OAAA,EAAA/G,IAAA,CAAAI,KAAA,IAAAJ,IAAA,CAAA+G,OAAA;gBACA3G,KAAA,EAAAJ,IAAA,CAAAI,KAAA,IAAAJ,IAAA,CAAA+G,OAAA;gBACAC,SAAA;cACA;cACAG,OAAA,CAAA5L,oBAAA;cACA4L,OAAA,CAAA1L,WAAA,GAAA0L,OAAA,CAAA3L,QAAA;YAAA;YAAA;cAAA,OAAA8L,SAAA,CAAAxH,IAAA;UAAA;QAAA,GAAAsH,QAAA;MAAA;IAEA;IACAH,cAAA,WAAAA,eAAA5G,OAAA;MACA,IAAAA,OAAA;QACA,QAAAoH,2BAAA,EAAApH,OAAA;UACAA,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA,CACA,+CACA,IACA;UACA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA,CACA,+CACA,IACA;UAEA9J,OAAA,CAAAC,GAAA,iQAAA8J,0BAAA,EAAAtH,OAAA;UACAzC,OAAA,CAAAC,GAAA,wQAAA+J,gCAAA,EAAAvH,OAAA;QACA;UACAA,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA,CACA,+CACA,IACA;UACA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA,CACA,+CACA,IACA;QACA;MACA;MACA,OAAArH,OAAA;IACA;IAEAwH,WAAA,WAAAA,YAAA;MACA,KAAAvM,UAAA;MACA,KAAAC,oBAAA;IACA;IAEAuM,gBAAA,WAAAA,iBAAA;MACA,SAAAtM,QAAA;QACA,KAAAA,QAAA;MACA;IACA;IAEAuM,gBAAA,WAAAA,iBAAA;MACA,SAAAvM,QAAA;QACA,KAAAA,QAAA;MACA;IACA;IACAwM,eAAA,WAAAA,gBAAAvP,IAAA;MACA;MACA,IAAAwP,gBAAA;MACA,SAAAnL,SAAA;QACAmL,gBAAA;MACA,gBAAAnL,SAAA;QACAmL,gBAAA;MACA;MACA,IAAAC,OAAA,GAAAC,IAAA,CAAAC,KAAA,CACAD,IAAA,CAAAE,SAAA,CAAAC,gBAAA,CAAAL,gBAAA,EAAAxP,IAAA,QACA;MACA,KAAAiD,iBAAA,QAAA6M,mBAAA,CAAAL,OAAA;IACA;IACAK,mBAAA,WAAAA,oBAAAzP,IAAA;MAAA,IAAA0P,OAAA;MACA,KAAA1P,IAAA,KAAAJ,KAAA,CAAA+P,OAAA,CAAA3P,IAAA,UAAAA,IAAA;MACA,OAAAA,IAAA,CAAAyJ,GAAA,WAAAvC,IAAA;QACA,IACA,CAAAA,IAAA,CAAAvH,IAAA,kBACAuH,IAAA,CAAAvH,IAAA,kBACAuH,IAAA,CAAAvH,IAAA,mBACAC,KAAA,CAAA+P,OAAA,CAAAzI,IAAA,CAAA0I,QAAA,GACA;UACA1I,IAAA,CAAA0I,QAAA,GAAA1I,IAAA,CAAA0I,QAAA,CAAAxG,KAAA;QACA;QAEA,IAAAlC,IAAA,CAAA0I,QAAA;UACA1I,IAAA,CAAA0I,QAAA,GAAAF,OAAA,CAAAD,mBAAA,CAAAvI,IAAA,CAAA0I,QAAA;QACA;QAEA,OAAA1I,IAAA;MACA;IACA;IACAuE,iBAAA,WAAAA,kBAAA9L,IAAA;MAAA,IAAAkQ,OAAA;MACA,IAAAC,GAAA;QACAC,IAAA;QACAC,IAAA;QACAC,KAAA;QACAC,OAAA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,EAAA;MACA;MACA,KAAAxN,YAAA,GAAApD,IAAA;;MAEA;MACA,KAAA+L,YAAA;MAEA,IAAA8E,uBAAA;QACAC,OAAA,EAAAX,GAAA,CAAAnQ,IAAA;MACA,GAAAiF,IAAA,WAAAC,GAAA;QACA;QACA,IAAA6L,gBAAA,GAAAb,OAAA,CAAAc,mBAAA,CAAA9L,GAAA;QACAgL,OAAA,CAAA/M,mBAAA,GAAA4N,gBAAA;QACAb,OAAA,CAAAvE,SAAA;UACA,IAAAM,OAAA,GAAAiE,OAAA,CAAA/E,KAAA,CAAA0B,cAAA;UACAZ,OAAA,CAAAI,SAAA;QACA;MACA;IACA;IACA4E,gBAAA,WAAAA,iBAAA;MACA,IAAAd,GAAA;QACAC,IAAA;QACAC,IAAA;QACAC,KAAA;QACAC,OAAA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,EAAA;MACA;MACAM,MAAA,CAAAC,IAAA,CAAAhB,GAAA,MAAA/M,YAAA;IACA;IACA;IACA4N,mBAAA,WAAAA,oBAAAI,QAAA;MACA,KAAAnR,KAAA,CAAA+P,OAAA,CAAAoB,QAAA;QACA;MACA;MAEA,IAAAC,IAAA,OAAAC,GAAA;MACA,IAAAC,MAAA;MAEAH,QAAA,CAAAjI,OAAA,WAAAqI,OAAA;QACA,IAAAA,OAAA,IAAAA,OAAA,CAAAlD,OAAA;UACA;UACA,IAAAmD,eAAA,GAAAD,OAAA,CAAAlD,OAAA,CAAAW,OAAA;UAEA,KAAAoC,IAAA,CAAAK,GAAA,CAAAD,eAAA;YACAJ,IAAA,CAAAM,GAAA,CAAAF,eAAA;YACAF,MAAA,CAAAlI,IAAA,CAAAmI,OAAA;UACA;QACA;UACA;UACAD,MAAA,CAAAlI,IAAA,CAAAmI,OAAA;QACA;MACA;MAEA,OAAAD,MAAA;IACA;IACAK,YAAA,WAAAA,aAAAC,GAAA,EAAAC,YAAA;MACA,IAAAC,MAAA,GAAAF,GAAA,CAAAG,QAAA;MACA,IAAAC,OAAA,OAAAC,MAAA,CAAAJ,YAAA,GAAAC,MAAA,CAAAvI,MAAA;MACA,UAAA3E,MAAA,CAAAoN,OAAA,EAAApN,MAAA,CAAAkN,MAAA,EAAA9C,OAAA;IACA;IACAkD,UAAA,WAAAA,WAAAC,GAAA;MACAlB,MAAA,CAAAC,IAAA,CAAAiB,GAAA;IACA;IACAlM,kBAAA,WAAAA,mBAAA;MACA,KAAAzC,cAAA;MACA,KAAAE,SAAA;MACA,KAAAH,cAAA;IACA;IAEA;IACA6O,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAjM,kBAAA,CAAAnG,OAAA,mBAAAoG,oBAAA,CAAApG,OAAA,IAAAqG,IAAA,UAAAgM,SAAA;QAAA,IAAA9L,QAAA,EAAA+L,WAAA;QAAA,WAAAlM,oBAAA,CAAApG,OAAA,IAAAwG,IAAA,UAAA+L,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7L,IAAA,GAAA6L,SAAA,CAAA5L,IAAA;YAAA;cAAA4L,SAAA,CAAA7L,IAAA;cAAA6L,SAAA,CAAA5L,IAAA;cAAA,OAEA,IAAA4E,oBAAA;YAAA;cAAAjF,QAAA,GAAAiM,SAAA,CAAAvL,IAAA;cACA,IAAAV,QAAA,IAAAA,QAAA,CAAApG,IAAA;gBACAmS,WAAA,GAAA/L,QAAA,CAAApG,IAAA,EACA;gBACA,IAAAiS,OAAA,CAAAK,oBAAA,CAAAH,WAAA;kBACAF,OAAA,CAAAjQ,kBAAA,GAAAmQ,WAAA;gBACA,QACA;cACA;cAAAE,SAAA,CAAA5L,IAAA;cAAA;YAAA;cAAA4L,SAAA,CAAA7L,IAAA;cAAA6L,SAAA,CAAAtL,EAAA,GAAAsL,SAAA;cAEAvN,OAAA,CAAAG,KAAA,kBAAAoN,SAAA,CAAAtL,EAAA;YAAA;YAAA;cAAA,OAAAsL,SAAA,CAAArL,IAAA;UAAA;QAAA,GAAAkL,QAAA;MAAA;IAEA;IAEA;IACAI,oBAAA,WAAAA,qBAAAH,WAAA;MACA;MACA,SAAAnQ,kBAAA,CAAAmH,MAAA;QACA,OAAAgJ,WAAA,CAAAhJ,MAAA;MACA;;MAEA;MACA,SAAAnH,kBAAA,CAAAmH,MAAA,KAAAgJ,WAAA,CAAAhJ,MAAA;QACA;MACA;;MAEA;MACA,SAAAoJ,CAAA,MAAAA,CAAA,GAAAJ,WAAA,CAAAhJ,MAAA,EAAAoJ,CAAA;QACA,IAAAC,UAAA,GAAAL,WAAA,CAAAI,CAAA;QACA,IAAAE,UAAA,QAAAzQ,kBAAA,CAAAuQ,CAAA;;QAEA;QACA,IACAC,UAAA,CAAAnL,EAAA,KAAAoL,UAAA,CAAApL,EAAA,IACAmL,UAAA,CAAAlL,KAAA,KAAAmL,UAAA,CAAAnL,KAAA,IACAkL,UAAA,CAAAE,WAAA,KAAAD,UAAA,CAAAC,WAAA,IACAF,UAAA,CAAAG,UAAA,KAAAF,UAAA,CAAAE,UAAA,EACA;UACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC,qBAAA,WAAAA,sBAAAhM,QAAA,EAAAiM,UAAA;MACA/N,OAAA,CAAAC,GAAA,6OAAA8N,UAAA,qCAAAjM,QAAA;MACA,KAAAjD,gBAAA,GAAAiD,QAAA;MACA,KAAAhD,mBAAA,GAAAgD,QAAA;MACA;MACA,KAAA/C,uBAAA;MACA,KAAAC,qBAAA,GAAA+O,UAAA;MACA,KAAA9O,wBAAA,GAAA6C,QAAA;;MAEA;MACA,KAAA0E,SAAA;QACA;QACA;MAAA,CACA;IACA;IAEA;IACAwH,2BAAA,WAAAA,4BAAA;MACA,KAAAjP,uBAAA;MACA,KAAAC,qBAAA;MACA,KAAAC,wBAAA;IACA;IAEA;IACAgP,uBAAA,WAAAA,wBAAA;MACA,KAAAC,KAAA;IACA;IAEA;IACAC,iBAAA,WAAAA,kBAAA9B,OAAA;MACA,KAAA6B,KAAA,8BAAA7B,OAAA;IACA;IAEA;IACA+B,SAAA,WAAAA,UAAAC,OAAA,EAAAC,YAAA;MAAA,IAAAC,OAAA;MACA,IAAAD,YAAA;QACA,KAAApP,SAAA,GAAAmP,OAAA;QACA,KAAAlP,cAAA,GAAAqP,cAAA,UAAAF,YAAA;QACA,KAAAG,aAAA;MACA,WAAAJ,OAAA,gBAAAA,OAAA,cAAAA,OAAA;QACA;QACA,SAAAnP,SAAA,KAAAmP,OAAA;UACA;UACA,KAAAvQ,iBAAA;UACA;UACA,KAAA0I,SAAA;YACA,IACA+H,OAAA,CAAAvI,KAAA,CAAAC,iBAAA,IACAsI,OAAA,CAAAvI,KAAA,CAAAC,iBAAA,CAAAyI,QAAA,CAAArK,MAAA,MACA;cACAkK,OAAA,CAAAvI,KAAA,CAAAC,iBAAA,CAAAmE,eAAA,CACAmE,OAAA,CAAAvI,KAAA,CAAAC,iBAAA,CAAAyI,QAAA,GACA;YACA;UACA;QACA;UACA;UACA,KAAAxP,SAAA,GAAAmP,OAAA;UACA,KAAAvQ,iBAAA;QACA;MACA;IACA;IACA2Q,aAAA,WAAAA,cAAA;MAAA,IAAAE,OAAA;MAAA,WAAAzN,kBAAA,CAAAnG,OAAA,mBAAAoG,oBAAA,CAAApG,OAAA,IAAAqG,IAAA,UAAAwN,SAAA;QAAA,IAAAC,GAAA,EAAAC,gBAAA,EAAAC,WAAA,EAAAC,qBAAA,EAAAC,IAAA,EAAAC,EAAA,EAAAC,aAAA;QAAA,WAAAhO,oBAAA,CAAApG,OAAA,IAAAwG,IAAA,UAAA6N,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3N,IAAA,GAAA2N,SAAA,CAAA1N,IAAA;YAAA;cAAA,IACAgN,OAAA,CAAAxP,cAAA;gBAAAkQ,SAAA,CAAA1N,IAAA;gBAAA;cAAA;cACAgN,OAAA,CAAAW,OAAA;cAAA,OAAAD,SAAA,CAAAE,MAAA;YAAA;cAAAF,SAAA,CAAA3N,IAAA;cAAA2N,SAAA,CAAA1N,IAAA;cAAA,OAKAgN,OAAA,CAAAnI,SAAA;YAAA;cACAqI,GAAA,GAAAF,OAAA,CAAA3I,KAAA,CAAAwJ,OAAA;cAAA,IACAX,GAAA;gBAAAQ,SAAA,CAAA1N,IAAA;gBAAA;cAAA;cAAA,MACA,IAAA8N,KAAA;YAAA;cAGA;cACAZ,GAAA,CAAAa,SAAA;;cAEA;cACAZ,gBAAA,GAAAH,OAAA,CAAAxP,cAAA,CACA2K,OAAA;cAAA,CACAA,OAAA;cAEAiF,WAAA,OAAAY,uBAAA;cAAAX,qBAAA,GACAD,WAAA,CAAAa,SAAA,CAAAd,gBAAA,GAAAG,IAAA,GAAAD,qBAAA,CAAAC,IAAA,EAEA;cACAC,EAAA,GAAAW,oBAAA,CAAAC,MAAA,CACAjB,GAAA,EACA;gBACAkB,OAAA;gBACAC,QAAA;gBACAC,aAAA;gBACAC,eAAA;gBACAC,iBAAA;gBACAC,QAAA;gBACAC,KAAA,WAAAA,MAAAC,IAAA;kBACA,IAAAC,MAAA;oBACA;oBAAA;oBACA;oBAAA;oBACA;oBAAA;oBACA;oBAAA;oBACA;kBACA;kBACA,OAAAA,MAAA,CAAAD,IAAA,CAAAE,KAAA;gBACA;gBACAC,QAAA,WAAAA,SAAAH,IAAA;kBACA,IAAAI,KAAA;oBACA;oBACA;oBACA;kBACA;kBACA,OACAA,KAAA,CAAAJ,IAAA,CAAAE,KAAA,KACA;gBAEA;gBACAG,QAAA;gBACAC,kBAAA;gBACAC,IAAA;gBACAC,GAAA;gBACAC,SAAA;gBACAC,SAAA,WAAAA,UAAAV,IAAA;kBAAA,aAAAA,IAAA,CAAAE,KAAA;gBAAA;gBACAS,SAAA,WAAAA,UAAAX,IAAA;kBACA,IAAAC,MAAA;oBACA;oBAAA;oBACA;oBAAA;oBACA;kBACA;kBACA,OAAAA,MAAA,CAAAD,IAAA,CAAAE,KAAA;gBACA;cACA,GACAvB,IACA,GAEA;cACAiC,UAAA;gBACAhC,EAAA,CAAAiC,GAAA;;gBAEA;gBACA,IAAAC,QAAA;gBACA,IAAAC,SAAA,GAAAnC,EAAA,CAAAoC,KAAA;kBAAAC,IAAA,GAAAF,SAAA,CAAAE,IAAA;kBAAAC,IAAA,GAAAH,SAAA,CAAAG,IAAA;kBAAAC,IAAA,GAAAJ,SAAA,CAAAI,IAAA;kBAAAC,IAAA,GAAAL,SAAA,CAAAK,IAAA;gBACA,IAAAC,KAAA,GAAAH,IAAA,GAAAD,IAAA;gBACA,IAAA9R,MAAA,GAAAiS,IAAA,GAAAD,IAAA;gBACA,IAAAG,cAAA,GAAA/C,GAAA,CAAAgD,WAAA;gBACA,IAAAC,eAAA,GAAAjD,GAAA,CAAAzH,YAAA;;gBAEA;gBACA,IAAA2K,KAAA,GAAAxJ,IAAA,CAAAyJ,GAAA,CACAJ,cAAA,GAAAD,KAAA,GAAAP,QAAA,EACAU,eAAA,GAAArS,MAAA,GAAA2R,QACA;;gBAEA;gBACAlC,EAAA,CAAA+C,OAAA,CAAAhD,IAAA;kBACAiD,YAAA,EAAAH,KAAA;kBACAI,eAAA,GACA,CAAAP,cAAA,GAAAD,KAAA,GAAAI,KAAA,OACA,CAAAD,eAAA,GAAArS,MAAA,GAAAsS,KAAA;gBAEA;cACA;;cAEA;cACA5C,aAAA,YAAAA,cAAA;gBAAA,OAAAD,EAAA,CAAAiC,GAAA;cAAA;cACApF,MAAA,CAAAqG,gBAAA,WAAAjD,aAAA;;cAEA;cACAR,OAAA,CAAA0D,KAAA;gBACAtG,MAAA,CAAAuG,mBAAA,WAAAnD,aAAA;cACA;cAAAE,SAAA,CAAA1N,IAAA;cAAA;YAAA;cAAA0N,SAAA,CAAA3N,IAAA;cAAA2N,SAAA,CAAApN,EAAA,GAAAoN,SAAA;cAEArP,OAAA,CAAAG,KAAA,6BAAAkP,SAAA,CAAApN,EAAA;cACA0M,OAAA,CAAA4D,QAAA,CAAApS,KAAA;YAAA;cAAAkP,SAAA,CAAA3N,IAAA;cAEAiN,OAAA,CAAAW,OAAA;cAAA,OAAAD,SAAA,CAAAmD,MAAA;YAAA;YAAA;cAAA,OAAAnD,SAAA,CAAAnN,IAAA;UAAA;QAAA,GAAA0M,QAAA;MAAA;IAEA;IACA;IACA6D,QAAA,WAAAA,SAAA;MACA1G,MAAA,CAAAC,IAAA,MAAA5M,OAAA;IACA;EACA;AACA", "ignoreList": []}]}
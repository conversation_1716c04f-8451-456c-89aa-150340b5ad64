import request from "@/utils/request";

const KeLIst = (params) => request.get("", params);
/* 新增科情 */
const Addmonitoring = (params) => {
  return request({
    url: "/article/monitoring",
    method: "post",
    data: params,
  });
};
/* 科情列表 */
const monitoringList = (params) => {
  return request({
    url: "/article/monitoring/list",
    method: "get",
    params: params,
  });
};
/* 科情详细信息 */
const monitoringInfo = (params) => {
  return request({
    url: `/dev-api/article/monitoring/{${params.id}}`,
    method: "get",
  });
};
/* ES检索 */
const EsSeach = (params) => {
  return request({
    url: "article/monitoring/esRetrieval",
    method: "post",
    data: params,
  });
};
/* 修改科情监测 */
const editMonitoring = (params) => {
  return request({
    url: "/article/monitoring/edit",
    method: "post",
    data: params,
  });
};
/* 删除科情监测 */
const deleteMonitoring = (params) => {
  return request({
    url: `/article/monitoring/remove`,
    method: "post",
    data: params,
  });
};
/* 下载科情监测 */
const downLoadExport = (params) => {
  return request({
    url: "/article/monitoring/export",
    method: "post",
    params: params,
  });
};
/* 查询新建报告 */
const getNewBuilt = (params) => {
  return request({
    url: "/article/report/reports",
    method: "get",
    params: params,
  });
};
/* 添加到报告 */
const AddReport = (params) => {
  return request({
    url: "/result/report",
    method: "post",
    data: params,
  });
};
/* 专题新增 */
const addSpecial = (params) => {
  return request({
    url: "/article/special",
    method: "post",
    data: params,
  });
};
/* 专题列表 */
const SpecialList = (params) => {
  return request({
    url: "/article/special/list",
    method: "get",
    params: params,
  });
};
/* 专题ES检索 */
const SpecialEs = (params) => {
  return request({
    url: "/article/special/esRetrieval",
    method: "post",
    data: params,
  });
};
/* 修改专题 */
const EditSpecialEs = (params) => {
  return request({
    url: "/article/special/edit",
    method: "post",
    data: params,
  });
};
// 查询专题
const getSpecialEs = (id) => {
  return request({
    url: "/article/special/" + id,
    method: "get",
  });
};
/* 删除专题 */
const deleteSpecialEs = (params) => {
  return request({
    url: `/article/special/remove`,
    method: "post",
    data: params,
  });
};
/* 收藏 */
const collectApi = (params) => {
  return request({
    url: "/article/collection",
    method: "post",
    data: params,
  });
};
/* 取消收藏 */
const cocelCollect = (params) => {
  return request({
    url: "/article/collection/cancel",
    method: "post",
    data: params,
  });
};
/* 推荐 */
const recommendAdd = (params) => {
  return request({
    url: "/article/recommend/add",
    method: "post",
    data: params,
  });
};
/* 取消推荐 */
const recommendCancel = (params) => {
  return request({
    url: "/article/recommend/cancel",
    method: "post",
    data: params,
  });
};
/* 用户推荐列表 */
const recommendList = (params) => {
  return request({
    url: "/article/recommend/list",
    method: "get",
    params: params,
  });
};
/* 管理员推荐列表 */
const recommendManageList = (params) => {
  return request({
    url: "/article/recommend/manage/list",
    method: "get",
    params: params,
  });
};
/* 获取文章对应的推荐人 */
const recommendQueryUser = (params) => {
  return request({
    url: `/article/recommend/query/user/${params.articleId}`,
    method: "get",
    params: params,
  });
};
/* 获取推荐文章列表（不分页） */
const recommendHot = (params) => {
  return request({
    url: `/article/recommend/hot`,
    method: "get",
    params: params,
  });
};
/* 新增浏览 */
const browseAdd = (params) => {
  return request({
    url: "/article/browse/add",
    method: "post",
    data: params,
  });
};
/* 删除浏览 */
const browseCancel = (params) => {
  return request({
    url: "/article/browse/remove",
    method: "post",
    data: params,
  });
};
/* 浏览列表 */
const browseList = (params) => {
  return request({
    url: "/article/browse/list",
    method: "get",
    params: params,
  });
};
/* 获取领域 */
const areaList = () => {
  return request({
    url: "/article/field/fieldAll",
    method: "get",
  });
};
/* 获取行业 */
const industry = () => {
  return request({
    url: "/article/industry/industryAll",
    method: "get",
  });
};
/* 标签添加 */
const tagAdd = (params) => {
  return request({
    url: "/article/label/edit",
    method: "post",
    data: params,
  });
};
/* 获取地区 */
const getAreaList = (params) => {
  return request({
    url: "/article/region/all",
    method: "get",
  });
};
/* 公众号列表 */
const GetWechatList = (params) => {
  return request({
    url: "/article/source/list",
    method: "get",
    params,
  });
};
/* 公众号列表 */
const weChatList = (params) => {
  return request({
    url: "/article/source/wechat/search",
    method: "post",
    data: params,
  });
};
/* 公众号列表 */
const wechatCountSourceName = (params) => {
  return request({
    url: "/article/source/wechat/countSourceName",
    method: "post",
    data: params,
    timeout: 1000000,
  });
};
/* 公众号检索 */
const esRetrieval = (params) => {
  return request({
    url: "/article/source/esRetrieval",
    method: "post",
    data: params,
  });
};
/* 文章详情 */
const AreaInfo = (params) => {
  return request({
    url: `/article/articleList/${params}`,
    method: "get",
  });
};
const articleDetail = (params) => {
  return request({
    url: `/article/articleList/detail`,
    method: "get",
    params: params,
  });
};

/* 批量导入报告 */
const batchImportReports = (formData) => {
  return request({
    url: "/report/list/uploads",
    method: "post",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
};

// 智库报告列表
const getReportsList = (params) => {
  return request({
    url: "/report/list/list",
    method: "get",
    params,
  });
};

/* 科情监测-Use */
const KeLIstUse = (params) => {
  return request({
    url: "/article/monitoring/source/search",
    method: "post",
    data: params,
  });
};
/* 导出科情监测列表 */
const downLoadExportExcel = (params) => {
  return request({
    url: "/article/monitoring/export",
    responseType: "blob",
    method: "post",
    data: params,
  });
};
/* 专题列表 */
const specialLIstUse = (params) => {
  return request({
    url: "/article/special/source/search",
    method: "post",
    data: params,
  });
};
// 查询专题
const getspecialLIstUse = (id) => {
  return request({
    url: "/article/monitoring/" + id,
    method: "get",
  });
};
/* 导出科情监测列表 */
const downLoadExcel = (params) => {
  return request({
    url: "/article/special/export",
    responseType: "blob",
    method: "post",
    data: params,
  });
};
/* 科情监测集成接口 */
const KeIntegration = (params) => {
  return request({
    url: "/article/monitoring/source/esRetrieval",
    method: "post",
    data: params,
  });
};
/* 专题媒体列表 */
const mediumLIst = (params) => {
  return request({
    url: "/article/source/type",
    method: "post",
    data: params,
  });
};
/* 科情媒体列表 */
const monitoringMedium = (params) => {
  return request({
    url: "/article/monitoring/type",
    method: "post",
    data: params,
  });
};
/* 科情生成快照 */
const downLoadExportKe = (params) => {
  return request({
    url: "/article/monitoring/generate/snapshot",
    method: "post",
    data: params,
  });
};
/*专题生成快照 */
const downLoadExportZhuan = (params) => {
  return request({
    url: "/article/special/generate/snapshot",
    method: "post",
    data: params,
  });
};
/* 科情附件下载提价 */
const documentDownload = (params) => {
  return request({
    url: "/article/monitoring/generate/file/" + params,
    method: "get",
  });
};
/* 专题附件下载 */
const downLoadSpecialSubject = (params) => {
  return request({
    url: "/article/special/generate/file/" + params,
    method: "get",
  });
};
/* 科情附件下载 */
const documentDownloadKe = (params) => {
  return request({
    url: "/article/monitoring/download/file",
    method: "post",
    data: params,
    responseType: "blob",
    headers: {
      "Content-Type": "multipart/form-data; boundary=something",
      Accept: "*/*",
    },
  });
};
// 新的专题监测
const newKeIntegration = (params) => {
  return request({
    url: "/article/monitoring/thematic/testing",
    method: "post",
    data: params,
  });
};
/* 科情附件下载 */
const downloadFile = (params) => {
  return request({
    url: "/article/articleList/download/file",
    method: "post",
    data: params,
    responseType: "blob",
    headers: {
      "Content-Type": "multipart/form-data; boundary=something",
      Accept: "*/*",
    },
  });
};
const getSourceList = (data) => {
  return request({
    url: "/article/source/listAll",
    method: "get",
    params: data,
  });
};
const translationTitle = (data) => {
  return request({
    url: "/article/translation/title",
    method: "post",
    data: data,
    timeout: 5 * 60 * 1000,
  });
};
const monitoringEsRemove = (data) => {
  return request({
    url: "/article/monitoring/es/remove",
    method: "post",
    data: data,
  });
};
const translationContent = (data) => {
  return request({
    url: "/article/translation/data/processing",
    method: "post",
    data: data,
    timeout: 5 * 60 * 1000,
  });
};
/* 科情采编的数据源统计 */
const countByWorkSourceSn = (params) => {
  return request({
    url: "/article/work/countByWorkSourceSn",
    method: "get",
    params: params,
    timeout: 1000000,
  });
};

// 查询图谱
const searchGraphData = (data) => {
  return request({
    url: "/graph/searchRelatedGraphData",
    method: "post",
    data: data,
    timeout: 5 * 60 * 1000,
  });
};

// 发布到每日最新热点
const publishEverydayHot = (data) => {
  return request({
    url: `/myarticle/articleList/joinHotArticle/${data}`,
    method: "post",
  });
};

// 批量删除
const batchRemove = (data) => {
  return request({
    url: `/article/monitoring/es/batchremove`,
    method: "post",
    data: data,
  });
};

/* 前沿科技动态查询列表 */
const qykjdtArticleList = (params) => {
  return request({
    url: "/article/articleList/qydtArticleList",
    method: "get",
    params: params,
  });
};

/* 科技资讯推荐 - 获取分类列表 */
const getCategoryList = (params) => {
  return request({
    url: "/article/recommend/categoryList",
    method: "get",
    params: params,
  });
};

/* 科技资讯推荐 - 获取推荐列表 */
const getRecommendList = (params) => {
  return request({
    url: "/article/recommend/list",
    method: "get",
    params: params,
  });
};

export default {
  documentDownloadKe,
  downLoadSpecialSubject,
  documentDownload,
  weChatList,
  downLoadExportZhuan,
  downLoadExportKe,
  mediumLIst,
  monitoringMedium,
  KeIntegration,
  downLoadExcel,
  specialLIstUse,
  getspecialLIstUse,
  downLoadExportExcel,
  KeLIstUse,
  AreaInfo,
  KeLIst,
  Addmonitoring,
  monitoringList,
  monitoringInfo,
  EsSeach,
  editMonitoring,
  deleteMonitoring,
  downLoadExport,
  addSpecial,
  SpecialList,
  SpecialEs,
  getSpecialEs,
  EditSpecialEs,
  deleteSpecialEs,
  getNewBuilt,
  AddReport,
  collectApi,
  cocelCollect,
  areaList,
  industry,
  tagAdd,
  getAreaList,
  GetWechatList,
  esRetrieval,
  newKeIntegration,
  downloadFile,
  getSourceList,
  translationTitle,
  translationContent,
  monitoringEsRemove,
  wechatCountSourceName,
  recommendAdd,
  recommendCancel,
  recommendList,
  recommendManageList,
  recommendQueryUser,
  recommendHot,
  browseAdd,
  browseCancel,
  browseList,
  countByWorkSourceSn,
  searchGraphData,
  articleDetail,
  batchImportReports,
  getReportsList,
  publishEverydayHot,
  batchRemove,
  qykjdtArticleList,
  // 科技资讯推荐相关API
  getCategoryList,
  getRecommendList,
};

{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\kejizixuntuijian\\index.vue?vue&type=style&index=0&id=172a71e8&lang=scss&scoped=true", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\kejizixuntuijian\\index.vue", "mtime": 1754447229704}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmxlZnQtcGFuZWwgew0KICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA2MHB4KTsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTsNCn0NCg0KLmxlZnQtdGFibGUtY29udGFpbmVyIHsNCiAgZmxleDogMTsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi5sZWZ0LXBhZ2luYXRpb24gew0KICBib3JkZXItdG9wOiAxcHggc29saWQgI2ViZWVmNTsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQoucmlnaHQtcGFuZWwgew0KICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA2MHB4KTsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTsNCn0NCg0KLnJpZ2h0LXRhYmxlLWNvbnRhaW5lciB7DQogIGZsZXg6IDE7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGJhY2tncm91bmQtY29sb3I6IHdoaXRlOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi5yaWdodC1wYWdpbmF0aW9uIHsNCiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlYmVlZjU7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLy8g5bem5L6n6KGo5qC86KGM6YCJ5Lit5qC35byPDQoubGVmdC10YWJsZS1jb250YWluZXIgew0KICA6OnYtZGVlcCAuZWwtdGFibGVfX3JvdyB7DQogICAgY3Vyc29yOiBwb2ludGVyOw0KICB9DQoNCiAgOjp2LWRlZXAgLmVsLXRhYmxlX19yb3c6aG92ZXIgew0KICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQogIH0NCg0KICA6OnYtZGVlcCAuY3VycmVudC1yb3cgew0KICAgIGJhY2tncm91bmQtY29sb3I6ICNlY2Y1ZmYgIWltcG9ydGFudDsNCiAgfQ0KfQ0KDQovLyDliIbpobXnu4Tku7bmoLflvI/osIPmlbQNCjo6di1kZWVwIC5lbC1wYWdpbmF0aW9uIHsNCiAgdG9wOiAtMnB4Ow0KICAuZWwtcGFnaW5hdGlvbl9fc2l6ZXMgew0KICAgIG1hcmdpbi10b3A6IC0ycHg7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0ZA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/kejiz<PERSON><PERSON><PERSON>an", "sourcesContent": ["<template>\r\n  <div v-loading=\"globalLoading\" element-loading-text=\"数据加载中\">\r\n    <splitpanes class=\"default-theme\">\r\n      <!-- 左侧面板 -->\r\n      <pane\r\n        class=\"leftLink\"\r\n        ref=\"leftLink\"\r\n        min-size=\"20\"\r\n        max-size=\"50\"\r\n        size=\"30\"\r\n      >\r\n        <div class=\"left-panel\">\r\n          <!-- 左侧表格 -->\r\n          <div class=\"left-table-container\">\r\n            <el-table\r\n              :data=\"leftTableData\"\r\n              v-loading=\"leftLoading\"\r\n              @row-click=\"handleLeftRowClick\"\r\n              :highlight-current-row=\"true\"\r\n              ref=\"leftTable\"\r\n              height=\"auto\"\r\n              size=\"small\"\r\n              border\r\n              :show-header=\"false\"\r\n              style=\"font-size: 14px\"\r\n            >\r\n              <el-table-column\r\n                type=\"index\"\r\n                label=\"序号\"\r\n                width=\"60\"\r\n                align=\"center\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  {{ (leftCurrentPage - 1) * leftPageSize + scope.$index + 1 }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"title\" label=\"标题\" show-overflow-tooltip>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <!-- 左侧分页 -->\r\n            <div class=\"left-pagination\">\r\n              <pagination\r\n                v-show=\"leftTotal > 0\"\r\n                :total=\"leftTotal\"\r\n                :page.sync=\"leftCurrentPage\"\r\n                :limit.sync=\"leftPageSize\"\r\n                @pagination=\"handleLeftPagination\"\r\n                :layout=\"'total, prev, pager, next'\"\r\n                :background=\"false\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </pane>\r\n\r\n      <!-- 右侧面板 -->\r\n      <pane min-size=\"50\" max-size=\"80\" size=\"70\">\r\n        <div class=\"right-panel\">\r\n          <!-- 右侧表格 -->\r\n          <div class=\"right-table-container\">\r\n            <el-table\r\n              :data=\"rightTableData\"\r\n              v-loading=\"rightLoading\"\r\n              height=\"auto\"\r\n              size=\"small\"\r\n              border\r\n              style=\"font-size: 14px\"\r\n            >\r\n              <el-table-column\r\n                prop=\"score\"\r\n                label=\"推荐分值\"\r\n                width=\"100\"\r\n                align=\"center\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag :type=\"getScoreTagType(scope.row.score)\" size=\"small\">\r\n                    {{ scope.row.score }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"title\" label=\"标题\" show-overflow-tooltip>\r\n                <template slot-scope=\"scope\">\r\n                  <el-link\r\n                    type=\"primary\"\r\n                    @click=\"openDetail(scope.row)\"\r\n                    :underline=\"false\"\r\n                  >\r\n                    {{ scope.row.title }}\r\n                  </el-link>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"sourceName\"\r\n                label=\"来源名称\"\r\n                width=\"150\"\r\n                show-overflow-tooltip\r\n              >\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"publishTime\"\r\n                label=\"发布日期\"\r\n                width=\"120\"\r\n                align=\"center\"\r\n              >\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"field\"\r\n                label=\"所属领域\"\r\n                width=\"120\"\r\n                show-overflow-tooltip\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag size=\"mini\" v-if=\"scope.row.field\">\r\n                    {{ scope.row.field }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <!-- 右侧分页 -->\r\n            <div class=\"right-pagination\">\r\n              <pagination\r\n                v-show=\"rightTotal > 0\"\r\n                :total=\"rightTotal\"\r\n                :page.sync=\"rightCurrentPage\"\r\n                :limit.sync=\"rightPageSize\"\r\n                @pagination=\"handleRightPagination\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </pane>\r\n    </splitpanes>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { Splitpanes, Pane } from \"splitpanes\";\r\nimport \"splitpanes/dist/splitpanes.css\";\r\nimport API from \"@/api/ScienceApi/index.js\";\r\n\r\nexport default {\r\n  name: \"KejizixuntuijianIndex\",\r\n  components: {\r\n    Splitpanes,\r\n    Pane,\r\n  },\r\n  data() {\r\n    return {\r\n      // 全局加载状态\r\n      globalLoading: false,\r\n\r\n      // 左侧数据\r\n      leftTableData: [],\r\n      leftLoading: false,\r\n      leftTotal: 0,\r\n      leftCurrentPage: 1,\r\n      leftPageSize: 20,\r\n      leftSearchTimer: null,\r\n      selectedLeftRow: null, // 当前选中的左侧行\r\n\r\n      // 右侧数据\r\n      rightTableData: [],\r\n      rightLoading: false,\r\n      rightTotal: 0,\r\n      rightCurrentPage: 1,\r\n      rightPageSize: 20,\r\n    };\r\n  },\r\n  created() {\r\n    this.initData();\r\n  },\r\n  methods: {\r\n    // 初始化数据\r\n    async initData() {\r\n      this.globalLoading = true;\r\n      try {\r\n        await this.getLeftTableData();\r\n      } catch (error) {\r\n        console.error(\"初始化数据失败:\", error);\r\n        this.$message.error(\"数据加载失败，请重试\");\r\n      } finally {\r\n        this.globalLoading = false;\r\n      }\r\n    },\r\n\r\n    // 获取左侧表格数据\r\n    async getLeftTableData() {\r\n      this.leftLoading = true;\r\n      try {\r\n        // 尝试调用真实API，如果失败则使用模拟数据\r\n        try {\r\n          const params = {\r\n            pageNum: this.leftCurrentPage,\r\n            pageSize: this.leftPageSize,\r\n          };\r\n          const response = await API.getCategoryList(params);\r\n          this.leftTableData = response.rows || response.data || [];\r\n          this.leftTotal = response.total || 0;\r\n        } catch (apiError) {\r\n          console.warn(\"API调用失败，使用模拟数据:\", apiError);\r\n          // 使用模拟数据作为后备\r\n          const mockData = this.generateMockLeftData();\r\n          this.leftTableData = mockData.rows;\r\n          this.leftTotal = mockData.total;\r\n        }\r\n\r\n        // 如果是第一次加载且有数据，自动选中第一行\r\n        if (\r\n          this.leftCurrentPage === 1 &&\r\n          this.leftTableData.length > 0 &&\r\n          !this.selectedLeftRow\r\n        ) {\r\n          this.$nextTick(() => {\r\n            this.handleLeftRowClick(this.leftTableData[0]);\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取左侧数据失败:\", error);\r\n        this.$message.error(\"获取分类数据失败\");\r\n      } finally {\r\n        this.leftLoading = false;\r\n      }\r\n    },\r\n\r\n    // 生成模拟左侧数据\r\n    generateMockLeftData() {\r\n      const categories = [\r\n        \"人工智能技术\",\r\n        \"量子计算研究\",\r\n        \"生物医学工程\",\r\n        \"新能源技术\",\r\n        \"航空航天技术\",\r\n        \"材料科学\",\r\n        \"信息安全技术\",\r\n        \"机器人技术\",\r\n        \"区块链技术\",\r\n        \"物联网技术\",\r\n        \"5G通信技术\",\r\n        \"自动驾驶技术\",\r\n        \"虚拟现实技术\",\r\n        \"基因编辑技术\",\r\n        \"纳米技术\",\r\n      ];\r\n\r\n      const total = categories.length;\r\n      const startIndex = (this.leftCurrentPage - 1) * this.leftPageSize;\r\n      const endIndex = Math.min(startIndex + this.leftPageSize, total);\r\n\r\n      let filteredCategories = categories;\r\n\r\n      const rows = filteredCategories\r\n        .slice(startIndex, endIndex)\r\n        .map((title, index) => ({\r\n          id: startIndex + index + 1,\r\n          title: title,\r\n        }));\r\n\r\n      return {\r\n        rows,\r\n        total: filteredCategories.length,\r\n      };\r\n    },\r\n\r\n    // 获取右侧表格数据\r\n    async getRightTableData() {\r\n      if (!this.selectedLeftRow) {\r\n        this.rightTableData = [];\r\n        this.rightTotal = 0;\r\n        return;\r\n      }\r\n\r\n      this.rightLoading = true;\r\n      try {\r\n        // 尝试调用真实API，如果失败则使用模拟数据\r\n        try {\r\n          const params = {\r\n            categoryId: this.selectedLeftRow.id,\r\n            pageNum: this.rightCurrentPage,\r\n            pageSize: this.rightPageSize,\r\n          };\r\n          const response = await API.getRecommendList(params);\r\n          this.rightTableData = response.rows || response.data || [];\r\n          this.rightTotal = response.total || 0;\r\n        } catch (apiError) {\r\n          console.warn(\"API调用失败，使用模拟数据:\", apiError);\r\n          // 使用模拟数据作为后备\r\n          const mockData = this.generateMockRightData();\r\n          this.rightTableData = mockData.rows;\r\n          this.rightTotal = mockData.total;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取右侧数据失败:\", error);\r\n        this.$message.error(\"获取推荐数据失败\");\r\n      } finally {\r\n        this.rightLoading = false;\r\n      }\r\n    },\r\n\r\n    // 生成模拟右侧数据\r\n    generateMockRightData() {\r\n      const mockArticles = [\r\n        {\r\n          id: 1,\r\n          title: \"人工智能在医疗诊断中的最新突破\",\r\n          score: 95,\r\n          sourceName: \"科技日报\",\r\n          publishTime: \"2024-01-15\",\r\n          field: \"人工智能\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"量子计算机实现新的里程碑\",\r\n          score: 88,\r\n          sourceName: \"自然杂志\",\r\n          publishTime: \"2024-01-14\",\r\n          field: \"量子计算\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"基因编辑技术的伦理考量\",\r\n          score: 82,\r\n          sourceName: \"生物技术周刊\",\r\n          publishTime: \"2024-01-13\",\r\n          field: \"生物技术\",\r\n        },\r\n        {\r\n          id: 4,\r\n          title: \"新能源汽车电池技术革新\",\r\n          score: 76,\r\n          sourceName: \"能源观察\",\r\n          publishTime: \"2024-01-12\",\r\n          field: \"新能源\",\r\n        },\r\n        {\r\n          id: 5,\r\n          title: \"5G网络安全防护新方案\",\r\n          score: 71,\r\n          sourceName: \"通信世界\",\r\n          publishTime: \"2024-01-11\",\r\n          field: \"通信技术\",\r\n        },\r\n      ];\r\n\r\n      // 根据选中的分类生成相关数据\r\n      const categoryRelatedArticles = mockArticles.map((article, index) => ({\r\n        ...article,\r\n        id: (this.rightCurrentPage - 1) * this.rightPageSize + index + 1,\r\n        title: `${this.selectedLeftRow.title}相关：${article.title}`,\r\n        field: this.selectedLeftRow.title,\r\n      }));\r\n\r\n      const total = 50; // 模拟总数\r\n      const startIndex = (this.rightCurrentPage - 1) * this.rightPageSize;\r\n      const rows = categoryRelatedArticles.slice(\r\n        0,\r\n        Math.min(this.rightPageSize, total - startIndex)\r\n      );\r\n\r\n      return {\r\n        rows,\r\n        total,\r\n      };\r\n    },\r\n\r\n    // 处理左侧行点击\r\n    handleLeftRowClick(row) {\r\n      this.selectedLeftRow = row;\r\n      this.$refs.leftTable.setCurrentRow(row);\r\n\r\n      // 重置右侧分页并加载数据\r\n      this.rightCurrentPage = 1;\r\n      this.getRightTableData();\r\n    },\r\n\r\n    // 处理左侧分页\r\n    handleLeftPagination({ page, limit }) {\r\n      this.leftCurrentPage = page;\r\n      this.leftPageSize = limit;\r\n      this.getLeftTableData();\r\n    },\r\n\r\n    // 处理右侧分页\r\n    handleRightPagination({ page, limit }) {\r\n      this.rightCurrentPage = page;\r\n      this.rightPageSize = limit;\r\n      this.getRightTableData();\r\n    },\r\n\r\n    // 获取分值标签类型\r\n    getScoreTagType(score) {\r\n      if (score >= 90) return \"success\";\r\n      if (score >= 80) return \"warning\";\r\n      if (score >= 70) return \"info\";\r\n      return \"danger\";\r\n    },\r\n\r\n    // 打开详情页\r\n    openDetail(row) {\r\n      // 这里可以根据实际需求打开详情页\r\n      // 例如：跳转到新页面或打开弹窗\r\n      const url = `/expressDetails?id=${row.id}`;\r\n      window.open(url, \"_blank\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.left-panel {\r\n  height: calc(100vh - 60px);\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.left-table-container {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: white;\r\n  overflow: hidden;\r\n}\r\n\r\n.left-pagination {\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  text-align: center;\r\n}\r\n\r\n.right-panel {\r\n  height: calc(100vh - 60px);\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.right-table-container {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: white;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.right-pagination {\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  text-align: center;\r\n}\r\n\r\n// 左侧表格行选中样式\r\n.left-table-container {\r\n  ::v-deep .el-table__row {\r\n    cursor: pointer;\r\n  }\r\n\r\n  ::v-deep .el-table__row:hover {\r\n    background-color: #f5f7fa;\r\n  }\r\n\r\n  ::v-deep .current-row {\r\n    background-color: #ecf5ff !important;\r\n  }\r\n}\r\n\r\n// 分页组件样式调整\r\n::v-deep .el-pagination {\r\n  top: -2px;\r\n  .el-pagination__sizes {\r\n    margin-top: -2px;\r\n  }\r\n}\r\n</style>\r\n"]}]}
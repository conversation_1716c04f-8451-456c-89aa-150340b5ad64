{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\tabOne.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\tabOne.vue", "mtime": 1754397030175}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgdXNhTWFwIGZyb20gIi4vY29tcG9uZW50cy91c2FNYXAiOw0KaW1wb3J0IHRpbWVMaW5lIGZyb20gIi4vY29tcG9uZW50cy90aW1lTGluZSI7DQppbXBvcnQgZ3JhcGhFY2hhcnRzIGZyb20gIi4vY29tcG9uZW50cy9ncmFwaEVjaGFydHMiOw0KaW1wb3J0IHRlY2hub2xvZ3lBcnRpY2xlcyBmcm9tICIuL2NvbXBvbmVudHMvdGVjaG5vbG9neUFydGljbGVzIjsNCmltcG9ydCB0cnVtcFZpZXdUcmVlIGZyb20gIi4vY29tcG9uZW50cy90cnVtcFZpZXdUcmVlIjsNCmltcG9ydCB2aWV3VHJlZSBmcm9tICIuL2NvbXBvbmVudHMvdmlld1RyZWUiOw0KaW1wb3J0IHBvbGljeVJpc2sgZnJvbSAiLi9zZWNvbmRMZXZlbC9wb2xpY3lSaXNrIjsNCmltcG9ydCBhcnRpY2xlRGV0YWlscyBmcm9tICIuL3NlY29uZExldmVsL2FydGljbGVEZXRhaWxzIjsNCmltcG9ydCBzdXBwcmVzc2lvbk9mUmlza3MgZnJvbSAiLi9zZWNvbmRMZXZlbC9zdXBwcmVzc2lvbk9mUmlza3MiOw0KaW1wb3J0IGVudGVycHJpc2VJbmZvcm1hdGlvbiBmcm9tICIuL3NlY29uZExldmVsL2VudGVycHJpc2VJbmZvcm1hdGlvbiI7DQppbXBvcnQgY29tcGFyaXNvbkNoYXJ0IGZyb20gIi4vc2Vjb25kTGV2ZWwvY29tcGFyaXNvbkNoYXJ0IjsNCmltcG9ydCBob3RUZWNobm9sb2d5IGZyb20gIi4vc2Vjb25kTGV2ZWwvaG90VGVjaG5vbG9neSI7DQppbXBvcnQgYmFhclRyZWVFY2hhcnRzIGZyb20gIi4vY29tcG9uZW50cy9iYWFyVHJlZUVjaGFydHMiOw0KaW1wb3J0IHRlY2hub2xvZ3lEZXRhaWxzIGZyb20gIi4vc2Vjb25kTGV2ZWwvdGVjaG5vbG9neURldGFpbHMiOw0KaW1wb3J0IHRlY2hCdWJibGVEaWFsb2cgZnJvbSAiLi9zZWNvbmRMZXZlbC90ZWNoQnViYmxlRGlhbG9nIjsNCmltcG9ydCBBcnRpY2xlTm90aWZpY2F0aW9uIGZyb20gIkAvY29tcG9uZW50cy9BcnRpY2xlTm90aWZpY2F0aW9uIjsNCmltcG9ydCB7DQogIHRlY2huaWNhbEFydGljbGVEZXRhaWwsDQogIHN1cHByZXNzRGF0YSwNCiAgc3VwcHJlc3NMZXZlbENvdW50LA0KICBzdXBwcmVzc0VudGVycHJpc2VMaXN0LA0KICBzdXBwcmVzc1BhdGVudExpc3QsDQogIHN1cHByZXNzU29mdHdhcmVMaXN0LA0KICBwcm9wb3NhbHNMaXN0LA0KICBwcm9wb3NhbHNUb0NoaW5hRGF0YSwNCiAgcHJvcG9zYWxzQ291bnQsDQogIGtqZHRBcnRpY2xlTGlzdCwNCiAgbG9naW5TSU5BLA0KfSBmcm9tICJAL2FwaS9iaWdTY3JlZW4vc2FuaGFvLmpzIjsNCmltcG9ydCB7DQogIGxhcmdlSG90UXVlcnlCeUlkLA0KICBsYXJnZUdhdGhlclF1ZXJ5R2F0aGVyRGF0YSwNCiAgZ2V0TGFyZ2VGVFQsDQogIGxhcmdlSG90TGlzdDIsDQp9IGZyb20gIkAvYXBpL2JpZ1NjcmVlbi9pbmRleDEiOw0KaW1wb3J0IHsgbWFya09iaiB9IGZyb20gIi4vZGF0YS96aGlrdS5qcyI7DQppbXBvcnQgeyB0cmVlRGF0YTIsIG1hcmtkb3duRGF0YSB9IGZyb20gIi4vZGF0YS9yZW53dS5qcyI7DQppbXBvcnQgTWFya21hcERpYWxvZyBmcm9tICIuLi9iaWdTY3JlZW5UaHJlZS9jb21wb25lbnRzL01hcmttYXBEaWFsb2cudnVlIjsNCmltcG9ydCB7DQogIGNvbnRhaW5zSHRtbFRhZ3MsDQogIGV4dHJhY3RIdG1sVGFncywNCiAgaGFzVmFsaWRIdG1sU3RydWN0dXJlLA0KfSBmcm9tICJAL3V0aWxzL2h0bWxVdGlscyI7DQppbXBvcnQgeyBUcmFuc2Zvcm1lciB9IGZyb20gIm1hcmttYXAtbGliIjsNCmltcG9ydCB7IE1hcmttYXAgfSBmcm9tICJtYXJrbWFwLXZpZXciOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJUYWJPbmUiLA0KICBjb21wb25lbnRzOiB7DQogICAgdXNhTWFwLA0KICAgIHRpbWVMaW5lLA0KICAgIGdyYXBoRWNoYXJ0cywNCiAgICB0ZWNobm9sb2d5QXJ0aWNsZXMsDQogICAgdHJ1bXBWaWV3VHJlZSwNCiAgICB2aWV3VHJlZSwNCiAgICBwb2xpY3lSaXNrLA0KICAgIGFydGljbGVEZXRhaWxzLA0KICAgIHN1cHByZXNzaW9uT2ZSaXNrcywNCiAgICBlbnRlcnByaXNlSW5mb3JtYXRpb24sDQogICAgY29tcGFyaXNvbkNoYXJ0LA0KICAgIGhvdFRlY2hub2xvZ3ksDQogICAgYmFhclRyZWVFY2hhcnRzLA0KICAgIHRlY2hub2xvZ3lEZXRhaWxzLA0KICAgIHRlY2hCdWJibGVEaWFsb2csDQogICAgTWFya21hcERpYWxvZywNCiAgICBBcnRpY2xlTm90aWZpY2F0aW9uLA0KICB9LA0KICBwcm9wczogew0KICAgIG5vdGlmaWNhdGlvbkFydGljbGVzOiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdLA0KICAgIH0sDQogICAgc2hvd05vdGlmaWNhdGlvbjogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlLA0KICAgIH0sDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHBvbGljeVJpc2tTaG93TW9kYWw6IGZhbHNlLA0KICAgICAgY29tcGFyaXNvbkNoYXJ0U2hvd01vZGFsOiBmYWxzZSwNCiAgICAgIGhvdFRlY2hub2xvZ3lTaG93TW9kYWw6IGZhbHNlLA0KICAgICAgaG90VGVjaG5vbG9neXRUaXRsZTogIiIsDQogICAgICBob3RUZWNobm9sb2d5dElEOiBudWxsLA0KICAgICAgYmFhclRyZWVFY2hhcnRzU2hvd01vZGFsOiBmYWxzZSwNCiAgICAgIGJhYXJUcmVlRWNoYXJ0c1R5cGU6IG51bGwsDQogICAgICB0ZWNobm9sb2d5RGV0YWlsc1Nob3dNb2RhbDogZmFsc2UsDQogICAgICB0ZWNobm9sb2d5RGV0YWlsc1RpdGxlOiAiIiwNCiAgICAgIHRlY2hub2xvZ3lEZXRhaWxzSXRlbTogbnVsbCwNCiAgICAgIHN1cHByZXNzaW9uT2ZSaXNrc1Nob3dNb2RhbDogZmFsc2UsDQogICAgICBlbnRlcnByaXNlSW5mb3JtYXRpb25TaG93TW9kYWw6IGZhbHNlLA0KICAgICAgZW50ZXJwcmlzZUluZm9ybWF0aW9uVGl0bGU6ICIiLA0KICAgICAgYXJ0aWNsZURldGFpbHNTaG93TW9kYWw6IGZhbHNlLA0KICAgICAgYXJ0aWNsZURldGFpbHNUaXRsZTogIiIsDQogICAgICBhcnRpY2xlRGV0YWlsc0NvbnRlbnQ6ICIiLA0KICAgICAgYXJ0aWNsZURldGFpbHNDb250ZW50RW46ICIiLA0KICAgICAgc3VwcHJlc3NMaXN0RGF0YTogW10sDQogICAgICByaXNrQmFyQ2hhcnREYXRhOiBbXSwNCiAgICAgIHJpc2tFbnRlcnByaXNlTGlzdDogW10sDQogICAgICByaXNrRW50ZXJwcmlzZUxpc3RUb3RhbDogMCwNCiAgICAgIGVudGVycHJpc2VJbmZvcm1hdGlvbkNvbnRlbnQ6IHt9LA0KICAgICAgcGF0ZW50TGlzdDogW10sDQogICAgICBzb2Z0d2FyZUxpc3Q6IFtdLA0KICAgICAgcGF0ZW50VG90YWw6IDAsDQogICAgICBzb2Z0d2FyZVRvdGFsOiAwLA0KICAgICAgcG9saWN5Umlza0xpc3QxOiBbXSwNCiAgICAgIHBvbGljeVJpc2tMaXN0MjogW10sDQogICAgICBwb2xpY3lSaXNrTGlzdDFUb3RhbDogMCwNCiAgICAgIC8vIOe+juWbveWcsOWbvuaVsOaNrg0KICAgICAgdXNhTWFwRGF0YTogbnVsbCwNCiAgICAgIGFydGljbGVEZXRhaWxzSXRlbToge30sDQogICAgICAvLyDng63ngrnmjqjojZDnm7jlhbPmlbDmja4NCiAgICAgIHJlbWVuZ3dlbnpoYW5nTGlzdDogW10sDQogICAgICBzY3JvbGxUaW1lcjogbnVsbCwNCiAgICAgIHNjcm9sbFRpbWVyMTogbnVsbCwNCiAgICAgIHNjcm9sbFRpbWVyMjogbnVsbCwNCiAgICAgIGlzSG92ZXJlZDogZmFsc2UsDQogICAgICBpc0hvdmVyZWQxOiBmYWxzZSwNCiAgICAgIGlzSG92ZXJlZDI6IGZhbHNlLA0KICAgICAgc2Nyb2xsU3RlcDogMSwNCiAgICAgIGRyYXdlckluZm86IHt9LA0KICAgICAgYXJ0aWNsZURpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgZm9udFNpemU6IDE2LA0KICAgICAgb3JpRm9udFNpemU6IDIwLA0KICAgICAgLy8g5Lq654mp6KeC54K55pWw5o2uDQogICAgICBjaGFyYWN0ZXJWaWV3RGF0YTogW10sDQogICAgICAvLyDmmbrlupPop4LngrnmlbDmja4NCiAgICAgIHRoaW5rVGFua1ZpZXdEYXRhOiBbXSwNCiAgICAgIHJlbWVuZ3dlbnpoYW5nTGlzdDE6IFtdLA0KICAgICAgYWN0aXZlQnV0dG9uOiBudWxsLA0KICAgICAgcWlhbnlhbmtlamlMaXN0OiBbXSwNCiAgICAgIGdhdGhlclRvdGFsOiAwLA0KICAgICAgZ2F0aGVyRGF5TnVtYmVyOiAwLA0KICAgICAgbWFya21hcFZpc2libGU6IGZhbHNlLA0KICAgICAgbWFya21hcENvbnRlbnQ6ICIiLA0KICAgICAgbWFya21hcFRpdGxlOiAi5pm65bqT6KeC54K5IiwNCiAgICAgIGFpTG9hZGluZzogZmFsc2UsDQogICAgICBmcm9udExvZ2luUGFyYW1zOiB7DQogICAgICAgIHVzZXJuYW1lOiAiZ3VhbmxpeXVhbiIsDQogICAgICAgIHBhc3N3b3JkOiAiMTIzNDU2IiwNCiAgICAgIH0sDQogICAgICBmcm9udFRva2VuOiAiIiwNCiAgICAgIC8vIOaKgOacr+mihuWfn+ebuOWFsw0KICAgICAgYWN0aXZlVGVjaEJ1dHRvbjogIjExIiwgLy8g6buY6K6k6YCJ5Lit5paw6IO95rqQDQogICAgICBjdXJyZW50VGVjaFNjcmVlblNuOiAiMTEiLCAvLyDlvZPliY3mioDmnK/poobln5/nmoRzY3JlZW5Tbg0KICAgICAgLy8g5oqA5pyv6aKG5Z+f5rOh5rOh5Zu+5by556qX55u45YWzDQogICAgICB0ZWNoQnViYmxlRGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICB0ZWNoQnViYmxlRGlhbG9nVGl0bGU6ICIiLA0KICAgICAgdGVjaEJ1YmJsZURpYWxvZ1NjcmVlblNuOiAiIiwNCiAgICAgIC8vIOaZuuW6k+ingueCuXRhYuWIh+aNog0KICAgICAgYWN0aXZlVGFiOiAidHJ1bXAiLCAvLyDpu5jorqTmmL7npLrnibnmnJfmma50YWINCiAgICAgIGRvbWFpbk1hcmtkb3duOiAiIiwNCiAgICAgIHNpbmFVcmw6ICIiLA0KICAgICAgemhpa3VBY3RpdmU6IDAsDQogICAgfTsNCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICAvLyDliqjmgIHorqHnrpfng63pl6jmlofnq6DliJfooajmoYbnmoTmoLflvI8NCiAgICByZW1lbmd3ZW56aGFuZ0JveFN0eWxlKCkgew0KICAgICAgY29uc3Qgbm90aWZpY2F0aW9uSGVpZ2h0ID0gMTEwOyAvLyDpgJrnn6Xnu4Tku7bnmoTpq5jluqYNCg0KICAgICAgaWYgKHRoaXMuc2hvd05vdGlmaWNhdGlvbikgew0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIGhlaWdodDogYGNhbGMoMTAwJSAtICR7bm90aWZpY2F0aW9uSGVpZ2h0fXB4KWAsDQogICAgICAgIH07DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIGhlaWdodDogYDEwMCVgLA0KICAgICAgICB9Ow0KICAgICAgfQ0KICAgIH0sDQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgLy8g6LCD55So55m75b2V5paw5rWq5o6l5Y+jDQogICAgbG9naW5TSU5BKCkNCiAgICAgIC50aGVuKChyZXMpID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coIuaWsOa1queZu+W9leaIkOWKnyIpOw0KICAgICAgICB0aGlzLnNpbmFVcmwgPSByZXM7DQogICAgICB9KQ0KICAgICAgLmNhdGNoKChlcnJvcikgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCLmlrDmtarnmbvlvZXlpLHotKU6IiwgZXJyb3IpOw0KICAgICAgfSk7DQoNCiAgICB0aGlzLmdldFN1cHByZXNzRGF0YSgpOw0KICAgIHRoaXMuaW5pdEhvdExpc3QoKTsNCiAgICB0aGlzLmluaXRIb3RMaXN0MSgpOw0KICAgIHRoaXMudXBkYXRlU2Nyb2xsYmFyKCk7DQogICAgdGhpcy51cGRhdGVTY3JvbGxiYXIxKCk7DQogICAgdGhpcy5mZXRjaFVzYU1hcERhdGEoKTsNCiAgICBsYXJnZUdhdGhlclF1ZXJ5R2F0aGVyRGF0YSh7fSkudGhlbigocmVzKSA9PiB7DQogICAgICB0aGlzLmdhdGhlclRvdGFsID0gcmVzLmRhdGEuZ2F0aGVyVG90YWw7DQogICAgICB0aGlzLmdhdGhlckRheU51bWJlciA9IHJlcy5kYXRhLmdhdGhlckRheU51bWJlcjsNCiAgICB9KTsNCiAgfSwNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICB0aGlzLmNsZWFyU2Nyb2xsVGltZXIoKTsNCiAgICB0aGlzLmNsZWFyU2Nyb2xsVGltZXIxKCk7DQogICAgdGhpcy5jbGVhclNjcm9sbFRpbWVyMigpOw0KICAgIHRoaXMuaGFuZGxlTWFya21hcENsb3NlKCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDojrflj5bnvo7lm73lnLDlm77mlbDmja4NCiAgICBhc3luYyBmZXRjaFVzYU1hcERhdGEoKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHByb3Bvc2Fsc0NvdW50KHsNCiAgICAgICAgICBwcm9qZWN0U246ICIxIiwNCiAgICAgICAgICBzY3JlZW5TbjogIjEiLA0KICAgICAgICAgIGNvbHVtblNuOiAiMSIsDQogICAgICAgIH0pOw0KICAgICAgICB0aGlzLnVzYU1hcERhdGEgPSByZXNwb25zZTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuiOt+WPlue+juWbveWcsOWbvuaVsOaNruWksei0pToiLCBlcnJvcik7DQogICAgICB9DQogICAgfSwNCg0KICAgIG9wZW5BcnRpY2xlRGV0YWlscyh0eXBlLCBpdGVtKSB7DQogICAgICB0aGlzLmFydGljbGVEZXRhaWxzSXRlbSA9IGl0ZW07DQogICAgICBzd2l0Y2ggKHR5cGUpIHsNCiAgICAgICAgY2FzZSAidGVjaG5vbG9neS1hcnRpY2xlIjoNCiAgICAgICAgICB0ZWNobmljYWxBcnRpY2xlRGV0YWlsKHsgaWQ6IGl0ZW0uaWQgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICB0aGlzLmFydGljbGVEZXRhaWxzVGl0bGUgPSBpdGVtLnRpdGxlOw0KICAgICAgICAgICAgdGhpcy5hcnRpY2xlRGV0YWlsc0NvbnRlbnQgPSByZXMuZGF0YS5jb250ZW50Ow0KICAgICAgICAgICAgdGhpcy5hcnRpY2xlRGV0YWlsc0NvbnRlbnRFbiA9IHJlcy5kYXRhLmVuQ29udGVudDsNCiAgICAgICAgICAgIHRoaXMuYXJ0aWNsZURldGFpbHNTaG93TW9kYWwgPSB0cnVlOw0KICAgICAgICAgIH0pOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICJlbnRlcnByaXNlSW5mb3JtYXRpb24tbmV3cyI6DQogICAgICAgICAgdGhpcy5vcGVuTmV3VmlldyhpdGVtKTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAicG9saWN5Umlzay1uZXdzIjoNCiAgICAgICAgICB0aGlzLm9wZW5OZXdWaWV3KGl0ZW0pOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgfQ0KICAgIH0sDQogICAgb3BlbkVudGVycHJpc2VJbmZvcm1hdGlvbihpdGVtKSB7DQogICAgICBzdXBwcmVzc1BhdGVudExpc3Qoew0KICAgICAgICBzdXBwcmVzc1NuOiBpdGVtLnN1cHByZXNzU24sDQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLnBhdGVudExpc3QgPSByZXMucm93czsNCiAgICAgICAgdGhpcy5wYXRlbnRUb3RhbCA9IHJlcy50b3RhbDsNCiAgICAgIH0pOw0KICAgICAgc3VwcHJlc3NTb2Z0d2FyZUxpc3Qoew0KICAgICAgICBzdXBwcmVzc1NuOiBpdGVtLnN1cHByZXNzU24sDQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLnNvZnR3YXJlTGlzdCA9IHJlcy5yb3dzOw0KICAgICAgICB0aGlzLnNvZnR3YXJlVG90YWwgPSByZXMudG90YWw7DQogICAgICB9KTsNCiAgICAgIHRoaXMuZW50ZXJwcmlzZUluZm9ybWF0aW9uQ29udGVudCA9IHsgLi4uaXRlbSB9Ow0KICAgICAgdGhpcy5lbnRlcnByaXNlSW5mb3JtYXRpb25UaXRsZSA9IGl0ZW0uZW50ZXJwcmlzZU5hbWU7DQogICAgICB0aGlzLmVudGVycHJpc2VJbmZvcm1hdGlvblNob3dNb2RhbCA9IHRydWU7DQogICAgfSwNCg0KICAgIHBhdGVudFBhZ2luYXRpb24oc3VwcHJlc3NTbiwgcXVlcnlQYXJhbXMpIHsNCiAgICAgIHN1cHByZXNzUGF0ZW50TGlzdCh7DQogICAgICAgIHN1cHByZXNzU246IHN1cHByZXNzU24sDQogICAgICAgIC4uLnF1ZXJ5UGFyYW1zLA0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMucGF0ZW50TGlzdCA9IHJlcy5yb3dzOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIHNvZnR3YXJlUGFnaW5hdGlvbihzdXBwcmVzc1NuLCBxdWVyeVBhcmFtcykgew0KICAgICAgc3VwcHJlc3NTb2Z0d2FyZUxpc3Qoew0KICAgICAgICBzdXBwcmVzc1NuOiBzdXBwcmVzc1NuLA0KICAgICAgICAuLi5xdWVyeVBhcmFtcywNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLnNvZnR3YXJlTGlzdCA9IHJlcy5yb3dzOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIGdldFN1cHByZXNzRGF0YSgpIHsNCiAgICAgIHN1cHByZXNzRGF0YSh7DQogICAgICAgIHByb2plY3RTbjogIjEiLA0KICAgICAgICBzY3JlZW5TbjogIjEiLA0KICAgICAgICBjb2x1bW5TbjogIjEiLA0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGxldCBkYXRhID0gW107DQogICAgICAgIE9iamVjdC5rZXlzKHJlcy5kYXRhKS5mb3JFYWNoKChrZXkpID0+IHsNCiAgICAgICAgICBkYXRhLnB1c2goew0KICAgICAgICAgICAgZGF0ZToga2V5LA0KICAgICAgICAgICAgZGVzY3JpcHRpb246DQogICAgICAgICAgICAgIHJlcy5kYXRhW2tleV0ubGVuZ3RoIDw9IDMNCiAgICAgICAgICAgICAgICA/IHJlcy5kYXRhW2tleV0NCiAgICAgICAgICAgICAgICA6IHJlcy5kYXRhW2tleV0uc2xpY2UoDQogICAgICAgICAgICAgICAgICAgIHJlcy5kYXRhW2tleV0ubGVuZ3RoIC0gMywNCiAgICAgICAgICAgICAgICAgICAgcmVzLmRhdGFba2V5XS5sZW5ndGgNCiAgICAgICAgICAgICAgICAgICksDQogICAgICAgICAgfSk7DQogICAgICAgIH0pOw0KICAgICAgICB0aGlzLnN1cHByZXNzTGlzdERhdGEgPSBkYXRhLnJldmVyc2UoKTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICBnZXRSaXNrRGV0YWlsKCkgew0KICAgICAgc3VwcHJlc3NFbnRlcnByaXNlTGlzdCh7DQogICAgICAgIHByb2plY3RTbjogIjEiLA0KICAgICAgICBzY3JlZW5TbjogIjEiLA0KICAgICAgICBjb2x1bW5TbjogIjEiLA0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5yaXNrRW50ZXJwcmlzZUxpc3QgPSByZXMucm93cy5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoew0KICAgICAgICAgIC4uLml0ZW0sDQogICAgICAgICAgdHlwZTogKGluZGV4ICUgMykgKyAxLA0KICAgICAgICB9KSk7DQogICAgICAgIHRoaXMucmlza0VudGVycHJpc2VMaXN0VG90YWwgPSByZXMudG90YWw7DQogICAgICB9KTsNCiAgICAgIHN1cHByZXNzTGV2ZWxDb3VudCh7DQogICAgICAgIHByb2plY3RTbjogIjEiLA0KICAgICAgICBzY3JlZW5TbjogIjEiLA0KICAgICAgICBjb2x1bW5TbjogIjEiLA0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIC8vIOWwhuWvueixoeagvOW8j+i9rOaNouS4uuaVsOe7hOagvOW8jw0KICAgICAgICBjb25zdCBkYXRhID0gT2JqZWN0LmtleXMocmVzLmRhdGEpLm1hcCgoeWVhcikgPT4gKHsNCiAgICAgICAgICBwcm9kdWN0OiB5ZWFyLA0KICAgICAgICAgIOS4pemHjTogcmVzLmRhdGFbeWVhcl0u5Lil6YeNLA0KICAgICAgICAgIOS4gOiIrDogcmVzLmRhdGFbeWVhcl0u5LiA6IisLA0KICAgICAgICAgIOi+g+i9uzogcmVzLmRhdGFbeWVhcl0u6L6D6L27LA0KICAgICAgICB9KSk7DQogICAgICAgIHRoaXMucmlza0JhckNoYXJ0RGF0YSA9IGRhdGE7DQogICAgICAgIHRoaXMuc3VwcHJlc3Npb25PZlJpc2tzU2hvd01vZGFsID0gdHJ1ZTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICByaXNrRW50ZXJwcmlzZUxpc3RQYWdpbmF0aW9uKHF1ZXJ5UGFyYW1zKSB7DQogICAgICBzdXBwcmVzc0VudGVycHJpc2VMaXN0KHsNCiAgICAgICAgcHJvamVjdFNuOiAiMSIsDQogICAgICAgIHNjcmVlblNuOiAiMSIsDQogICAgICAgIGNvbHVtblNuOiAiMSIsDQogICAgICAgIC4uLnF1ZXJ5UGFyYW1zLA0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMucmlza0VudGVycHJpc2VMaXN0ID0gcmVzLnJvd3MubWFwKChpdGVtLCBpbmRleCkgPT4gKHsNCiAgICAgICAgICAuLi5pdGVtLA0KICAgICAgICAgIHR5cGU6IChpbmRleCAlIDMpICsgMSwNCiAgICAgICAgfSkpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIGdldFBvbGljeVJpc2tEZXRhaWwoKSB7DQogICAgICBwcm9wb3NhbHNMaXN0KHsNCiAgICAgICAgcHJvamVjdFNuOiAiMSIsDQogICAgICAgIHNjcmVlblNuOiAiMSIsDQogICAgICAgIGNvbHVtblNuOiAiMSIsDQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLnBvbGljeVJpc2tMaXN0MSA9IHJlcy5yb3dzOw0KICAgICAgICB0aGlzLnBvbGljeVJpc2tMaXN0MVRvdGFsID0gcmVzLnRvdGFsOw0KICAgICAgfSk7DQogICAgICBwcm9wb3NhbHNUb0NoaW5hRGF0YSh7DQogICAgICAgIHByb2plY3RTbjogIjEiLA0KICAgICAgICBzY3JlZW5TbjogIjEiLA0KICAgICAgICBjb2x1bW5TbjogIjEiLA0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMucG9saWN5Umlza0xpc3QyID0gcmVzLmRhdGE7DQogICAgICAgIHRoaXMucG9saWN5Umlza1Nob3dNb2RhbCA9IHRydWU7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgcG9saWN5Umlza1BhZ2luYXRpb24ocXVlcnlQYXJhbXMpIHsNCiAgICAgIHByb3Bvc2Fsc0xpc3Qoew0KICAgICAgICBwcm9qZWN0U246ICIxIiwNCiAgICAgICAgc2NyZWVuU246ICIxIiwNCiAgICAgICAgY29sdW1uU246ICIxIiwNCiAgICAgICAgLi4ucXVlcnlQYXJhbXMsDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5wb2xpY3lSaXNrTGlzdDEgPSByZXMucm93czsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICBvcGVuSG90VGVjaG5vbG9neShkYXRhKSB7DQogICAgICB0aGlzLmhvdFRlY2hub2xvZ3lTaG93TW9kYWwgPSB0cnVlOw0KICAgICAgdGhpcy5ob3RUZWNobm9sb2d5dFRpdGxlID0gZGF0YS50aXRsZTsNCiAgICAgIHRoaXMuaG90VGVjaG5vbG9neXRJRCA9IGRhdGEucmVwb3J0U247DQogICAgfSwNCiAgICBvcGVuYmFhclRyZWVFY2hhcnRzKGRhdGEpIHsNCiAgICAgIC8vIHRoaXMuYmFhclRyZWVFY2hhcnRzVHlwZSA9IHBhcnNlSW50KHRoaXMuJHJlZnMudHJ1bXBWaWV3VHJlZS5ub2RlVHlwZS5yZXBsYWNlKC9cRCsvZywgJycpLCAxMCkNCiAgICAgIC8vIHRoaXMuYmFhclRyZWVFY2hhcnRzU2hvd01vZGFsID0gdHJ1ZTsNCg0KICAgICAgLy8g5qC55o2u5b2T5YmN6YCJ5Lit55qEdGFi5p2l5Yaz5a6a5L2/55So5ZOq5LiqbWFya09iag0KICAgICAgaWYgKHRoaXMuYWN0aXZlVGFiID09PSAidHJ1bXAiKSB7DQogICAgICAgIC8vIOS9v+eUqOWvvOWFpeeahG1hcmtkb3duRGF0YQ0KICAgICAgICB0aGlzLm1hcmttYXBDb250ZW50ID0NCiAgICAgICAgICBtYXJrZG93bkRhdGEudHJ1bXBbdGhpcy4kcmVmcy5jaGFyYWN0ZXJWaWV3VHJlZS5ub2RlVHlwZV07DQogICAgICAgIHRoaXMubWFya21hcFRpdGxlID0gIueJueacl+aZriI7DQogICAgICB9IGVsc2UgaWYgKHRoaXMuYWN0aXZlVGFiID09PSAibXNrIikgew0KICAgICAgICAvLyDpqazmlq/lhYvnmoRtYXJrZG93bkRhdGENCiAgICAgICAgdGhpcy5tYXJrbWFwQ29udGVudCA9DQogICAgICAgICAgbWFya2Rvd25EYXRhLm1za1t0aGlzLiRyZWZzLmNoYXJhY3RlclZpZXdUcmVlLm5vZGVUeXBlXTsNCiAgICAgICAgdGhpcy5tYXJrbWFwVGl0bGUgPSAi5Z+D6ZqGwrfph4zlpKvCt+mprOaWr+WFiyI7DQogICAgICB9IGVsc2UgaWYgKHRoaXMuYWN0aXZlVGFiID09PSAid3MiKSB7DQogICAgICAgIC8vIOS4h+aWr+eahG1hcmtkb3duRGF0YQ0KICAgICAgICB0aGlzLm1hcmttYXBDb250ZW50ID0NCiAgICAgICAgICBtYXJrZG93bkRhdGEud3NbdGhpcy4kcmVmcy5jaGFyYWN0ZXJWaWV3VHJlZS5ub2RlVHlwZV07DQogICAgICAgIHRoaXMubWFya21hcFRpdGxlID0gIuipueWnhuaWr8K35ZSQ57qz5b63wrfkuIfmlq8iOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5tYXJrbWFwQ29udGVudCA9IHRoaXMuZG9tYWluTWFya2Rvd247DQogICAgICAgIHN3aXRjaCAodGhpcy5hY3RpdmVUYWIpIHsNCiAgICAgICAgICBjYXNlICJiZHQiOg0KICAgICAgICAgICAgdGhpcy5tYXJrbWFwVGl0bGUgPSAi5Y2K5a+85L2T6aKG5Z+fIjsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgImdkemIiOg0KICAgICAgICAgICAgdGhpcy5tYXJrbWFwVGl0bGUgPSAi6auY56uv6KOF5aSH5LiO5p2Q5paZIjsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgInhueXFjIjoNCiAgICAgICAgICAgIHRoaXMubWFya21hcFRpdGxlID0gIuaWsOiDvea6kOaxvei9puS4jueUteaxoCI7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlICJzemh6eCI6DQogICAgICAgICAgICB0aGlzLm1hcmttYXBUaXRsZSA9ICLmlbDlrZfljJbovazlnovkuI7lt6XkuJrova/ku7YiOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAibHN6eiI6DQogICAgICAgICAgICB0aGlzLm1hcmttYXBUaXRsZSA9ICLnu7/oibLliLbpgKDkuI7mlrDog73mupAiOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAic3d5eSI6DQogICAgICAgICAgICB0aGlzLm1hcmttYXBUaXRsZSA9ICLnlJ/nianljLvoja/kuI7ljLvnlpflmajmorAiOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMuYWlMb2FkaW5nID0gZmFsc2U7DQogICAgICB0aGlzLm1hcmttYXBWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KDQogICAgb3BlblRlY2hub2xvZ3lEZXRhaWxzKGRhdGEpIHsNCiAgICAgIHRoaXMudGVjaG5vbG9neURldGFpbHNTaG93TW9kYWwgPSB0cnVlOw0KICAgICAgdGhpcy50ZWNobm9sb2d5RGV0YWlsc1RpdGxlID0gZGF0YS5uYW1lOw0KICAgICAgdGhpcy50ZWNobm9sb2d5RGV0YWlsc0l0ZW0gPSBkYXRhLmRhdGE7DQogICAgfSwNCg0KICAgIC8vIOeDreeCueaOqOiNkOebuOWFs+aWueazlQ0KICAgIGluaXRIb3RMaXN0KCkgew0KICAgICAgLy8g5L2/55SoYmlnU2NyZWVuVGhyZWXnm7jlkIznmoRBUEnmjqXlj6MNCiAgICAgIGxhcmdlSG90TGlzdDIoKQ0KICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgdGhpcy5yZW1lbmd3ZW56aGFuZ0xpc3QgPSByZXMuZGF0YSB8fCBbXTsNCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICB0aGlzLnN0YXJ0U2Nyb2xsKCk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCLojrflj5bng63ngrnmjqjojZDmlbDmja7lpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICAgIC8vIOWmguaenEFQSeiwg+eUqOWksei0pe+8jOS9v+eUqOepuuaVsOe7hA0KICAgICAgICAgIHRoaXMucmVtZW5nd2VuemhhbmdMaXN0ID0gW107DQogICAgICAgIH0pOw0KICAgIH0sDQogICAgaW5pdEhvdExpc3QxKCkgew0KICAgICAgdGhpcy5oYW5kbGVCdXR0b25DbGljaygi6ISR5py65o6l5Y+jIik7DQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMuc3RhcnRTY3JvbGwyKCk7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgc3RhcnRTY3JvbGwoKSB7DQogICAgICB0aGlzLmNsZWFyU2Nyb2xsVGltZXIoKTsNCiAgICAgIGNvbnN0IHdyYXBwZXIgPSB0aGlzLiRyZWZzLnNjcm9sbFdyYXBwZXI7DQogICAgICBjb25zdCBjb250ZW50ID0gdGhpcy4kcmVmcy5zY3JvbGxDb250ZW50Ow0KDQogICAgICBpZiAoIXdyYXBwZXIgfHwgIWNvbnRlbnQpIHJldHVybjsNCg0KICAgICAgdGhpcy5zY3JvbGxUaW1lciA9IHNldEludGVydmFsKCgpID0+IHsNCiAgICAgICAgaWYgKHRoaXMuaXNIb3ZlcmVkKSByZXR1cm47DQoNCiAgICAgICAgaWYgKHdyYXBwZXIuc2Nyb2xsVG9wID49IGNvbnRlbnQuc2Nyb2xsSGVpZ2h0IC0gd3JhcHBlci5jbGllbnRIZWlnaHQpIHsNCiAgICAgICAgICB3cmFwcGVyLnNjcm9sbFRvcCA9IDA7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgd3JhcHBlci5zY3JvbGxUb3AgKz0gdGhpcy5zY3JvbGxTdGVwOw0KICAgICAgICB9DQogICAgICAgIHRoaXMudXBkYXRlU2Nyb2xsYmFyKCk7DQogICAgICB9LCA0MCk7DQogICAgfSwNCg0KICAgIGNsZWFyU2Nyb2xsVGltZXIoKSB7DQogICAgICBpZiAodGhpcy5zY3JvbGxUaW1lcikgew0KICAgICAgICBjbGVhckludGVydmFsKHRoaXMuc2Nyb2xsVGltZXIpOw0KICAgICAgICB0aGlzLnNjcm9sbFRpbWVyID0gbnVsbDsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgaGFuZGxlTW91c2VFbnRlcigpIHsNCiAgICAgIHRoaXMuaXNIb3ZlcmVkID0gdHJ1ZTsNCiAgICB9LA0KDQogICAgaGFuZGxlTW91c2VMZWF2ZSgpIHsNCiAgICAgIHRoaXMuaXNIb3ZlcmVkID0gZmFsc2U7DQogICAgICB0aGlzLnN0YXJ0U2Nyb2xsKCk7DQogICAgfSwNCiAgICBzdGFydFNjcm9sbDEoKSB7DQogICAgICB0aGlzLmNsZWFyU2Nyb2xsVGltZXIxKCk7DQogICAgICBjb25zdCB3cmFwcGVyID0gdGhpcy4kcmVmcy5zY3JvbGxXcmFwcGVyMTsNCiAgICAgIGNvbnN0IGNvbnRlbnQgPSB0aGlzLiRyZWZzLnNjcm9sbENvbnRlbnQxOw0KDQogICAgICBpZiAoIXdyYXBwZXIgfHwgIWNvbnRlbnQpIHJldHVybjsNCg0KICAgICAgdGhpcy5zY3JvbGxUaW1lcjEgPSBzZXRJbnRlcnZhbCgoKSA9PiB7DQogICAgICAgIGlmICh0aGlzLmlzSG92ZXJlZDEpIHJldHVybjsNCg0KICAgICAgICBpZiAod3JhcHBlci5zY3JvbGxUb3AgPj0gY29udGVudC5zY3JvbGxIZWlnaHQgLSB3cmFwcGVyLmNsaWVudEhlaWdodCkgew0KICAgICAgICAgIHdyYXBwZXIuc2Nyb2xsVG9wID0gMDsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB3cmFwcGVyLnNjcm9sbFRvcCArPSB0aGlzLnNjcm9sbFN0ZXA7DQogICAgICAgIH0NCiAgICAgICAgdGhpcy51cGRhdGVTY3JvbGxiYXIxKCk7DQogICAgICB9LCAyMCk7DQogICAgfSwNCiAgICBzdGFydFNjcm9sbDIoKSB7DQogICAgICB0aGlzLmNsZWFyU2Nyb2xsVGltZXIyKCk7DQogICAgICB0aGlzLnNjcm9sbFRpbWVyMiA9IHNldEludGVydmFsKCgpID0+IHsNCiAgICAgICAgaWYgKHRoaXMuaXNIb3ZlcmVkMSkgcmV0dXJuOw0KDQogICAgICAgIC8vIOWumuS5ieaJgOaciXRhYuagh+etvueahOmhuuW6jw0KICAgICAgICBjb25zdCB0YWJPcmRlciA9IFsNCiAgICAgICAgICAi6ISR5py65o6l5Y+jIiwNCiAgICAgICAgICAi6YeP5a2Q5L+h5oGvIiwNCiAgICAgICAgICAi5Lq65b2i5py65Zmo5Lq6IiwNCiAgICAgICAgICAi55Sf5oiQ5byP5Lq65bel5pm66IO9IiwNCiAgICAgICAgICAi55Sf54mp5Yi26YCgIiwNCiAgICAgICAgICAi5pyq5p2l5pi+56S6IiwNCiAgICAgICAgICAi5pyq5p2l572R57ucIiwNCiAgICAgICAgICAi5paw5Z6L5YKo6IO9IiwNCiAgICAgICAgICAi5YW25LuWIiwNCiAgICAgICAgXTsNCg0KICAgICAgICAvLyDmib7liLDlvZPliY3mtLvot4PmoIfnrb7nmoTntKLlvJUNCiAgICAgICAgY29uc3QgY3VycmVudEluZGV4ID0gdGFiT3JkZXIuaW5kZXhPZih0aGlzLmFjdGl2ZUJ1dHRvbik7DQogICAgICAgIC8vIOiuoeeul+S4i+S4gOS4quagh+etvueahOe0ouW8le+8jOWmguaenOWIsOacgOWQjuS4gOS4quWImeWbnuWIsOesrOS4gOS4qg0KICAgICAgICBjb25zdCBuZXh0SW5kZXggPSAoY3VycmVudEluZGV4ICsgMSkgJSB0YWJPcmRlci5sZW5ndGg7DQogICAgICAgIC8vIOWIh+aNouWIsOS4i+S4gOS4quagh+etvg0KICAgICAgICB0aGlzLmhhbmRsZUJ1dHRvbkNsaWNrKHRhYk9yZGVyW25leHRJbmRleF0pOw0KICAgICAgfSwgODAwMCk7DQogICAgfSwNCiAgICBjbGVhclNjcm9sbFRpbWVyMSgpIHsNCiAgICAgIGlmICh0aGlzLnNjcm9sbFRpbWVyMSkgew0KICAgICAgICBjbGVhckludGVydmFsKHRoaXMuc2Nyb2xsVGltZXIxKTsNCiAgICAgICAgdGhpcy5zY3JvbGxUaW1lcjEgPSBudWxsOw0KICAgICAgfQ0KICAgIH0sDQogICAgY2xlYXJTY3JvbGxUaW1lcjIoKSB7DQogICAgICBpZiAodGhpcy5zY3JvbGxUaW1lcjIpIHsNCiAgICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLnNjcm9sbFRpbWVyMik7DQogICAgICAgIHRoaXMuc2Nyb2xsVGltZXIyID0gbnVsbDsNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZU1vdXNlRW50ZXIxKCkgew0KICAgICAgdGhpcy5pc0hvdmVyZWQxID0gdHJ1ZTsNCiAgICB9LA0KDQogICAgaGFuZGxlTW91c2VMZWF2ZTEoKSB7DQogICAgICB0aGlzLmlzSG92ZXJlZDEgPSBmYWxzZTsNCiAgICAgIC8vIHRoaXMuc3RhcnRTY3JvbGwxKCk7DQogICAgICAvLyB0aGlzLnN0YXJ0U2Nyb2xsMigpOw0KICAgIH0sDQogICAgaGFuZGxlTW91c2VFbnRlcjIoKSB7DQogICAgICB0aGlzLmlzSG92ZXJlZDIgPSB0cnVlOw0KICAgIH0sDQoNCiAgICBoYW5kbGVNb3VzZUxlYXZlMigpIHsNCiAgICAgIHRoaXMuaXNIb3ZlcmVkMiA9IGZhbHNlOw0KICAgIH0sDQogICAgdXBkYXRlU2Nyb2xsYmFyKCkgew0KICAgICAgY29uc3Qgd3JhcHBlciA9IHRoaXMuJHJlZnMuc2Nyb2xsV3JhcHBlcjsNCiAgICAgIGlmICghd3JhcHBlcikgcmV0dXJuOw0KDQogICAgICBjb25zdCB7IHNjcm9sbFRvcCwgc2Nyb2xsSGVpZ2h0LCBjbGllbnRIZWlnaHQgfSA9IHdyYXBwZXI7DQogICAgICBjb25zdCBzY3JvbGxQZXJjZW50ID0gY2xpZW50SGVpZ2h0IC8gc2Nyb2xsSGVpZ2h0Ow0KICAgICAgY29uc3Qgc2Nyb2xsYmFySGVpZ2h0ID0gTWF0aC5tYXgoMzAsIHNjcm9sbFBlcmNlbnQgKiBjbGllbnRIZWlnaHQpOw0KICAgICAgY29uc3Qgc2Nyb2xsYmFyVG9wID0gKHNjcm9sbFRvcCAvIHNjcm9sbEhlaWdodCkgKiBjbGllbnRIZWlnaHQ7DQoNCiAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zdHlsZS5zZXRQcm9wZXJ0eSgNCiAgICAgICAgIi0tc2Nyb2xsYmFyLWhlaWdodCIsDQogICAgICAgIGAke3Njcm9sbGJhckhlaWdodH1weGANCiAgICAgICk7DQogICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc3R5bGUuc2V0UHJvcGVydHkoDQogICAgICAgICItLXNjcm9sbGJhci10b3AiLA0KICAgICAgICBgJHtzY3JvbGxiYXJUb3B9cHhgDQogICAgICApOw0KICAgIH0sDQogICAgdXBkYXRlU2Nyb2xsYmFyMSgpIHsNCiAgICAgIGNvbnN0IHdyYXBwZXIgPSB0aGlzLiRyZWZzLnNjcm9sbFdyYXBwZXIxOw0KICAgICAgaWYgKCF3cmFwcGVyKSByZXR1cm47DQoNCiAgICAgIGNvbnN0IHsgc2Nyb2xsVG9wLCBzY3JvbGxIZWlnaHQsIGNsaWVudEhlaWdodCB9ID0gd3JhcHBlcjsNCiAgICAgIGNvbnN0IHNjcm9sbFBlcmNlbnQgPSBjbGllbnRIZWlnaHQgLyBzY3JvbGxIZWlnaHQ7DQogICAgICBjb25zdCBzY3JvbGxiYXJIZWlnaHQgPSBNYXRoLm1heCgzMCwgc2Nyb2xsUGVyY2VudCAqIGNsaWVudEhlaWdodCk7DQogICAgICBjb25zdCBzY3JvbGxiYXJUb3AgPSAoc2Nyb2xsVG9wIC8gc2Nyb2xsSGVpZ2h0KSAqIGNsaWVudEhlaWdodDsNCg0KICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlLnNldFByb3BlcnR5KA0KICAgICAgICAiLS1zY3JvbGxiYXItaGVpZ2h0IiwNCiAgICAgICAgYCR7c2Nyb2xsYmFySGVpZ2h0fXB4YA0KICAgICAgKTsNCiAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zdHlsZS5zZXRQcm9wZXJ0eSgNCiAgICAgICAgIi0tc2Nyb2xsYmFyLXRvcCIsDQogICAgICAgIGAke3Njcm9sbGJhclRvcH1weGANCiAgICAgICk7DQogICAgfSwNCg0KICAgIGFzeW5jIG9wZW5OZXdWaWV3KGl0ZW0pIHsNCiAgICAgIC8vIOS9v+eUqGJpZ1NjcmVlblRocmVl55u45ZCM55qEQVBJ5o6l5Y+jDQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBsYXJnZUhvdFF1ZXJ5QnlJZChpdGVtLmlkKTsNCiAgICAgICAgdGhpcy5kcmF3ZXJJbmZvID0gew0KICAgICAgICAgIGNuVGl0bGU6DQogICAgICAgICAgICBpdGVtLmNuVGl0bGUgfHwgaXRlbS50aXRsZSB8fCByZXMuZGF0YS50aXRsZSB8fCByZXMuZGF0YS5jblRpdGxlLA0KICAgICAgICAgIHRpdGxlOg0KICAgICAgICAgICAgaXRlbS50aXRsZSB8fCBpdGVtLmNuVGl0bGUgfHwgcmVzLmRhdGEudGl0bGUgfHwgcmVzLmRhdGEuY25UaXRsZSwNCiAgICAgICAgICBjbkNvbnRlbnQ6IHJlcy5kYXRhLmNvbnRlbnQgfHwgcmVzLmRhdGEuY25Db250ZW50LA0KICAgICAgICB9Ow0KDQogICAgICAgIC8vIOWkhOeQhuWGheWuueagvOW8jw0KICAgICAgICBsZXQgY29udGVudCA9IHRoaXMuZm9ybWF0dGluZ0pzb24odGhpcy5kcmF3ZXJJbmZvLmNuQ29udGVudCk7DQogICAgICAgIC8vIGlmIChjb250ZW50KSB7DQogICAgICAgIC8vICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvXG4vZywgIjxicj4iKTsNCiAgICAgICAgLy8gICBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKC9cJHtbXn1dK30vZywgIjxicj4iKTsNCiAgICAgICAgLy8gICBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKCJ8eGEwIiwgIiIpOw0KICAgICAgICAvLyAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoIm9wYWNpdHk6IDAiLCAiIik7DQogICAgICAgIC8vICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvPGltZ1xiW14+XSo+L2dpLCAiIik7DQogICAgICAgIC8vICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvIHN0eWxlPSJbXiJdKiIvZywgIiIpOw0KICAgICAgICAvLyB9DQogICAgICAgIHRoaXMuZHJhd2VySW5mby5jbkNvbnRlbnQgPSBjb250ZW50Ow0KDQogICAgICAgIHRoaXMuYXJ0aWNsZURpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgICB0aGlzLm9yaUZvbnRTaXplID0gdGhpcy5mb250U2l6ZTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuiOt+WPluaWh+eroOivpuaDheWksei0pToiLCBlcnJvcik7DQogICAgICAgIC8vIOWmguaenEFQSeiwg+eUqOWksei0pe+8jOaYvuekuuWfuuacrOS/oeaBrw0KICAgICAgICB0aGlzLmRyYXdlckluZm8gPSB7DQogICAgICAgICAgY25UaXRsZTogaXRlbS50aXRsZSB8fCBpdGVtLmNuVGl0bGUsDQogICAgICAgICAgdGl0bGU6IGl0ZW0udGl0bGUgfHwgaXRlbS5jblRpdGxlLA0KICAgICAgICAgIGNuQ29udGVudDogIuaaguaXoOivpue7huWGheWuuSIsDQogICAgICAgIH07DQogICAgICAgIHRoaXMuYXJ0aWNsZURpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgICB0aGlzLm9yaUZvbnRTaXplID0gdGhpcy5mb250U2l6ZTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIG9wZW5OZXdWaWV3MShpdGVtKSB7DQogICAgICAvLyDkvb/nlKhiaWdTY3JlZW5UaHJlZeebuOWQjOeahEFQSeaOpeWPow0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgZ2V0TGFyZ2VGVFQoaXRlbS5zbik7DQogICAgICAgIHRoaXMuZHJhd2VySW5mbyA9IHsNCiAgICAgICAgICBjblRpdGxlOg0KICAgICAgICAgICAgaXRlbS5jblRpdGxlIHx8IGl0ZW0udGl0bGUgfHwgcmVzLmRhdGEudGl0bGUgfHwgcmVzLmRhdGEuY25UaXRsZSwNCiAgICAgICAgICB0aXRsZToNCiAgICAgICAgICAgIGl0ZW0udGl0bGUgfHwgaXRlbS5jblRpdGxlIHx8IHJlcy5kYXRhLnRpdGxlIHx8IHJlcy5kYXRhLmNuVGl0bGUsDQogICAgICAgICAgY25Db250ZW50OiByZXMuZGF0YS5jb250ZW50IHx8IHJlcy5kYXRhLmNuQ29udGVudCwNCiAgICAgICAgfTsNCg0KICAgICAgICAvLyDlpITnkIblhoXlrrnmoLzlvI8NCiAgICAgICAgbGV0IGNvbnRlbnQgPSB0aGlzLmZvcm1hdHRpbmdKc29uKHRoaXMuZHJhd2VySW5mby5jbkNvbnRlbnQpOw0KICAgICAgICAvLyBpZiAoY29udGVudCkgew0KICAgICAgICAvLyAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoL1xuL2csICI8YnI+Iik7DQogICAgICAgIC8vICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvXCR7W159XSt9L2csICI8YnI+Iik7DQogICAgICAgIC8vICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgifHhhMCIsICIiKTsNCiAgICAgICAgLy8gICBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKCJvcGFjaXR5OiAwIiwgIiIpOw0KICAgICAgICAvLyAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoLzxpbWdcYltePl0qPi9naSwgIiIpOw0KICAgICAgICAvLyAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoLyBzdHlsZT0iW14iXSoiL2csICIiKTsNCiAgICAgICAgLy8gfQ0KICAgICAgICB0aGlzLmRyYXdlckluZm8uY25Db250ZW50ID0gY29udGVudDsNCg0KICAgICAgICB0aGlzLmFydGljbGVEaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICAgICAgdGhpcy5vcmlGb250U2l6ZSA9IHRoaXMuZm9udFNpemU7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLojrflj5bmlofnq6Dor6bmg4XlpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICAvLyDlpoLmnpxBUEnosIPnlKjlpLHotKXvvIzmmL7npLrln7rmnKzkv6Hmga8NCiAgICAgICAgdGhpcy5kcmF3ZXJJbmZvID0gew0KICAgICAgICAgIGNuVGl0bGU6IGl0ZW0udGl0bGUgfHwgaXRlbS5jblRpdGxlLA0KICAgICAgICAgIHRpdGxlOiBpdGVtLnRpdGxlIHx8IGl0ZW0uY25UaXRsZSwNCiAgICAgICAgICBjbkNvbnRlbnQ6ICLmmoLml6Dor6bnu4blhoXlrrkiLA0KICAgICAgICB9Ow0KICAgICAgICB0aGlzLmFydGljbGVEaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICAgICAgdGhpcy5vcmlGb250U2l6ZSA9IHRoaXMuZm9udFNpemU7DQogICAgICB9DQogICAgfSwNCiAgICBmb3JtYXR0aW5nSnNvbihjb250ZW50KSB7DQogICAgICBpZiAoY29udGVudCkgew0KICAgICAgICBpZiAoY29udGFpbnNIdG1sVGFncyhjb250ZW50KSkgew0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoLzxicj4vZywgIiIpOw0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoL1xuL2csICIiKTsNCiAgICAgICAgICBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKC9cXG4vZywgIiIpOw0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoL1xcXG4vZywgIiIpOw0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoInx4YTAiLCAiIik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgib3BhY2l0eTogMCIsICIiKTsNCiAgICAgICAgICAvLyBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKC9cJHtbXn1dK30vZywgIiIpOw0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoLzxpbWdcYltePl0qPi9naSwgIiIpOw0KICAgICAgICAgIC8vIOenu+mZpOWujOaVtOeahOagh+etvu+8iOWMheaLrOW8gOWni+agh+etvuOAgeWGheWuueWSjOe7k+adn+agh+etvu+8iQ0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoLzxmaWd1cmVcYltePl0qPltcc1xTXSo/PFwvZmlndXJlPi9naSwgIiIpOw0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoLzxpZnJhbWVcYltePl0qPltcc1xTXSo/PFwvaWZyYW1lPi9naSwgIiIpOw0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoLzx2aWRlb1xiW14+XSo+W1xzXFNdKj88XC92aWRlbz4vZ2ksICIiKTsNCiAgICAgICAgICAvLyDnp7vpmaToh6rpl63lkIjnmoRpZnJhbWXlkox2aWRlb+agh+etvg0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoLzxpZnJhbWVcYltePl0qXC8+L2dpLCAiIik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvPHZpZGVvXGJbXj5dKlwvPi9naSwgIiIpOw0KICAgICAgICAgIC8vIGNueOagh+etvu+8iOWBh+iuvuS5n+mcgOimgeWujOaVtOenu+mZpO+8iQ0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoLzxjbnhcYltePl0qPltcc1xTXSo/PFwvY254Pi9naSwgIiIpOw0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoLzxjbnhcYltePl0qXC8+L2dpLCAiIik7DQogICAgICAgICAgLy8g56e76Zmk5bim5qC35byP55qE5qCH562+77yM5L+d55WZ5YaF5a65DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgNCiAgICAgICAgICAgIC88KFx3KylbXj5dKnN0eWxlPSJbXiJdKiJbXj5dKj4oLio/KTxcL1wxPi9naSwNCiAgICAgICAgICAgICIkMiINCiAgICAgICAgICApOw0KICAgICAgICAgIC8vIOenu+mZpOS7u+S9leWFtuS7luagt+W8j+agh+etvg0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoDQogICAgICAgICAgICAvPChcdyspW14+XSpjbGFzcz0iW14iXSoiW14+XSo+KC4qPyk8XC9cMT4vZ2ksDQogICAgICAgICAgICAiJDIiDQogICAgICAgICAgKTsNCg0KICAgICAgICAgIGNvbnNvbGUubG9nKCLljIXlkKvnmoRIVE1M5qCH562+IiwgZXh0cmFjdEh0bWxUYWdzKGNvbnRlbnQpKTsNCiAgICAgICAgICBjb25zb2xlLmxvZygiSFRNTOaYr+WQpue7k+aehOato+ehriIsIGhhc1ZhbGlkSHRtbFN0cnVjdHVyZShjb250ZW50KSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvXG4vZywgIjxicj4iKTsNCiAgICAgICAgICBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKC9cXG4vZywgIjxicj4iKTsNCiAgICAgICAgICBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKC9cXFxuL2csICI8YnI+Iik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvXCR7W159XSt9L2csICI8YnI+Iik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgifHhhMCIsICIiKTsNCiAgICAgICAgICBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKCJvcGFjaXR5OiAwIiwgIiIpOw0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoLzxpbWdcYltePl0qPi9naSwgIiIpOw0KICAgICAgICAgIC8vIOenu+mZpOWujOaVtOeahOagh+etvu+8iOWMheaLrOW8gOWni+agh+etvuOAgeWGheWuueWSjOe7k+adn+agh+etvu+8iQ0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoLzxmaWd1cmVcYltePl0qPltcc1xTXSo/PFwvZmlndXJlPi9naSwgIiIpOw0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoLzxpZnJhbWVcYltePl0qPltcc1xTXSo/PFwvaWZyYW1lPi9naSwgIiIpOw0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoLzx2aWRlb1xiW14+XSo+W1xzXFNdKj88XC92aWRlbz4vZ2ksICIiKTsNCiAgICAgICAgICAvLyDnp7vpmaToh6rpl63lkIjnmoRpZnJhbWXlkox2aWRlb+agh+etvg0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoLzxpZnJhbWVcYltePl0qXC8+L2dpLCAiIik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvPHZpZGVvXGJbXj5dKlwvPi9naSwgIiIpOw0KICAgICAgICAgIC8vIGNueOagh+etvu+8iOWBh+iuvuS5n+mcgOimgeWujOaVtOenu+mZpO+8iQ0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoLzxjbnhcYltePl0qPltcc1xTXSo/PFwvY254Pi9naSwgIiIpOw0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoLzxjbnhcYltePl0qXC8+L2dpLCAiIik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgNCiAgICAgICAgICAgIC88KFx3KylbXj5dKnN0eWxlPSJbXiJdKiJbXj5dKj4oLio/KTxcL1wxPi9naSwNCiAgICAgICAgICAgICIkMiINCiAgICAgICAgICApOw0KICAgICAgICAgIC8vIOenu+mZpOS7u+S9leWFtuS7luagt+W8j+agh+etvg0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoDQogICAgICAgICAgICAvPChcdyspW14+XSpjbGFzcz0iW14iXSoiW14+XSo+KC4qPyk8XC9cMT4vZ2ksDQogICAgICAgICAgICAiJDIiDQogICAgICAgICAgKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGNvbnRlbnQ7DQogICAgfSwNCg0KICAgIGhhbmRsZUNsb3NlKCkgew0KICAgICAgdGhpcy5kcmF3ZXJJbmZvID0ge307DQogICAgICB0aGlzLmFydGljbGVEaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgfSwNCg0KICAgIGluY3JlYXNlRm9udFNpemUoKSB7DQogICAgICBpZiAodGhpcy5mb250U2l6ZSA8IDMwKSB7DQogICAgICAgIHRoaXMuZm9udFNpemUgKz0gMjsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgZGVjcmVhc2VGb250U2l6ZSgpIHsNCiAgICAgIGlmICh0aGlzLmZvbnRTaXplID4gMTYpIHsNCiAgICAgICAgdGhpcy5mb250U2l6ZSAtPSAyOw0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlTm9kZUNsaWNrKHR5cGUpIHsNCiAgICAgIC8vIOagueaNruW9k+WJjWFjdGl2ZVRhYuiOt+WPluWvueW6lOS6uueJqeeahOaVsOaNrg0KICAgICAgbGV0IGN1cnJlbnRDaGFyYWN0ZXIgPSAidHJ1bXAiOyAvLyDpu5jorqTnibnmnJfmma4NCiAgICAgIGlmICh0aGlzLmFjdGl2ZVRhYiA9PT0gIm1zayIpIHsNCiAgICAgICAgY3VycmVudENoYXJhY3RlciA9ICJtc2siOw0KICAgICAgfSBlbHNlIGlmICh0aGlzLmFjdGl2ZVRhYiA9PT0gIndzIikgew0KICAgICAgICBjdXJyZW50Q2hhcmFjdGVyID0gIndzIjsNCiAgICAgIH0NCiAgICAgIGxldCByYXdEYXRhID0gSlNPTi5wYXJzZSgNCiAgICAgICAgSlNPTi5zdHJpbmdpZnkodHJlZURhdGEyW2N1cnJlbnRDaGFyYWN0ZXJdW3R5cGVdIHx8IFtdKQ0KICAgICAgKTsNCiAgICAgIHRoaXMuY2hhcmFjdGVyVmlld0RhdGEgPSB0aGlzLmxpbWl0TGV2ZWwzQ2hpbGRyZW4ocmF3RGF0YSk7DQogICAgfSwNCiAgICBsaW1pdExldmVsM0NoaWxkcmVuKGRhdGEpIHsNCiAgICAgIGlmICghZGF0YSB8fCAhQXJyYXkuaXNBcnJheShkYXRhKSkgcmV0dXJuIGRhdGE7DQogICAgICByZXR1cm4gZGF0YS5tYXAoKGl0ZW0pID0+IHsNCiAgICAgICAgaWYgKA0KICAgICAgICAgIChpdGVtLnR5cGUgPT0gImxldmVsMi0xIiB8fA0KICAgICAgICAgICAgaXRlbS50eXBlID09ICJsZXZlbDItMiIgfHwNCiAgICAgICAgICAgIGl0ZW0udHlwZSA9PSAibGV2ZWwyLTMiKSAmJg0KICAgICAgICAgIEFycmF5LmlzQXJyYXkoaXRlbS5jaGlsZHJlbikNCiAgICAgICAgKSB7DQogICAgICAgICAgaXRlbS5jaGlsZHJlbiA9IGl0ZW0uY2hpbGRyZW4uc2xpY2UoMCwgMik7IC8vIOWPquS/neeVmeWJjeS4pOS4qg0KICAgICAgICB9DQoNCiAgICAgICAgaWYgKGl0ZW0uY2hpbGRyZW4pIHsNCiAgICAgICAgICBpdGVtLmNoaWxkcmVuID0gdGhpcy5saW1pdExldmVsM0NoaWxkcmVuKGl0ZW0uY2hpbGRyZW4pOw0KICAgICAgICB9DQoNCiAgICAgICAgcmV0dXJuIGl0ZW07DQogICAgICB9KTsNCiAgICB9LA0KICAgIGhhbmRsZUJ1dHRvbkNsaWNrKHR5cGUpIHsNCiAgICAgIGxldCBvYmogPSB7DQogICAgICAgIOiEkeacuuaOpeWPozogIjMiLA0KICAgICAgICDph4/lrZDkv6Hmga86ICI0IiwNCiAgICAgICAg5Lq65b2i5py65Zmo5Lq6OiAiNiIsDQogICAgICAgIOeUn+aIkOW8j+S6uuW3peaZuuiDvTogIjEiLA0KICAgICAgICDnlJ/nianliLbpgKA6ICI3IiwNCiAgICAgICAg5pyq5p2l5pi+56S6OiAiOCIsDQogICAgICAgIOacquadpee9kee7nDogIjkiLA0KICAgICAgICDmlrDlnovlgqjog706ICIxMCIsDQogICAgICAgIOWFtuS7ljogIjIsNSwxMSwxMiwxMywxNCwxNSwxNiwxNyIsDQogICAgICB9Ow0KICAgICAgdGhpcy5hY3RpdmVCdXR0b24gPSB0eXBlOw0KDQogICAgICAvLyDph43nva7ova7mkq3ml7bpl7QNCiAgICAgIHRoaXMuc3RhcnRTY3JvbGwyKCk7DQoNCiAgICAgIGtqZHRBcnRpY2xlTGlzdCh7DQogICAgICAgIGxhYmVsU246IG9ialt0eXBlXSwNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAvLyDlr7nmlbDmja7ov5vooYzljrvph43lpITnkIbvvIzln7rkuo5jblRpdGxl5Y676Zmk56m65qC85ZCO5Yik5patDQogICAgICAgIGNvbnN0IGRlZHVwbGljYXRlZERhdGEgPSB0aGlzLmRlZHVwbGljYXRlQXJ0aWNsZXMocmVzIHx8IFtdKTsNCiAgICAgICAgdGhpcy5yZW1lbmd3ZW56aGFuZ0xpc3QxID0gZGVkdXBsaWNhdGVkRGF0YTsNCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIGNvbnN0IHdyYXBwZXIgPSB0aGlzLiRyZWZzLnNjcm9sbFdyYXBwZXIxOw0KICAgICAgICAgIHdyYXBwZXIuc2Nyb2xsVG9wID0gMDsNCiAgICAgICAgfSk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIHF5a2pkdE9wZW5OZXdUYWIoKSB7DQogICAgICBsZXQgb2JqID0gew0KICAgICAgICDohJHmnLrmjqXlj6M6ICIvcWlhbnlhbmtlamlkb25ndGFpL25hb2ppamlla291P2lkPTEmZG9tYWluPTMiLA0KICAgICAgICDph4/lrZDkv6Hmga86ICIvcWlhbnlhbmtlamlkb25ndGFpL2xpYW5neml4aW54aT9pZD0xJmRvbWFpbj00IiwNCiAgICAgICAg5Lq65b2i5py65Zmo5Lq6OiAiL3FpYW55YW5rZWppZG9uZ3RhaS9yZW54aW5namlxaXJlbj9pZD0xJmRvbWFpbj02IiwNCiAgICAgICAg55Sf5oiQ5byP5Lq65bel5pm66IO9OiAiL3FpYW55YW5rZWppZG9uZ3RhaS9yZW5nb25nemhpbmVuZz9pZD0xJmRvbWFpbj0xIiwNCiAgICAgICAg55Sf54mp5Yi26YCgOiAiL3FpYW55YW5rZWppZG9uZ3RhaS9zaGVuZ3d1emhpemFvP2lkPTEmZG9tYWluPTciLA0KICAgICAgICDmnKrmnaXmmL7npLo6ICIvcWlhbnlhbmtlamlkb25ndGFpL3dlaWxhaXhpYW5zaGk/aWQ9MSZkb21haW49OCIsDQogICAgICAgIOacquadpee9kee7nDogIi9xaWFueWFua2VqaWRvbmd0YWkvd2VpbGFpd2FuZ2x1bz9pZD0xJmRvbWFpbj05IiwNCiAgICAgICAg5paw5Z6L5YKo6IO9OiAiL3FpYW55YW5rZWppZG9uZ3RhaS94aW54aW5nY2h1bmVuZz9pZD0xJmRvbWFpbj0xMCIsDQogICAgICAgIOWFtuS7ljogIi9xaWFueWFua2VqaWRvbmd0YWkvcWl0YT9pZD0xJmRvbWFpbj0yLDUsMTEsMTIsMTMsMTQsMTUsMTYsMTciLA0KICAgICAgfTsNCiAgICAgIHdpbmRvdy5vcGVuKG9ialt0aGlzLmFjdGl2ZUJ1dHRvbl0sICJfYmxhbmsiKTsNCiAgICB9LA0KICAgIC8vIOaWh+eroOWOu+mHjeaWueazle+8jOWfuuS6jmNuVGl0bGXljrvpmaTnqbrmoLzlkI7liKTmlq0NCiAgICBkZWR1cGxpY2F0ZUFydGljbGVzKGFydGljbGVzKSB7DQogICAgICBpZiAoIUFycmF5LmlzQXJyYXkoYXJ0aWNsZXMpKSB7DQogICAgICAgIHJldHVybiBbXTsNCiAgICAgIH0NCg0KICAgICAgY29uc3Qgc2VlbiA9IG5ldyBTZXQoKTsNCiAgICAgIGNvbnN0IHJlc3VsdCA9IFtdOw0KDQogICAgICBhcnRpY2xlcy5mb3JFYWNoKChhcnRpY2xlKSA9PiB7DQogICAgICAgIGlmIChhcnRpY2xlICYmIGFydGljbGUuY25UaXRsZSkgew0KICAgICAgICAgIC8vIOWOu+mZpGNuVGl0bGXkuK3nmoTmiYDmnInnqbrmoLwNCiAgICAgICAgICBjb25zdCBub3JtYWxpemVkVGl0bGUgPSBhcnRpY2xlLmNuVGl0bGUucmVwbGFjZSgvXHMrL2csICIiKTsNCg0KICAgICAgICAgIGlmICghc2Vlbi5oYXMobm9ybWFsaXplZFRpdGxlKSkgew0KICAgICAgICAgICAgc2Vlbi5hZGQobm9ybWFsaXplZFRpdGxlKTsNCiAgICAgICAgICAgIHJlc3VsdC5wdXNoKGFydGljbGUpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDlpoLmnpzmsqHmnIljblRpdGxl77yM5Lmf5L+d55WZ6L+Z5p2h6K6w5b2VDQogICAgICAgICAgcmVzdWx0LnB1c2goYXJ0aWNsZSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KDQogICAgICByZXR1cm4gcmVzdWx0Ow0KICAgIH0sDQogICAgcGFkV2l0aFplcm9zKG51bSwgdGFyZ2V0TGVuZ3RoKSB7DQogICAgICBjb25zdCBudW1TdHIgPSBudW0udG9TdHJpbmcoKTsNCiAgICAgIGNvbnN0IHBhZGRpbmcgPSAiMCIucmVwZWF0KHRhcmdldExlbmd0aCAtIG51bVN0ci5sZW5ndGgpOw0KICAgICAgcmV0dXJuIGAke3BhZGRpbmd9JHtudW1TdHJ9YC5yZXBsYWNlKC9cQig/PShcZHszfSkrKD8hXGQpKS9nLCAiLCIpOw0KICAgIH0sDQogICAgb3Blbk5ld1RhYih1cmwpIHsNCiAgICAgIHdpbmRvdy5vcGVuKHVybCwgIl9ibGFuayIpOw0KICAgIH0sDQogICAgaGFuZGxlTWFya21hcENsb3NlKCkgew0KICAgICAgdGhpcy5tYXJrbWFwQ29udGVudCA9ICIiOw0KICAgICAgdGhpcy5haUxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIHRoaXMubWFya21hcFZpc2libGUgPSBmYWxzZTsNCiAgICB9LA0KDQogICAgLy8g5pu05paw54Ot54K55o6o6I2Q5paH56ug5YiX6KGoDQogICAgYXN5bmMgdXBkYXRlSG90QXJ0aWNsZXNMaXN0KCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBsYXJnZUhvdExpc3QyKCk7DQogICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgY29uc3QgbmV3QXJ0aWNsZXMgPSByZXNwb25zZS5kYXRhOw0KICAgICAgICAgIC8vIOWvueavlOaVsOaNruaYr+WQpuS4gOiHtA0KICAgICAgICAgIGlmICh0aGlzLmlzQXJ0aWNsZURhdGFDaGFuZ2VkKG5ld0FydGljbGVzKSkgew0KICAgICAgICAgICAgdGhpcy5yZW1lbmd3ZW56aGFuZ0xpc3QgPSBuZXdBcnRpY2xlczsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi5pu05paw54Ot54K55o6o6I2Q5paH56ug5YiX6KGo5aSx6LSlOiIsIGVycm9yKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5qOA5p+l5paH56ug5pWw5o2u5piv5ZCm5Y+R55Sf5Y+Y5YyWDQogICAgaXNBcnRpY2xlRGF0YUNoYW5nZWQobmV3QXJ0aWNsZXMpIHsNCiAgICAgIC8vIOWmguaenOW9k+WJjeWIl+ihqOS4uuepuu+8jOebtOaOpei/lOWbnnRydWUNCiAgICAgIGlmICh0aGlzLnJlbWVuZ3dlbnpoYW5nTGlzdC5sZW5ndGggPT09IDApIHsNCiAgICAgICAgcmV0dXJuIG5ld0FydGljbGVzLmxlbmd0aCA+IDA7DQogICAgICB9DQoNCiAgICAgIC8vIOWmguaenOaVsOmHj+S4jeWQjO+8jOivtOaYjuacieWPmOWMlg0KICAgICAgaWYgKHRoaXMucmVtZW5nd2VuemhhbmdMaXN0Lmxlbmd0aCAhPT0gbmV3QXJ0aWNsZXMubGVuZ3RoKSB7DQogICAgICAgIHJldHVybiB0cnVlOw0KICAgICAgfQ0KDQogICAgICAvLyDlr7nmr5Tmr4/nr4fmlofnq6DnmoTlhbPplK7kv6Hmga8NCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbmV3QXJ0aWNsZXMubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgY29uc3QgbmV3QXJ0aWNsZSA9IG5ld0FydGljbGVzW2ldOw0KICAgICAgICBjb25zdCBvbGRBcnRpY2xlID0gdGhpcy5yZW1lbmd3ZW56aGFuZ0xpc3RbaV07DQoNCiAgICAgICAgLy8g5a+55q+U5paH56ugSUTjgIHmoIfpopjjgIHlj5HluIPml7bpl7TnrYnlhbPplK7lrZfmrrUNCiAgICAgICAgaWYgKA0KICAgICAgICAgIG5ld0FydGljbGUuaWQgIT09IG9sZEFydGljbGUuaWQgfHwNCiAgICAgICAgICBuZXdBcnRpY2xlLnRpdGxlICE9PSBvbGRBcnRpY2xlLnRpdGxlIHx8DQogICAgICAgICAgbmV3QXJ0aWNsZS5wdWJsaXNoVGltZSAhPT0gb2xkQXJ0aWNsZS5wdWJsaXNoVGltZSB8fA0KICAgICAgICAgIG5ld0FydGljbGUuc291cmNlTmFtZSAhPT0gb2xkQXJ0aWNsZS5zb3VyY2VOYW1lDQogICAgICAgICkgew0KICAgICAgICAgIHJldHVybiB0cnVlOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC8vIOaJgOacieaVsOaNrumDveS4gOiHtA0KICAgICAgcmV0dXJuIGZhbHNlOw0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbmioDmnK/poobln5/mjInpkq7ngrnlh7sNCiAgICBoYW5kbGVUZWNoQnV0dG9uQ2xpY2soc2NyZWVuU24sIGJ1dHRvbk5hbWUpIHsNCiAgICAgIGNvbnNvbGUubG9nKCLliIfmjaLmioDmnK/poobln586IiwgYnV0dG9uTmFtZSwgInNjcmVlblNuOiIsIHNjcmVlblNuKTsNCiAgICAgIHRoaXMuYWN0aXZlVGVjaEJ1dHRvbiA9IHNjcmVlblNuOw0KICAgICAgdGhpcy5jdXJyZW50VGVjaFNjcmVlblNuID0gc2NyZWVuU247DQogICAgICAvLyDlvLnlh7rmioDmnK/poobln5/ms6Hms6Hlm77lvLnnqpcNCiAgICAgIHRoaXMudGVjaEJ1YmJsZURpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgdGhpcy50ZWNoQnViYmxlRGlhbG9nVGl0bGUgPSBidXR0b25OYW1lOw0KICAgICAgdGhpcy50ZWNoQnViYmxlRGlhbG9nU2NyZWVuU24gPSBzY3JlZW5TbjsNCg0KICAgICAgLy8g6YCa55+l5a2Q57uE5Lu25pu05paw5pWw5o2uDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIC8vIOWPr+S7pemAmui/h3JlZuebtOaOpeiwg+eUqOWtkOe7hOS7tueahOaWueazleadpeWIt+aWsOaVsOaNrg0KICAgICAgICAvLyDmiJbogIXpgJrov4d3YXRjaOebkeWQrGN1cnJlbnRUZWNoU2NyZWVuU27nmoTlj5jljJbmnaXop6blj5HlrZDnu4Tku7bmm7TmlrANCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDlhbPpl63mioDmnK/poobln5/ms6Hms6Hlm77lvLnnqpcNCiAgICBoYW5kbGVUZWNoQnViYmxlRGlhbG9nQ2xvc2UoKSB7DQogICAgICB0aGlzLnRlY2hCdWJibGVEaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICB0aGlzLnRlY2hCdWJibGVEaWFsb2dUaXRsZSA9ICIiOw0KICAgICAgdGhpcy50ZWNoQnViYmxlRGlhbG9nU2NyZWVuU24gPSAiIjsNCiAgICB9LA0KDQogICAgLy8g5aSE55CG6YCa55+l5YWz6ZetDQogICAgaGFuZGxlTm90aWZpY2F0aW9uQ2xvc2UoKSB7DQogICAgICB0aGlzLiRlbWl0KCJub3RpZmljYXRpb24tY2xvc2UiKTsNCiAgICB9LA0KDQogICAgLy8g5aSE55CG5p+l55yL5Y2V56+H5paH56ugDQogICAgaGFuZGxlVmlld0FydGljbGUoYXJ0aWNsZSkgew0KICAgICAgdGhpcy4kZW1pdCgibm90aWZpY2F0aW9uLXZpZXctYXJ0aWNsZSIsIGFydGljbGUpOw0KICAgIH0sDQoNCiAgICAvLyDliIfmjaLmmbrlupPop4Lngrl0YWINCiAgICBzd2l0Y2hUYWIodGFiTmFtZSwgbWFya2Rvd25UeXBlKSB7DQogICAgICBpZiAobWFya2Rvd25UeXBlKSB7DQogICAgICAgIHRoaXMuYWN0aXZlVGFiID0gdGFiTmFtZTsNCiAgICAgICAgdGhpcy5kb21haW5NYXJrZG93biA9IG1hcmtPYmpbInR5cGUiICsgbWFya2Rvd25UeXBlXTsNCiAgICAgICAgdGhpcy5yZW5kZXJNYXJrbWFwKCk7DQogICAgICB9IGVsc2UgaWYgKHRhYk5hbWUgPT09ICJ0cnVtcCIgfHwgdGFiTmFtZSA9PT0gIm1zayIgfHwgdGFiTmFtZSA9PT0gIndzIikgew0KICAgICAgICAvLyDlpoLmnpzngrnlh7vnmoTmmK/lvZPliY3lt7Lmv4DmtLvnmoTkurrnial0YWLvvIzpnIDopoHph43mlrDop6blj5HmlbDmja7liqDovb0NCiAgICAgICAgaWYgKHRoaXMuYWN0aXZlVGFiID09PSB0YWJOYW1lKSB7DQogICAgICAgICAgLy8g5riF56m65pWw5o2uDQogICAgICAgICAgdGhpcy5jaGFyYWN0ZXJWaWV3RGF0YSA9IFtdOw0KICAgICAgICAgIC8vIOmAmui/h3JlZuebtOaOpeiwg+eUqHRydW1wVmlld1RyZWXnu4Tku7bnmoTmlrnms5XmnaXph43mlrDliJ3lp4vljJYNCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICBpZiAoDQogICAgICAgICAgICAgIHRoaXMuJHJlZnMuY2hhcmFjdGVyVmlld1RyZWUgJiYNCiAgICAgICAgICAgICAgdGhpcy4kcmVmcy5jaGFyYWN0ZXJWaWV3VHJlZS5hbGxUeXBlcy5sZW5ndGggPiAwDQogICAgICAgICAgICApIHsNCiAgICAgICAgICAgICAgdGhpcy4kcmVmcy5jaGFyYWN0ZXJWaWV3VHJlZS5oYW5kbGVOb2RlQ2xpY2soDQogICAgICAgICAgICAgICAgdGhpcy4kcmVmcy5jaGFyYWN0ZXJWaWV3VHJlZS5hbGxUeXBlc1swXQ0KICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOWIh+aNouWIsOS4jeWQjOeahOS6uueJqXRhYuaXtu+8jOiuvue9rmFjdGl2ZVRhYu+8jOiuqXdhdGNoZXLoh6rliqjlpITnkIYNCiAgICAgICAgICB0aGlzLmFjdGl2ZVRhYiA9IHRhYk5hbWU7DQogICAgICAgICAgdGhpcy5jaGFyYWN0ZXJWaWV3RGF0YSA9IFtdOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyByZW5kZXJNYXJrbWFwKCkgew0KICAgICAgaWYgKCF0aGlzLmRvbWFpbk1hcmtkb3duKSB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIHRyeSB7DQogICAgICAgIGF3YWl0IHRoaXMuJG5leHRUaWNrKCk7DQogICAgICAgIGNvbnN0IHN2ZyA9IHRoaXMuJHJlZnMubWFya21hcDsNCiAgICAgICAgaWYgKCFzdmcpIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIlNWRyBlbGVtZW50IG5vdCBmb3VuZCIpOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5riF56m65LmL5YmN55qE5YaF5a65DQogICAgICAgIHN2Zy5pbm5lckhUTUwgPSAiIjsNCg0KICAgICAgICAvLyDlpITnkIblhoXlrrnvvIznp7vpmaQgbWFya2Rvd24g5qCH6K6wDQogICAgICAgIGxldCBwcm9jZXNzZWRDb250ZW50ID0gdGhpcy5kb21haW5NYXJrZG93bg0KICAgICAgICAgIC5yZXBsYWNlKC9eYGBgbWFya2Rvd25ccyovaSwgIiIpIC8vIOenu+mZpOW8gOWktOeahCBgYGBtYXJrZG93bg0KICAgICAgICAgIC5yZXBsYWNlKC9ccypgYGBccyokLywgIiIpOyAvLyDnp7vpmaTnu5PlsL7nmoQgYGBgDQoNCiAgICAgICAgY29uc3QgdHJhbnNmb3JtZXIgPSBuZXcgVHJhbnNmb3JtZXIoKTsNCiAgICAgICAgY29uc3QgeyByb290IH0gPSB0cmFuc2Zvcm1lci50cmFuc2Zvcm0ocHJvY2Vzc2VkQ29udGVudCk7DQoNCiAgICAgICAgLy8g5Yib5bu65oCd57u05a+85Zu+DQogICAgICAgIGNvbnN0IG1tID0gTWFya21hcC5jcmVhdGUoDQogICAgICAgICAgc3ZnLA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGF1dG9GaXQ6IHRydWUsDQogICAgICAgICAgICBkdXJhdGlvbjogMCwNCiAgICAgICAgICAgIG5vZGVNaW5IZWlnaHQ6IDIwLA0KICAgICAgICAgICAgc3BhY2luZ1ZlcnRpY2FsOiAxMCwNCiAgICAgICAgICAgIHNwYWNpbmdIb3Jpem9udGFsOiAxMDAsDQogICAgICAgICAgICBwYWRkaW5nWDogMjAsDQogICAgICAgICAgICBjb2xvcjogKG5vZGUpID0+IHsNCiAgICAgICAgICAgICAgY29uc3QgY29sb3JzID0gew0KICAgICAgICAgICAgICAgIDA6ICIjMDA1MmZmIiwgLy8g5Lqu6JOd6ImyDQogICAgICAgICAgICAgICAgMTogIiMwMDk2MDAiLCAvLyDkuq7nu7/oibINCiAgICAgICAgICAgICAgICAyOiAiI2ZmNjYwMCIsIC8vIOS6ruapmeiJsg0KICAgICAgICAgICAgICAgIDM6ICIjODAwMGZmIiwgLy8g5Lqu57Sr6ImyDQogICAgICAgICAgICAgICAgNDogIiNmZjAwNjYiLCAvLyDkuq7nsonoibINCiAgICAgICAgICAgICAgfTsNCiAgICAgICAgICAgICAgcmV0dXJuIGNvbG9yc1tub2RlLmRlcHRoXSB8fCAiIzAwNTJmZiI7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgbm9kZUZvbnQ6IChub2RlKSA9PiB7DQogICAgICAgICAgICAgIGNvbnN0IGZvbnRzID0gew0KICAgICAgICAgICAgICAgIDA6ICdib2xkIDIwcHgvMS41IC1hcHBsZS1zeXN0ZW0sIEJsaW5rTWFjU3lzdGVtRm9udCwgIlNlZ29lIFVJIiwgUm9ib3RvJywNCiAgICAgICAgICAgICAgICAxOiAnNjAwIDE4cHgvMS41IC1hcHBsZS1zeXN0ZW0sIEJsaW5rTWFjU3lzdGVtRm9udCwgIlNlZ29lIFVJIiwgUm9ib3RvJywNCiAgICAgICAgICAgICAgICAyOiAnNTAwIDE2cHgvMS41IC1hcHBsZS1zeXN0ZW0sIEJsaW5rTWFjU3lzdGVtRm9udCwgIlNlZ29lIFVJIiwgUm9ib3RvJywNCiAgICAgICAgICAgICAgfTsNCiAgICAgICAgICAgICAgcmV0dXJuICgNCiAgICAgICAgICAgICAgICBmb250c1tub2RlLmRlcHRoXSB8fA0KICAgICAgICAgICAgICAgICc0MDAgMTRweC8xLjUgLWFwcGxlLXN5c3RlbSwgQmxpbmtNYWNTeXN0ZW1Gb250LCAiU2Vnb2UgVUkiLCBSb2JvdG8nDQogICAgICAgICAgICAgICk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgbWF4V2lkdGg6IDQwMCwNCiAgICAgICAgICAgIGluaXRpYWxFeHBhbmRMZXZlbDogLTEsDQogICAgICAgICAgICB6b29tOiB0cnVlLA0KICAgICAgICAgICAgcGFuOiB0cnVlLA0KICAgICAgICAgICAgbGlua1NoYXBlOiAiZGlhZ29uYWwiLA0KICAgICAgICAgICAgbGlua1dpZHRoOiAobm9kZSkgPT4gMi41IC0gbm9kZS5kZXB0aCAqIDAuNSwNCiAgICAgICAgICAgIGxpbmtDb2xvcjogKG5vZGUpID0+IHsNCiAgICAgICAgICAgICAgY29uc3QgY29sb3JzID0gew0KICAgICAgICAgICAgICAgIDA6ICJyZ2JhKDAsIDgyLCAyNTUsIDAuOCkiLCAvLyDkuq7ok53oibINCiAgICAgICAgICAgICAgICAxOiAicmdiYSgwLCAxNTAsIDAsIDAuOCkiLCAvLyDkuq7nu7/oibINCiAgICAgICAgICAgICAgICAyOiAicmdiYSgyNTUsIDEwMiwgMCwgMC44KSIsIC8vIOS6ruapmeiJsg0KICAgICAgICAgICAgICB9Ow0KICAgICAgICAgICAgICByZXR1cm4gY29sb3JzW25vZGUuZGVwdGhdIHx8ICJyZ2JhKDEyOCwgMCwgMjU1LCAwLjgpIjsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgfSwNCiAgICAgICAgICByb290DQogICAgICAgICk7DQoNCiAgICAgICAgLy8g5L+u5pS55Yid5aeL5YyW5Yqo55S76YOo5YiGDQogICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgIG1tLmZpdCgpOyAvLyDpgILlupTop4blm77lpKflsI8NCg0KICAgICAgICAgIC8vIOmHjeaWsOiuvue9ruaVsOaNruS7peinpuWPkemHjee7mA0KICAgICAgICAgIGNvbnN0IGZpdFJhdGlvID0gMC45NTsgLy8g55WZ5Ye65LiA5Lqb6L656LedDQogICAgICAgICAgY29uc3QgeyBtaW5YLCBtYXhYLCBtaW5ZLCBtYXhZIH0gPSBtbS5zdGF0ZTsNCiAgICAgICAgICBjb25zdCB3aWR0aCA9IG1heFggLSBtaW5YOw0KICAgICAgICAgIGNvbnN0IGhlaWdodCA9IG1heFkgLSBtaW5ZOw0KICAgICAgICAgIGNvbnN0IGNvbnRhaW5lcldpZHRoID0gc3ZnLmNsaWVudFdpZHRoOw0KICAgICAgICAgIGNvbnN0IGNvbnRhaW5lckhlaWdodCA9IHN2Zy5jbGllbnRIZWlnaHQ7DQoNCiAgICAgICAgICAvLyDorqHnrpflkIjpgILnmoTnvKnmlL7mr5TkvosNCiAgICAgICAgICBjb25zdCBzY2FsZSA9IE1hdGgubWluKA0KICAgICAgICAgICAgKGNvbnRhaW5lcldpZHRoIC8gd2lkdGgpICogZml0UmF0aW8sDQogICAgICAgICAgICAoY29udGFpbmVySGVpZ2h0IC8gaGVpZ2h0KSAqIGZpdFJhdGlvDQogICAgICAgICAgKTsNCg0KICAgICAgICAgIC8vIOabtOaWsOaVsOaNruS7peW6lOeUqOaWsOeahOe8qeaUvg0KICAgICAgICAgIG1tLnNldERhdGEocm9vdCwgew0KICAgICAgICAgICAgaW5pdGlhbFNjYWxlOiBzY2FsZSwNCiAgICAgICAgICAgIGluaXRpYWxQb3NpdGlvbjogWw0KICAgICAgICAgICAgICAoY29udGFpbmVyV2lkdGggLSB3aWR0aCAqIHNjYWxlKSAvIDIsDQogICAgICAgICAgICAgIChjb250YWluZXJIZWlnaHQgLSBoZWlnaHQgKiBzY2FsZSkgLyAyLA0KICAgICAgICAgICAgXSwNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSwgMTAwKTsNCg0KICAgICAgICAvLyDnm5HlkKznqpflj6PlpKflsI/lj5jljJYNCiAgICAgICAgY29uc3QgcmVzaXplSGFuZGxlciA9ICgpID0+IG1tLmZpdCgpOw0KICAgICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigicmVzaXplIiwgcmVzaXplSGFuZGxlcik7DQoNCiAgICAgICAgLy8g57uE5Lu26ZSA5q+B5pe25riF55CGDQogICAgICAgIHRoaXMuJG9uY2UoImhvb2s6YmVmb3JlRGVzdHJveSIsICgpID0+IHsNCiAgICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigicmVzaXplIiwgcmVzaXplSGFuZGxlcik7DQogICAgICAgIH0pOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigiTWFya21hcCByZW5kZXJpbmcgZXJyb3I6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLmgJ3nu7Tlr7zlm77muLLmn5PlpLHotKUiKTsNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5omT5byA5paw5rWq6IiG5oOF6YCaDQogICAgb3BlblNpbmEoKSB7DQogICAgICB3aW5kb3cub3Blbih0aGlzLnNpbmFVcmwsICJfYmxhbmsiKTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["tabOne.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8kBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "tabOne.vue", "sourceRoot": "src/views/bigScreenSanhao", "sourcesContent": ["<template>\r\n  <div style=\"height: 100%; display: flex\" class=\"two\">\r\n    <div class=\"left\">\r\n      <div class=\"bsContentBox\" style=\"width: 516px; height: 380px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">热点推荐</div>\r\n          <!-- <div class=\"bsContentTitleHelp\"></div> -->\r\n\r\n          <div\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"openNewTab('MonitorUse?id=1')\"\r\n            style=\"right: 80px\"\r\n          >\r\n            更多\r\n          </div>\r\n          <div class=\"bsContentTitleMore\" @click=\"openSina\">舆情通</div>\r\n        </div>\r\n        <div class=\"bsContentContent\">\r\n          <div class=\"remengwenzhang-box\" :style=\"remengwenzhangBoxStyle\">\r\n            <div\r\n              class=\"scroll-wrapper\"\r\n              ref=\"scrollWrapper\"\r\n              @mouseenter=\"handleMouseEnter\"\r\n              @mouseleave=\"handleMouseLeave\"\r\n              @scroll=\"updateScrollbar\"\r\n            >\r\n              <div class=\"scroll-content\" ref=\"scrollContent\">\r\n                <div\r\n                  class=\"remengwenzhang-list\"\r\n                  v-for=\"(item, index) in remengwenzhangList\"\r\n                  :key=\"index\"\r\n                  @click=\"openNewView(item)\"\r\n                >\r\n                  <div\r\n                    class=\"block\"\r\n                    :style=\"{\r\n                      background: item.isShow === '3' ? '#F48200' : '#1bdcff',\r\n                    }\"\r\n                  ></div>\r\n                  <div class=\"title\">{{ item.title }}</div>\r\n                  <div class=\"sourceName\">{{ item.sourceName }}</div>\r\n                  <div class=\"time\">\r\n                    {{ parseTime(item.publishTime, \"{y}-{m}-{d}\") }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"scroll-bar\"></div>\r\n          </div>\r\n\r\n          <!-- 文章通知组件 -->\r\n          <article-notification\r\n            ref=\"articleNotification\"\r\n            :articles=\"notificationArticles\"\r\n            :visible=\"showNotification\"\r\n            @close=\"handleNotificationClose\"\r\n            @view-article=\"handleViewArticle\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <div class=\"bsContentBox\" style=\"width: 516px; height: 290px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">政策风险</div>\r\n          <div class=\"bsContentTitleHelp\" @click=\"getPolicyRiskDetail\"></div>\r\n          <div\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"openNewTab('http://61.149.6.16:21001/bigScreen')\"\r\n          >\r\n            更多\r\n          </div>\r\n        </div>\r\n        <div class=\"bsContentContent\">\r\n          <usaMap\r\n            style=\"width: 516px; height: 247px\"\r\n            :external-data=\"usaMapData\"\r\n          ></usaMap>\r\n        </div>\r\n      </div>\r\n      <div class=\"bsContentBox\" style=\"width: 516px; height: 290px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">打压风险</div>\r\n          <div class=\"bsContentTitleHelp\" @click=\"getRiskDetail\"></div>\r\n          <div\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"openNewTab('http://61.149.6.16:21001/bigScreen')\"\r\n          >\r\n            更多\r\n          </div>\r\n        </div>\r\n        <div class=\"bsContentContent\">\r\n          <timeLine\r\n            :timelineEvents=\"suppressListData\"\r\n            @openEnterpriseInformation=\"openEnterpriseInformation\"\r\n            style=\"width: 516px; height: 247px\"\r\n          ></timeLine>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"center\">\r\n      <div class=\"center-top\">\r\n        <div class=\"top-content\">\r\n          <div class=\"bg1\"></div>\r\n          <div class=\"top-content-number\">\r\n            {{ padWithZeros(gatherTotal, 6) }}\r\n          </div>\r\n          <div class=\"top-content-name\">有效采集量</div>\r\n        </div>\r\n        <div class=\"top-content\">\r\n          <div class=\"bg1\"></div>\r\n          <div class=\"top-content-number\">\r\n            {{ padWithZeros(gatherDayNumber, 6) }}\r\n          </div>\r\n          <div class=\"top-content-name\">当日采集数量</div>\r\n        </div>\r\n      </div>\r\n      <div\r\n        class=\"bsContentBox1\"\r\n        @mouseenter=\"handleMouseEnter2\"\r\n        @mouseleave=\"handleMouseLeave2\"\r\n      >\r\n        <div class=\"bsContentTitle1\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\" style=\"display: flex\">\r\n            <div\r\n              @click=\"zhikuActive = 0\"\r\n              :style=\"{ fontWeight: zhikuActive === 0 ? '800' : '400' }\"\r\n              style=\"cursor: pointer\"\r\n            >\r\n              重点人物分析\r\n            </div>\r\n            <div style=\"margin: 0 4px\">/</div>\r\n            <div\r\n              @click=\"zhikuActive = 1\"\r\n              :style=\"{ fontWeight: zhikuActive === 1 ? '800' : '400' }\"\r\n              style=\"cursor: pointer\"\r\n            >\r\n              产业与技术专题分析\r\n            </div>\r\n          </div>\r\n          <!-- <div class=\"bsContentTitleHelp\" ></div> -->\r\n        </div>\r\n        <div\r\n          class=\"bsContentContent\"\r\n          style=\"display: flex; flex-direction: column; gap: 8px; padding: 8px\"\r\n        >\r\n          <!-- Tab 切换按钮 -->\r\n          <div class=\"tab-buttons\" v-if=\"zhikuActive === 0\">\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'trump' }\"\r\n              @click=\"switchTab('trump')\"\r\n            >\r\n              特朗普\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'msk' }\"\r\n              @click=\"switchTab('msk')\"\r\n            >\r\n              埃隆·里夫·马斯克\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'ws' }\"\r\n              @click=\"switchTab('ws')\"\r\n            >\r\n              詹姆斯·唐纳德·万斯\r\n            </div>\r\n          </div>\r\n          <div class=\"tab-buttons\" v-if=\"zhikuActive === 1\">\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'bdt' }\"\r\n              @click=\"switchTab('bdt', 7)\"\r\n            >\r\n              半导体领域\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'gdzb' }\"\r\n              @click=\"switchTab('gdzb', 8)\"\r\n            >\r\n              高端装备与材料\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'xnyqc' }\"\r\n              @click=\"switchTab('xnyqc', 9)\"\r\n            >\r\n              新能源汽车与电池\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'szhzx' }\"\r\n              @click=\"switchTab('szhzx', 10)\"\r\n            >\r\n              数字化转型与工业软件\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'lszz' }\"\r\n              @click=\"switchTab('lszz', 11)\"\r\n            >\r\n              绿色制造与新能源\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'swyy' }\"\r\n              @click=\"switchTab('swyy', 12)\"\r\n            >\r\n              生物医药与医疗器械\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 人物观点内容（特朗普/马斯克公用） -->\r\n          <div\r\n            v-show=\"\r\n              activeTab === 'trump' || activeTab === 'msk' || activeTab === 'ws'\r\n            \"\r\n            class=\"tab-content\"\r\n          >\r\n            <!-- 上方人物观点树状图 -->\r\n            <div class=\"trump-view-container\">\r\n              <trumpViewTree\r\n                ref=\"characterViewTree\"\r\n                @handleNodeClick=\"handleNodeClick\"\r\n                style=\"width: 100%; height: 300px\"\r\n                :move=\"isHovered2\"\r\n                :currentCharacter=\"activeTab\"\r\n              >\r\n              </trumpViewTree>\r\n            </div>\r\n            <div class=\"view-tree-container\">\r\n              <viewTree\r\n                :treeData=\"characterViewData\"\r\n                :title=\"'renwu'\"\r\n                :visible=\"\r\n                  activeTab === 'trump' ||\r\n                  activeTab === 'msk' ||\r\n                  activeTab === 'ws'\r\n                \"\r\n                style=\"width: 100%; height: 100%\"\r\n                @openNewView=\"openNewView1\"\r\n                @openbaarTreeEcharts=\"openbaarTreeEcharts()\"\r\n              ></viewTree>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 中国制造短板分析内容 -->\r\n          <div\r\n            v-show=\"\r\n              activeTab !== 'trump' && activeTab !== 'msk' && activeTab !== 'ws'\r\n            \"\r\n            class=\"tab-content\"\r\n          >\r\n            <svg ref=\"markmap\" class=\"markmap-svg\"></svg>\r\n          </div>\r\n\r\n          <div\r\n            style=\"\"\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"openbaarTreeEcharts()\"\r\n          >\r\n            更多\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"right\">\r\n      <div class=\"bsContentBox\" style=\"width: 516px; height: 380px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">前沿科技动态</div>\r\n          <div class=\"bsContentTitleMore\" @click=\"qykjdtOpenNewTab()\">更多</div>\r\n        </div>\r\n        <div\r\n          class=\"bsContentContent\"\r\n          @mouseleave=\"handleMouseLeave1\"\r\n          @scroll=\"updateScrollbar1\"\r\n        >\r\n          <div class=\"kejidongtai-box\">\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '脑机接口' }\"\r\n              @click=\"handleButtonClick('脑机接口')\"\r\n            >\r\n              脑机接口\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '量子信息' }\"\r\n              @click=\"handleButtonClick('量子信息')\"\r\n            >\r\n              量子信息\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '人形机器人' }\"\r\n              @click=\"handleButtonClick('人形机器人')\"\r\n            >\r\n              人形机器人\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '生成式人工智能' }\"\r\n              @click=\"handleButtonClick('生成式人工智能')\"\r\n            >\r\n              生成式人工智能\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '生物制造' }\"\r\n              @click=\"handleButtonClick('生物制造')\"\r\n            >\r\n              生物制造\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '未来显示' }\"\r\n              @click=\"handleButtonClick('未来显示')\"\r\n            >\r\n              未来显示\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '未来网络' }\"\r\n              @click=\"handleButtonClick('未来网络')\"\r\n            >\r\n              未来网络\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '新型储能' }\"\r\n              @click=\"handleButtonClick('新型储能')\"\r\n            >\r\n              新型储能\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '其他' }\"\r\n              @click=\"handleButtonClick('其他')\"\r\n            >\r\n              其他\r\n            </div>\r\n          </div>\r\n          <div class=\"remengwenzhang-box1\">\r\n            <div\r\n              class=\"scroll-wrapper\"\r\n              ref=\"scrollWrapper1\"\r\n              @mouseenter=\"handleMouseEnter1\"\r\n            >\r\n              <div class=\"scroll-content\" ref=\"scrollContent1\">\r\n                <div\r\n                  class=\"remengwenzhang-list\"\r\n                  v-for=\"(item, index) in remengwenzhangList1\"\r\n                  :key=\"index\"\r\n                  @click=\"openNewView1(item)\"\r\n                >\r\n                  <div class=\"block\"></div>\r\n                  <div class=\"title\">{{ item.cnTitle }}</div>\r\n                  <div class=\"sourceName\">{{ item.sourceName }}</div>\r\n                  <div class=\"time\">\r\n                    {{ parseTime(item.publishTime, \"{y}-{m}-{d}\") }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- <div class=\"scroll-bar\"></div> -->\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"bsContentBox2\" style=\"width: 516px; height: 600px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">国内外前沿热点技术</div>\r\n          <div\r\n            class=\"bsContentTitleHelp\"\r\n            @click=\"comparisonChartShowModal = true\"\r\n          ></div>\r\n          <div\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"\r\n              openNewTab(\r\n                'http://36.110.223.95:8080/analysis/#/infoQuery/queryManage'\r\n              )\r\n            \"\r\n          >\r\n            更多\r\n          </div>\r\n        </div>\r\n        <div class=\"kejidongtai-box\">\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '11' }\"\r\n            @click=\"handleTechButtonClick('11', '新能源')\"\r\n          >\r\n            新能源\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '12' }\"\r\n            @click=\"handleTechButtonClick('12', '新材料')\"\r\n          >\r\n            新材料\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '13' }\"\r\n            @click=\"handleTechButtonClick('13', '高端装备')\"\r\n          >\r\n            高端装备\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '14' }\"\r\n            @click=\"handleTechButtonClick('14', '新能源汽车')\"\r\n          >\r\n            新能源汽车\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '17' }\"\r\n            @click=\"handleTechButtonClick('17', '船舶与海洋工程装备')\"\r\n          >\r\n            船舶与海洋工程装备\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '16' }\"\r\n            @click=\"handleTechButtonClick('16', '民用航空')\"\r\n          >\r\n            民用航空\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '15' }\"\r\n            @click=\"handleTechButtonClick('15', '绿色环保')\"\r\n          >\r\n            绿色环保\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '18' }\"\r\n            @click=\"handleTechButtonClick('18', '新一代信息技术')\"\r\n          >\r\n            新一代信息技术\r\n          </div>\r\n        </div>\r\n        <!-- <div class=\"bsContentContent\" style=\"height: 188px\">\r\n          <technologyArticles\r\n            :sccenId=\"1\"\r\n            :screenSn=\"currentTechScreenSn\"\r\n            @openHotTechnology=\"openHotTechnology\"\r\n          ></technologyArticles>\r\n        </div> -->\r\n        <div class=\"bsContentContent\" style=\"padding-top: 0px; height: 450px\">\r\n          <graphEcharts\r\n            :sccenId=\"1\"\r\n            :screenSn=\"currentTechScreenSn\"\r\n            @openTechnologyDetails=\"openTechnologyDetails\"\r\n          ></graphEcharts>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <policyRisk\r\n      :visible=\"policyRiskShowModal\"\r\n      :list1=\"policyRiskList1\"\r\n      :list2=\"policyRiskList2\"\r\n      :total=\"policyRiskList1Total\"\r\n      :usa-map-data=\"usaMapData\"\r\n      title=\"美国相关提案\"\r\n      @update:visible=\"policyRiskShowModal = $event\"\r\n      @pagination=\"policyRiskPagination\"\r\n      @openArticleDetail=\"openArticleDetails('policyRisk-news', $event)\"\r\n    >\r\n    </policyRisk>\r\n    <suppressionOfRisks\r\n      :visible=\"suppressionOfRisksShowModal\"\r\n      :levelCount=\"riskBarChartData\"\r\n      :enterpriseList=\"riskEnterpriseList\"\r\n      :total=\"riskEnterpriseListTotal\"\r\n      title=\"打压风险\"\r\n      @update:visible=\"suppressionOfRisksShowModal = $event\"\r\n      @openEnterpriseInformation=\"openEnterpriseInformation\"\r\n      @pagination=\"riskEnterpriseListPagination\"\r\n    >\r\n    </suppressionOfRisks>\r\n    <technologyDetails\r\n      :visible=\"technologyDetailsShowModal\"\r\n      @update:visible=\"technologyDetailsShowModal = $event\"\r\n      @openArticleDetail=\"(e) => openArticleDetails('technology-article', e)\"\r\n      :title=\"technologyDetailsTitle\"\r\n      :item=\"technologyDetailsItem\"\r\n    ></technologyDetails>\r\n    <articleDetails\r\n      :visible=\"articleDetailsShowModal\"\r\n      :title=\"articleDetailsTitle\"\r\n      :content=\"articleDetailsContent\"\r\n      :contentEn=\"articleDetailsContentEn\"\r\n      :item=\"articleDetailsItem\"\r\n      @update:visible=\"articleDetailsShowModal = $event\"\r\n    >\r\n    </articleDetails>\r\n    <enterpriseInformation\r\n      :visible=\"enterpriseInformationShowModal\"\r\n      :title=\"enterpriseInformationTitle\"\r\n      :content=\"enterpriseInformationContent\"\r\n      :patentList=\"patentList\"\r\n      :softwareList=\"softwareList\"\r\n      :total1=\"patentTotal\"\r\n      :total2=\"softwareTotal\"\r\n      @update:visible=\"enterpriseInformationShowModal = $event\"\r\n      @pagination1=\"patentPagination\"\r\n      @pagination2=\"softwarePagination\"\r\n      @openArticleDetail=\"\r\n        (e) => openArticleDetails('enterpriseInformation-news', e)\r\n      \"\r\n    >\r\n    </enterpriseInformation>\r\n    <comparisonChart\r\n      :visible=\"comparisonChartShowModal\"\r\n      @update:visible=\"comparisonChartShowModal = $event\"\r\n      @openHotTechnology=\"openHotTechnology\"\r\n      title=\"前沿技术热点对比图详情\"\r\n    ></comparisonChart>\r\n    <hotTechnology\r\n      :visible=\"hotTechnologyShowModal\"\r\n      :title=\"hotTechnologytTitle\"\r\n      :id=\"hotTechnologytID\"\r\n      @update:visible=\"hotTechnologyShowModal = $event\"\r\n    ></hotTechnology>\r\n    <baarTreeEcharts\r\n      :visible=\"baarTreeEchartsShowModal\"\r\n      :type=\"baarTreeEchartsType\"\r\n      title=\"智库观点\"\r\n      @update:visible=\"baarTreeEchartsShowModal = $event\"\r\n      @openNewView=\"openNewView1\"\r\n    ></baarTreeEcharts>\r\n\r\n    <!-- 热点推荐文章详情弹窗 -->\r\n    <el-dialog\r\n      :title=\"drawerInfo.cnTitle || drawerInfo.title\"\r\n      :visible.sync=\"articleDialogVisible\"\r\n      width=\"80%\"\r\n      :before-close=\"handleClose\"\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\"\r\n    >\r\n      <div class=\"fz\">\r\n        <div class=\"text\">字号：</div>\r\n        <div class=\"btns\">\r\n          <div class=\"btn-minus\" @click=\"decreaseFontSize\">-</div>\r\n          <div class=\"font-size\">{{ fontSize }}px</div>\r\n          <div class=\"btn-plus\" @click=\"increaseFontSize\">+</div>\r\n        </div>\r\n      </div>\r\n      <div\r\n        class=\"dialog-art\"\r\n        :style=\"{ fontSize: fontSize + 'px' }\"\r\n        v-html=\"drawerInfo.cnContent\"\r\n      ></div>\r\n      <el-empty\r\n        description=\"当前文章暂无数据\"\r\n        v-if=\"!drawerInfo.cnContent\"\r\n      ></el-empty>\r\n    </el-dialog>\r\n    <markmap-dialog\r\n      :visible.sync=\"markmapVisible\"\r\n      :content=\"markmapContent\"\r\n      :title=\"markmapTitle\"\r\n      :loading=\"aiLoading\"\r\n      @close=\"handleMarkmapClose\"\r\n    />\r\n\r\n    <!-- 技术领域泡泡图弹窗 -->\r\n    <techBubbleDialog\r\n      :visible=\"techBubbleDialogVisible\"\r\n      :title=\"techBubbleDialogTitle\"\r\n      :screenSn=\"techBubbleDialogScreenSn\"\r\n      @update:visible=\"techBubbleDialogVisible = $event\"\r\n      @openTechnologyDetails=\"openTechnologyDetails\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport usaMap from \"./components/usaMap\";\r\nimport timeLine from \"./components/timeLine\";\r\nimport graphEcharts from \"./components/graphEcharts\";\r\nimport technologyArticles from \"./components/technologyArticles\";\r\nimport trumpViewTree from \"./components/trumpViewTree\";\r\nimport viewTree from \"./components/viewTree\";\r\nimport policyRisk from \"./secondLevel/policyRisk\";\r\nimport articleDetails from \"./secondLevel/articleDetails\";\r\nimport suppressionOfRisks from \"./secondLevel/suppressionOfRisks\";\r\nimport enterpriseInformation from \"./secondLevel/enterpriseInformation\";\r\nimport comparisonChart from \"./secondLevel/comparisonChart\";\r\nimport hotTechnology from \"./secondLevel/hotTechnology\";\r\nimport baarTreeEcharts from \"./components/baarTreeEcharts\";\r\nimport technologyDetails from \"./secondLevel/technologyDetails\";\r\nimport techBubbleDialog from \"./secondLevel/techBubbleDialog\";\r\nimport ArticleNotification from \"@/components/ArticleNotification\";\r\nimport {\r\n  technicalArticleDetail,\r\n  suppressData,\r\n  suppressLevelCount,\r\n  suppressEnterpriseList,\r\n  suppressPatentList,\r\n  suppressSoftwareList,\r\n  proposalsList,\r\n  proposalsToChinaData,\r\n  proposalsCount,\r\n  kjdtArticleList,\r\n  loginSINA,\r\n} from \"@/api/bigScreen/sanhao.js\";\r\nimport {\r\n  largeHotQueryById,\r\n  largeGatherQueryGatherData,\r\n  getLargeFTT,\r\n  largeHotList2,\r\n} from \"@/api/bigScreen/index1\";\r\nimport { markObj } from \"./data/zhiku.js\";\r\nimport { treeData2, markdownData } from \"./data/renwu.js\";\r\nimport MarkmapDialog from \"../bigScreenThree/components/MarkmapDialog.vue\";\r\nimport {\r\n  containsHtmlTags,\r\n  extractHtmlTags,\r\n  hasValidHtmlStructure,\r\n} from \"@/utils/htmlUtils\";\r\nimport { Transformer } from \"markmap-lib\";\r\nimport { Markmap } from \"markmap-view\";\r\n\r\nexport default {\r\n  name: \"TabOne\",\r\n  components: {\r\n    usaMap,\r\n    timeLine,\r\n    graphEcharts,\r\n    technologyArticles,\r\n    trumpViewTree,\r\n    viewTree,\r\n    policyRisk,\r\n    articleDetails,\r\n    suppressionOfRisks,\r\n    enterpriseInformation,\r\n    comparisonChart,\r\n    hotTechnology,\r\n    baarTreeEcharts,\r\n    technologyDetails,\r\n    techBubbleDialog,\r\n    MarkmapDialog,\r\n    ArticleNotification,\r\n  },\r\n  props: {\r\n    notificationArticles: {\r\n      type: Array,\r\n      default: () => [],\r\n    },\r\n    showNotification: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      policyRiskShowModal: false,\r\n      comparisonChartShowModal: false,\r\n      hotTechnologyShowModal: false,\r\n      hotTechnologytTitle: \"\",\r\n      hotTechnologytID: null,\r\n      baarTreeEchartsShowModal: false,\r\n      baarTreeEchartsType: null,\r\n      technologyDetailsShowModal: false,\r\n      technologyDetailsTitle: \"\",\r\n      technologyDetailsItem: null,\r\n      suppressionOfRisksShowModal: false,\r\n      enterpriseInformationShowModal: false,\r\n      enterpriseInformationTitle: \"\",\r\n      articleDetailsShowModal: false,\r\n      articleDetailsTitle: \"\",\r\n      articleDetailsContent: \"\",\r\n      articleDetailsContentEn: \"\",\r\n      suppressListData: [],\r\n      riskBarChartData: [],\r\n      riskEnterpriseList: [],\r\n      riskEnterpriseListTotal: 0,\r\n      enterpriseInformationContent: {},\r\n      patentList: [],\r\n      softwareList: [],\r\n      patentTotal: 0,\r\n      softwareTotal: 0,\r\n      policyRiskList1: [],\r\n      policyRiskList2: [],\r\n      policyRiskList1Total: 0,\r\n      // 美国地图数据\r\n      usaMapData: null,\r\n      articleDetailsItem: {},\r\n      // 热点推荐相关数据\r\n      remengwenzhangList: [],\r\n      scrollTimer: null,\r\n      scrollTimer1: null,\r\n      scrollTimer2: null,\r\n      isHovered: false,\r\n      isHovered1: false,\r\n      isHovered2: false,\r\n      scrollStep: 1,\r\n      drawerInfo: {},\r\n      articleDialogVisible: false,\r\n      fontSize: 16,\r\n      oriFontSize: 20,\r\n      // 人物观点数据\r\n      characterViewData: [],\r\n      // 智库观点数据\r\n      thinkTankViewData: [],\r\n      remengwenzhangList1: [],\r\n      activeButton: null,\r\n      qianyankejiList: [],\r\n      gatherTotal: 0,\r\n      gatherDayNumber: 0,\r\n      markmapVisible: false,\r\n      markmapContent: \"\",\r\n      markmapTitle: \"智库观点\",\r\n      aiLoading: false,\r\n      frontLoginParams: {\r\n        username: \"guanliyuan\",\r\n        password: \"123456\",\r\n      },\r\n      frontToken: \"\",\r\n      // 技术领域相关\r\n      activeTechButton: \"11\", // 默认选中新能源\r\n      currentTechScreenSn: \"11\", // 当前技术领域的screenSn\r\n      // 技术领域泡泡图弹窗相关\r\n      techBubbleDialogVisible: false,\r\n      techBubbleDialogTitle: \"\",\r\n      techBubbleDialogScreenSn: \"\",\r\n      // 智库观点tab切换\r\n      activeTab: \"trump\", // 默认显示特朗普tab\r\n      domainMarkdown: \"\",\r\n      sinaUrl: \"\",\r\n      zhikuActive: 0,\r\n    };\r\n  },\r\n  computed: {\r\n    // 动态计算热门文章列表框的样式\r\n    remengwenzhangBoxStyle() {\r\n      const notificationHeight = 110; // 通知组件的高度\r\n\r\n      if (this.showNotification) {\r\n        return {\r\n          height: `calc(100% - ${notificationHeight}px)`,\r\n        };\r\n      } else {\r\n        return {\r\n          height: `100%`,\r\n        };\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    // 调用登录新浪接口\r\n    loginSINA()\r\n      .then((res) => {\r\n        console.log(\"新浪登录成功\");\r\n        this.sinaUrl = res;\r\n      })\r\n      .catch((error) => {\r\n        console.error(\"新浪登录失败:\", error);\r\n      });\r\n\r\n    this.getSuppressData();\r\n    this.initHotList();\r\n    this.initHotList1();\r\n    this.updateScrollbar();\r\n    this.updateScrollbar1();\r\n    this.fetchUsaMapData();\r\n    largeGatherQueryGatherData({}).then((res) => {\r\n      this.gatherTotal = res.data.gatherTotal;\r\n      this.gatherDayNumber = res.data.gatherDayNumber;\r\n    });\r\n  },\r\n  beforeDestroy() {\r\n    this.clearScrollTimer();\r\n    this.clearScrollTimer1();\r\n    this.clearScrollTimer2();\r\n    this.handleMarkmapClose();\r\n  },\r\n  methods: {\r\n    // 获取美国地图数据\r\n    async fetchUsaMapData() {\r\n      try {\r\n        const response = await proposalsCount({\r\n          projectSn: \"1\",\r\n          screenSn: \"1\",\r\n          columnSn: \"1\",\r\n        });\r\n        this.usaMapData = response;\r\n      } catch (error) {\r\n        console.error(\"获取美国地图数据失败:\", error);\r\n      }\r\n    },\r\n\r\n    openArticleDetails(type, item) {\r\n      this.articleDetailsItem = item;\r\n      switch (type) {\r\n        case \"technology-article\":\r\n          technicalArticleDetail({ id: item.id }).then((res) => {\r\n            this.articleDetailsTitle = item.title;\r\n            this.articleDetailsContent = res.data.content;\r\n            this.articleDetailsContentEn = res.data.enContent;\r\n            this.articleDetailsShowModal = true;\r\n          });\r\n          break;\r\n        case \"enterpriseInformation-news\":\r\n          this.openNewView(item);\r\n          break;\r\n        case \"policyRisk-news\":\r\n          this.openNewView(item);\r\n          break;\r\n      }\r\n    },\r\n    openEnterpriseInformation(item) {\r\n      suppressPatentList({\r\n        suppressSn: item.suppressSn,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      }).then((res) => {\r\n        this.patentList = res.rows;\r\n        this.patentTotal = res.total;\r\n      });\r\n      suppressSoftwareList({\r\n        suppressSn: item.suppressSn,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      }).then((res) => {\r\n        this.softwareList = res.rows;\r\n        this.softwareTotal = res.total;\r\n      });\r\n      this.enterpriseInformationContent = { ...item };\r\n      this.enterpriseInformationTitle = item.enterpriseName;\r\n      this.enterpriseInformationShowModal = true;\r\n    },\r\n\r\n    patentPagination(suppressSn, queryParams) {\r\n      suppressPatentList({\r\n        suppressSn: suppressSn,\r\n        ...queryParams,\r\n      }).then((res) => {\r\n        this.patentList = res.rows;\r\n      });\r\n    },\r\n\r\n    softwarePagination(suppressSn, queryParams) {\r\n      suppressSoftwareList({\r\n        suppressSn: suppressSn,\r\n        ...queryParams,\r\n      }).then((res) => {\r\n        this.softwareList = res.rows;\r\n      });\r\n    },\r\n\r\n    getSuppressData() {\r\n      suppressData({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n      }).then((res) => {\r\n        let data = [];\r\n        Object.keys(res.data).forEach((key) => {\r\n          data.push({\r\n            date: key,\r\n            description:\r\n              res.data[key].length <= 3\r\n                ? res.data[key]\r\n                : res.data[key].slice(\r\n                    res.data[key].length - 3,\r\n                    res.data[key].length\r\n                  ),\r\n          });\r\n        });\r\n        this.suppressListData = data.reverse();\r\n      });\r\n    },\r\n\r\n    getRiskDetail() {\r\n      suppressEnterpriseList({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      }).then((res) => {\r\n        this.riskEnterpriseList = res.rows.map((item, index) => ({\r\n          ...item,\r\n          type: (index % 3) + 1,\r\n        }));\r\n        this.riskEnterpriseListTotal = res.total;\r\n      });\r\n      suppressLevelCount({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n      }).then((res) => {\r\n        // 将对象格式转换为数组格式\r\n        const data = Object.keys(res.data).map((year) => ({\r\n          product: year,\r\n          严重: res.data[year].严重,\r\n          一般: res.data[year].一般,\r\n          较轻: res.data[year].较轻,\r\n        }));\r\n        this.riskBarChartData = data;\r\n        this.suppressionOfRisksShowModal = true;\r\n      });\r\n    },\r\n\r\n    riskEnterpriseListPagination(queryParams) {\r\n      suppressEnterpriseList({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n        ...queryParams,\r\n      }).then((res) => {\r\n        this.riskEnterpriseList = res.rows.map((item, index) => ({\r\n          ...item,\r\n          type: (index % 3) + 1,\r\n        }));\r\n      });\r\n    },\r\n\r\n    getPolicyRiskDetail() {\r\n      proposalsList({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      }).then((res) => {\r\n        this.policyRiskList1 = res.rows;\r\n        this.policyRiskList1Total = res.total;\r\n      });\r\n      proposalsToChinaData({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n      }).then((res) => {\r\n        this.policyRiskList2 = res.data;\r\n        this.policyRiskShowModal = true;\r\n      });\r\n    },\r\n\r\n    policyRiskPagination(queryParams) {\r\n      proposalsList({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n        ...queryParams,\r\n      }).then((res) => {\r\n        this.policyRiskList1 = res.rows;\r\n      });\r\n    },\r\n\r\n    openHotTechnology(data) {\r\n      this.hotTechnologyShowModal = true;\r\n      this.hotTechnologytTitle = data.title;\r\n      this.hotTechnologytID = data.reportSn;\r\n    },\r\n    openbaarTreeEcharts(data) {\r\n      // this.baarTreeEchartsType = parseInt(this.$refs.trumpViewTree.nodeType.replace(/\\D+/g, ''), 10)\r\n      // this.baarTreeEchartsShowModal = true;\r\n\r\n      // 根据当前选中的tab来决定使用哪个markObj\r\n      if (this.activeTab === \"trump\") {\r\n        // 使用导入的markdownData\r\n        this.markmapContent =\r\n          markdownData.trump[this.$refs.characterViewTree.nodeType];\r\n        this.markmapTitle = \"特朗普\";\r\n      } else if (this.activeTab === \"msk\") {\r\n        // 马斯克的markdownData\r\n        this.markmapContent =\r\n          markdownData.msk[this.$refs.characterViewTree.nodeType];\r\n        this.markmapTitle = \"埃隆·里夫·马斯克\";\r\n      } else if (this.activeTab === \"ws\") {\r\n        // 万斯的markdownData\r\n        this.markmapContent =\r\n          markdownData.ws[this.$refs.characterViewTree.nodeType];\r\n        this.markmapTitle = \"詹姆斯·唐纳德·万斯\";\r\n      } else {\r\n        this.markmapContent = this.domainMarkdown;\r\n        switch (this.activeTab) {\r\n          case \"bdt\":\r\n            this.markmapTitle = \"半导体领域\";\r\n            break;\r\n          case \"gdzb\":\r\n            this.markmapTitle = \"高端装备与材料\";\r\n            break;\r\n          case \"xnyqc\":\r\n            this.markmapTitle = \"新能源汽车与电池\";\r\n            break;\r\n          case \"szhzx\":\r\n            this.markmapTitle = \"数字化转型与工业软件\";\r\n            break;\r\n          case \"lszz\":\r\n            this.markmapTitle = \"绿色制造与新能源\";\r\n            break;\r\n          case \"swyy\":\r\n            this.markmapTitle = \"生物医药与医疗器械\";\r\n            break;\r\n        }\r\n      }\r\n      this.aiLoading = false;\r\n      this.markmapVisible = true;\r\n    },\r\n\r\n    openTechnologyDetails(data) {\r\n      this.technologyDetailsShowModal = true;\r\n      this.technologyDetailsTitle = data.name;\r\n      this.technologyDetailsItem = data.data;\r\n    },\r\n\r\n    // 热点推荐相关方法\r\n    initHotList() {\r\n      // 使用bigScreenThree相同的API接口\r\n      largeHotList2()\r\n        .then((res) => {\r\n          this.remengwenzhangList = res.data || [];\r\n          this.$nextTick(() => {\r\n            this.startScroll();\r\n          });\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"获取热点推荐数据失败:\", error);\r\n          // 如果API调用失败，使用空数组\r\n          this.remengwenzhangList = [];\r\n        });\r\n    },\r\n    initHotList1() {\r\n      this.handleButtonClick(\"脑机接口\");\r\n      this.$nextTick(() => {\r\n        this.startScroll2();\r\n      });\r\n    },\r\n\r\n    startScroll() {\r\n      this.clearScrollTimer();\r\n      const wrapper = this.$refs.scrollWrapper;\r\n      const content = this.$refs.scrollContent;\r\n\r\n      if (!wrapper || !content) return;\r\n\r\n      this.scrollTimer = setInterval(() => {\r\n        if (this.isHovered) return;\r\n\r\n        if (wrapper.scrollTop >= content.scrollHeight - wrapper.clientHeight) {\r\n          wrapper.scrollTop = 0;\r\n        } else {\r\n          wrapper.scrollTop += this.scrollStep;\r\n        }\r\n        this.updateScrollbar();\r\n      }, 40);\r\n    },\r\n\r\n    clearScrollTimer() {\r\n      if (this.scrollTimer) {\r\n        clearInterval(this.scrollTimer);\r\n        this.scrollTimer = null;\r\n      }\r\n    },\r\n\r\n    handleMouseEnter() {\r\n      this.isHovered = true;\r\n    },\r\n\r\n    handleMouseLeave() {\r\n      this.isHovered = false;\r\n      this.startScroll();\r\n    },\r\n    startScroll1() {\r\n      this.clearScrollTimer1();\r\n      const wrapper = this.$refs.scrollWrapper1;\r\n      const content = this.$refs.scrollContent1;\r\n\r\n      if (!wrapper || !content) return;\r\n\r\n      this.scrollTimer1 = setInterval(() => {\r\n        if (this.isHovered1) return;\r\n\r\n        if (wrapper.scrollTop >= content.scrollHeight - wrapper.clientHeight) {\r\n          wrapper.scrollTop = 0;\r\n        } else {\r\n          wrapper.scrollTop += this.scrollStep;\r\n        }\r\n        this.updateScrollbar1();\r\n      }, 20);\r\n    },\r\n    startScroll2() {\r\n      this.clearScrollTimer2();\r\n      this.scrollTimer2 = setInterval(() => {\r\n        if (this.isHovered1) return;\r\n\r\n        // 定义所有tab标签的顺序\r\n        const tabOrder = [\r\n          \"脑机接口\",\r\n          \"量子信息\",\r\n          \"人形机器人\",\r\n          \"生成式人工智能\",\r\n          \"生物制造\",\r\n          \"未来显示\",\r\n          \"未来网络\",\r\n          \"新型储能\",\r\n          \"其他\",\r\n        ];\r\n\r\n        // 找到当前活跃标签的索引\r\n        const currentIndex = tabOrder.indexOf(this.activeButton);\r\n        // 计算下一个标签的索引，如果到最后一个则回到第一个\r\n        const nextIndex = (currentIndex + 1) % tabOrder.length;\r\n        // 切换到下一个标签\r\n        this.handleButtonClick(tabOrder[nextIndex]);\r\n      }, 8000);\r\n    },\r\n    clearScrollTimer1() {\r\n      if (this.scrollTimer1) {\r\n        clearInterval(this.scrollTimer1);\r\n        this.scrollTimer1 = null;\r\n      }\r\n    },\r\n    clearScrollTimer2() {\r\n      if (this.scrollTimer2) {\r\n        clearInterval(this.scrollTimer2);\r\n        this.scrollTimer2 = null;\r\n      }\r\n    },\r\n    handleMouseEnter1() {\r\n      this.isHovered1 = true;\r\n    },\r\n\r\n    handleMouseLeave1() {\r\n      this.isHovered1 = false;\r\n      // this.startScroll1();\r\n      // this.startScroll2();\r\n    },\r\n    handleMouseEnter2() {\r\n      this.isHovered2 = true;\r\n    },\r\n\r\n    handleMouseLeave2() {\r\n      this.isHovered2 = false;\r\n    },\r\n    updateScrollbar() {\r\n      const wrapper = this.$refs.scrollWrapper;\r\n      if (!wrapper) return;\r\n\r\n      const { scrollTop, scrollHeight, clientHeight } = wrapper;\r\n      const scrollPercent = clientHeight / scrollHeight;\r\n      const scrollbarHeight = Math.max(30, scrollPercent * clientHeight);\r\n      const scrollbarTop = (scrollTop / scrollHeight) * clientHeight;\r\n\r\n      document.documentElement.style.setProperty(\r\n        \"--scrollbar-height\",\r\n        `${scrollbarHeight}px`\r\n      );\r\n      document.documentElement.style.setProperty(\r\n        \"--scrollbar-top\",\r\n        `${scrollbarTop}px`\r\n      );\r\n    },\r\n    updateScrollbar1() {\r\n      const wrapper = this.$refs.scrollWrapper1;\r\n      if (!wrapper) return;\r\n\r\n      const { scrollTop, scrollHeight, clientHeight } = wrapper;\r\n      const scrollPercent = clientHeight / scrollHeight;\r\n      const scrollbarHeight = Math.max(30, scrollPercent * clientHeight);\r\n      const scrollbarTop = (scrollTop / scrollHeight) * clientHeight;\r\n\r\n      document.documentElement.style.setProperty(\r\n        \"--scrollbar-height\",\r\n        `${scrollbarHeight}px`\r\n      );\r\n      document.documentElement.style.setProperty(\r\n        \"--scrollbar-top\",\r\n        `${scrollbarTop}px`\r\n      );\r\n    },\r\n\r\n    async openNewView(item) {\r\n      // 使用bigScreenThree相同的API接口\r\n      try {\r\n        const res = await largeHotQueryById(item.id);\r\n        this.drawerInfo = {\r\n          cnTitle:\r\n            item.cnTitle || item.title || res.data.title || res.data.cnTitle,\r\n          title:\r\n            item.title || item.cnTitle || res.data.title || res.data.cnTitle,\r\n          cnContent: res.data.content || res.data.cnContent,\r\n        };\r\n\r\n        // 处理内容格式\r\n        let content = this.formattingJson(this.drawerInfo.cnContent);\r\n        // if (content) {\r\n        //   content = content.replace(/\\n/g, \"<br>\");\r\n        //   content = content.replace(/\\${[^}]+}/g, \"<br>\");\r\n        //   content = content.replace(\"|xa0\", \"\");\r\n        //   content = content.replace(\"opacity: 0\", \"\");\r\n        //   content = content.replace(/<img\\b[^>]*>/gi, \"\");\r\n        //   content = content.replace(/ style=\"[^\"]*\"/g, \"\");\r\n        // }\r\n        this.drawerInfo.cnContent = content;\r\n\r\n        this.articleDialogVisible = true;\r\n        this.oriFontSize = this.fontSize;\r\n      } catch (error) {\r\n        console.error(\"获取文章详情失败:\", error);\r\n        // 如果API调用失败，显示基本信息\r\n        this.drawerInfo = {\r\n          cnTitle: item.title || item.cnTitle,\r\n          title: item.title || item.cnTitle,\r\n          cnContent: \"暂无详细内容\",\r\n        };\r\n        this.articleDialogVisible = true;\r\n        this.oriFontSize = this.fontSize;\r\n      }\r\n    },\r\n    async openNewView1(item) {\r\n      // 使用bigScreenThree相同的API接口\r\n      try {\r\n        const res = await getLargeFTT(item.sn);\r\n        this.drawerInfo = {\r\n          cnTitle:\r\n            item.cnTitle || item.title || res.data.title || res.data.cnTitle,\r\n          title:\r\n            item.title || item.cnTitle || res.data.title || res.data.cnTitle,\r\n          cnContent: res.data.content || res.data.cnContent,\r\n        };\r\n\r\n        // 处理内容格式\r\n        let content = this.formattingJson(this.drawerInfo.cnContent);\r\n        // if (content) {\r\n        //   content = content.replace(/\\n/g, \"<br>\");\r\n        //   content = content.replace(/\\${[^}]+}/g, \"<br>\");\r\n        //   content = content.replace(\"|xa0\", \"\");\r\n        //   content = content.replace(\"opacity: 0\", \"\");\r\n        //   content = content.replace(/<img\\b[^>]*>/gi, \"\");\r\n        //   content = content.replace(/ style=\"[^\"]*\"/g, \"\");\r\n        // }\r\n        this.drawerInfo.cnContent = content;\r\n\r\n        this.articleDialogVisible = true;\r\n        this.oriFontSize = this.fontSize;\r\n      } catch (error) {\r\n        console.error(\"获取文章详情失败:\", error);\r\n        // 如果API调用失败，显示基本信息\r\n        this.drawerInfo = {\r\n          cnTitle: item.title || item.cnTitle,\r\n          title: item.title || item.cnTitle,\r\n          cnContent: \"暂无详细内容\",\r\n        };\r\n        this.articleDialogVisible = true;\r\n        this.oriFontSize = this.fontSize;\r\n      }\r\n    },\r\n    formattingJson(content) {\r\n      if (content) {\r\n        if (containsHtmlTags(content)) {\r\n          content = content.replace(/<br>/g, \"\");\r\n          content = content.replace(/\\n/g, \"\");\r\n          content = content.replace(/\\\\n/g, \"\");\r\n          content = content.replace(/\\\\\\n/g, \"\");\r\n          content = content.replace(\"|xa0\", \"\");\r\n          content = content.replace(\"opacity: 0\", \"\");\r\n          // content = content.replace(/\\${[^}]+}/g, \"\");\r\n          content = content.replace(/<img\\b[^>]*>/gi, \"\");\r\n          // 移除完整的标签（包括开始标签、内容和结束标签）\r\n          content = content.replace(/<figure\\b[^>]*>[\\s\\S]*?<\\/figure>/gi, \"\");\r\n          content = content.replace(/<iframe\\b[^>]*>[\\s\\S]*?<\\/iframe>/gi, \"\");\r\n          content = content.replace(/<video\\b[^>]*>[\\s\\S]*?<\\/video>/gi, \"\");\r\n          // 移除自闭合的iframe和video标签\r\n          content = content.replace(/<iframe\\b[^>]*\\/>/gi, \"\");\r\n          content = content.replace(/<video\\b[^>]*\\/>/gi, \"\");\r\n          // cnx标签（假设也需要完整移除）\r\n          content = content.replace(/<cnx\\b[^>]*>[\\s\\S]*?<\\/cnx>/gi, \"\");\r\n          content = content.replace(/<cnx\\b[^>]*\\/>/gi, \"\");\r\n          // 移除带样式的标签，保留内容\r\n          content = content.replace(\r\n            /<(\\w+)[^>]*style=\"[^\"]*\"[^>]*>(.*?)<\\/\\1>/gi,\r\n            \"$2\"\r\n          );\r\n          // 移除任何其他样式标签\r\n          content = content.replace(\r\n            /<(\\w+)[^>]*class=\"[^\"]*\"[^>]*>(.*?)<\\/\\1>/gi,\r\n            \"$2\"\r\n          );\r\n\r\n          console.log(\"包含的HTML标签\", extractHtmlTags(content));\r\n          console.log(\"HTML是否结构正确\", hasValidHtmlStructure(content));\r\n        } else {\r\n          content = content.replace(/\\n/g, \"<br>\");\r\n          content = content.replace(/\\\\n/g, \"<br>\");\r\n          content = content.replace(/\\\\\\n/g, \"<br>\");\r\n          content = content.replace(/\\${[^}]+}/g, \"<br>\");\r\n          content = content.replace(\"|xa0\", \"\");\r\n          content = content.replace(\"opacity: 0\", \"\");\r\n          content = content.replace(/<img\\b[^>]*>/gi, \"\");\r\n          // 移除完整的标签（包括开始标签、内容和结束标签）\r\n          content = content.replace(/<figure\\b[^>]*>[\\s\\S]*?<\\/figure>/gi, \"\");\r\n          content = content.replace(/<iframe\\b[^>]*>[\\s\\S]*?<\\/iframe>/gi, \"\");\r\n          content = content.replace(/<video\\b[^>]*>[\\s\\S]*?<\\/video>/gi, \"\");\r\n          // 移除自闭合的iframe和video标签\r\n          content = content.replace(/<iframe\\b[^>]*\\/>/gi, \"\");\r\n          content = content.replace(/<video\\b[^>]*\\/>/gi, \"\");\r\n          // cnx标签（假设也需要完整移除）\r\n          content = content.replace(/<cnx\\b[^>]*>[\\s\\S]*?<\\/cnx>/gi, \"\");\r\n          content = content.replace(/<cnx\\b[^>]*\\/>/gi, \"\");\r\n          content = content.replace(\r\n            /<(\\w+)[^>]*style=\"[^\"]*\"[^>]*>(.*?)<\\/\\1>/gi,\r\n            \"$2\"\r\n          );\r\n          // 移除任何其他样式标签\r\n          content = content.replace(\r\n            /<(\\w+)[^>]*class=\"[^\"]*\"[^>]*>(.*?)<\\/\\1>/gi,\r\n            \"$2\"\r\n          );\r\n        }\r\n      }\r\n      return content;\r\n    },\r\n\r\n    handleClose() {\r\n      this.drawerInfo = {};\r\n      this.articleDialogVisible = false;\r\n    },\r\n\r\n    increaseFontSize() {\r\n      if (this.fontSize < 30) {\r\n        this.fontSize += 2;\r\n      }\r\n    },\r\n\r\n    decreaseFontSize() {\r\n      if (this.fontSize > 16) {\r\n        this.fontSize -= 2;\r\n      }\r\n    },\r\n    handleNodeClick(type) {\r\n      // 根据当前activeTab获取对应人物的数据\r\n      let currentCharacter = \"trump\"; // 默认特朗普\r\n      if (this.activeTab === \"msk\") {\r\n        currentCharacter = \"msk\";\r\n      } else if (this.activeTab === \"ws\") {\r\n        currentCharacter = \"ws\";\r\n      }\r\n      let rawData = JSON.parse(\r\n        JSON.stringify(treeData2[currentCharacter][type] || [])\r\n      );\r\n      this.characterViewData = this.limitLevel3Children(rawData);\r\n    },\r\n    limitLevel3Children(data) {\r\n      if (!data || !Array.isArray(data)) return data;\r\n      return data.map((item) => {\r\n        if (\r\n          (item.type == \"level2-1\" ||\r\n            item.type == \"level2-2\" ||\r\n            item.type == \"level2-3\") &&\r\n          Array.isArray(item.children)\r\n        ) {\r\n          item.children = item.children.slice(0, 2); // 只保留前两个\r\n        }\r\n\r\n        if (item.children) {\r\n          item.children = this.limitLevel3Children(item.children);\r\n        }\r\n\r\n        return item;\r\n      });\r\n    },\r\n    handleButtonClick(type) {\r\n      let obj = {\r\n        脑机接口: \"3\",\r\n        量子信息: \"4\",\r\n        人形机器人: \"6\",\r\n        生成式人工智能: \"1\",\r\n        生物制造: \"7\",\r\n        未来显示: \"8\",\r\n        未来网络: \"9\",\r\n        新型储能: \"10\",\r\n        其他: \"2,5,11,12,13,14,15,16,17\",\r\n      };\r\n      this.activeButton = type;\r\n\r\n      // 重置轮播时间\r\n      this.startScroll2();\r\n\r\n      kjdtArticleList({\r\n        labelSn: obj[type],\r\n      }).then((res) => {\r\n        // 对数据进行去重处理，基于cnTitle去除空格后判断\r\n        const deduplicatedData = this.deduplicateArticles(res || []);\r\n        this.remengwenzhangList1 = deduplicatedData;\r\n        this.$nextTick(() => {\r\n          const wrapper = this.$refs.scrollWrapper1;\r\n          wrapper.scrollTop = 0;\r\n        });\r\n      });\r\n    },\r\n    qykjdtOpenNewTab() {\r\n      let obj = {\r\n        脑机接口: \"/qianyankejidongtai/naojijiekou?id=1&domain=3\",\r\n        量子信息: \"/qianyankejidongtai/liangzixinxi?id=1&domain=4\",\r\n        人形机器人: \"/qianyankejidongtai/renxingjiqiren?id=1&domain=6\",\r\n        生成式人工智能: \"/qianyankejidongtai/rengongzhineng?id=1&domain=1\",\r\n        生物制造: \"/qianyankejidongtai/shengwuzhizao?id=1&domain=7\",\r\n        未来显示: \"/qianyankejidongtai/weilaixianshi?id=1&domain=8\",\r\n        未来网络: \"/qianyankejidongtai/weilaiwangluo?id=1&domain=9\",\r\n        新型储能: \"/qianyankejidongtai/xinxingchuneng?id=1&domain=10\",\r\n        其他: \"/qianyankejidongtai/qita?id=1&domain=2,5,11,12,13,14,15,16,17\",\r\n      };\r\n      window.open(obj[this.activeButton], \"_blank\");\r\n    },\r\n    // 文章去重方法，基于cnTitle去除空格后判断\r\n    deduplicateArticles(articles) {\r\n      if (!Array.isArray(articles)) {\r\n        return [];\r\n      }\r\n\r\n      const seen = new Set();\r\n      const result = [];\r\n\r\n      articles.forEach((article) => {\r\n        if (article && article.cnTitle) {\r\n          // 去除cnTitle中的所有空格\r\n          const normalizedTitle = article.cnTitle.replace(/\\s+/g, \"\");\r\n\r\n          if (!seen.has(normalizedTitle)) {\r\n            seen.add(normalizedTitle);\r\n            result.push(article);\r\n          }\r\n        } else {\r\n          // 如果没有cnTitle，也保留这条记录\r\n          result.push(article);\r\n        }\r\n      });\r\n\r\n      return result;\r\n    },\r\n    padWithZeros(num, targetLength) {\r\n      const numStr = num.toString();\r\n      const padding = \"0\".repeat(targetLength - numStr.length);\r\n      return `${padding}${numStr}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\r\n    },\r\n    openNewTab(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n    handleMarkmapClose() {\r\n      this.markmapContent = \"\";\r\n      this.aiLoading = false;\r\n      this.markmapVisible = false;\r\n    },\r\n\r\n    // 更新热点推荐文章列表\r\n    async updateHotArticlesList() {\r\n      try {\r\n        const response = await largeHotList2();\r\n        if (response && response.data) {\r\n          const newArticles = response.data;\r\n          // 对比数据是否一致\r\n          if (this.isArticleDataChanged(newArticles)) {\r\n            this.remengwenzhangList = newArticles;\r\n          } else {\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"更新热点推荐文章列表失败:\", error);\r\n      }\r\n    },\r\n\r\n    // 检查文章数据是否发生变化\r\n    isArticleDataChanged(newArticles) {\r\n      // 如果当前列表为空，直接返回true\r\n      if (this.remengwenzhangList.length === 0) {\r\n        return newArticles.length > 0;\r\n      }\r\n\r\n      // 如果数量不同，说明有变化\r\n      if (this.remengwenzhangList.length !== newArticles.length) {\r\n        return true;\r\n      }\r\n\r\n      // 对比每篇文章的关键信息\r\n      for (let i = 0; i < newArticles.length; i++) {\r\n        const newArticle = newArticles[i];\r\n        const oldArticle = this.remengwenzhangList[i];\r\n\r\n        // 对比文章ID、标题、发布时间等关键字段\r\n        if (\r\n          newArticle.id !== oldArticle.id ||\r\n          newArticle.title !== oldArticle.title ||\r\n          newArticle.publishTime !== oldArticle.publishTime ||\r\n          newArticle.sourceName !== oldArticle.sourceName\r\n        ) {\r\n          return true;\r\n        }\r\n      }\r\n\r\n      // 所有数据都一致\r\n      return false;\r\n    },\r\n\r\n    // 处理技术领域按钮点击\r\n    handleTechButtonClick(screenSn, buttonName) {\r\n      console.log(\"切换技术领域:\", buttonName, \"screenSn:\", screenSn);\r\n      this.activeTechButton = screenSn;\r\n      this.currentTechScreenSn = screenSn;\r\n      // 弹出技术领域泡泡图弹窗\r\n      this.techBubbleDialogVisible = true;\r\n      this.techBubbleDialogTitle = buttonName;\r\n      this.techBubbleDialogScreenSn = screenSn;\r\n\r\n      // 通知子组件更新数据\r\n      this.$nextTick(() => {\r\n        // 可以通过ref直接调用子组件的方法来刷新数据\r\n        // 或者通过watch监听currentTechScreenSn的变化来触发子组件更新\r\n      });\r\n    },\r\n\r\n    // 关闭技术领域泡泡图弹窗\r\n    handleTechBubbleDialogClose() {\r\n      this.techBubbleDialogVisible = false;\r\n      this.techBubbleDialogTitle = \"\";\r\n      this.techBubbleDialogScreenSn = \"\";\r\n    },\r\n\r\n    // 处理通知关闭\r\n    handleNotificationClose() {\r\n      this.$emit(\"notification-close\");\r\n    },\r\n\r\n    // 处理查看单篇文章\r\n    handleViewArticle(article) {\r\n      this.$emit(\"notification-view-article\", article);\r\n    },\r\n\r\n    // 切换智库观点tab\r\n    switchTab(tabName, markdownType) {\r\n      if (markdownType) {\r\n        this.activeTab = tabName;\r\n        this.domainMarkdown = markObj[\"type\" + markdownType];\r\n        this.renderMarkmap();\r\n      } else if (tabName === \"trump\" || tabName === \"msk\" || tabName === \"ws\") {\r\n        // 如果点击的是当前已激活的人物tab，需要重新触发数据加载\r\n        if (this.activeTab === tabName) {\r\n          // 清空数据\r\n          this.characterViewData = [];\r\n          // 通过ref直接调用trumpViewTree组件的方法来重新初始化\r\n          this.$nextTick(() => {\r\n            if (\r\n              this.$refs.characterViewTree &&\r\n              this.$refs.characterViewTree.allTypes.length > 0\r\n            ) {\r\n              this.$refs.characterViewTree.handleNodeClick(\r\n                this.$refs.characterViewTree.allTypes[0]\r\n              );\r\n            }\r\n          });\r\n        } else {\r\n          // 切换到不同的人物tab时，设置activeTab，让watcher自动处理\r\n          this.activeTab = tabName;\r\n          this.characterViewData = [];\r\n        }\r\n      }\r\n    },\r\n    async renderMarkmap() {\r\n      if (!this.domainMarkdown) {\r\n        this.loading = false;\r\n        return;\r\n      }\r\n\r\n      try {\r\n        await this.$nextTick();\r\n        const svg = this.$refs.markmap;\r\n        if (!svg) {\r\n          throw new Error(\"SVG element not found\");\r\n        }\r\n\r\n        // 清空之前的内容\r\n        svg.innerHTML = \"\";\r\n\r\n        // 处理内容，移除 markdown 标记\r\n        let processedContent = this.domainMarkdown\r\n          .replace(/^```markdown\\s*/i, \"\") // 移除开头的 ```markdown\r\n          .replace(/\\s*```\\s*$/, \"\"); // 移除结尾的 ```\r\n\r\n        const transformer = new Transformer();\r\n        const { root } = transformer.transform(processedContent);\r\n\r\n        // 创建思维导图\r\n        const mm = Markmap.create(\r\n          svg,\r\n          {\r\n            autoFit: true,\r\n            duration: 0,\r\n            nodeMinHeight: 20,\r\n            spacingVertical: 10,\r\n            spacingHorizontal: 100,\r\n            paddingX: 20,\r\n            color: (node) => {\r\n              const colors = {\r\n                0: \"#0052ff\", // 亮蓝色\r\n                1: \"#009600\", // 亮绿色\r\n                2: \"#ff6600\", // 亮橙色\r\n                3: \"#8000ff\", // 亮紫色\r\n                4: \"#ff0066\", // 亮粉色\r\n              };\r\n              return colors[node.depth] || \"#0052ff\";\r\n            },\r\n            nodeFont: (node) => {\r\n              const fonts = {\r\n                0: 'bold 20px/1.5 -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto',\r\n                1: '600 18px/1.5 -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto',\r\n                2: '500 16px/1.5 -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto',\r\n              };\r\n              return (\r\n                fonts[node.depth] ||\r\n                '400 14px/1.5 -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto'\r\n              );\r\n            },\r\n            maxWidth: 400,\r\n            initialExpandLevel: -1,\r\n            zoom: true,\r\n            pan: true,\r\n            linkShape: \"diagonal\",\r\n            linkWidth: (node) => 2.5 - node.depth * 0.5,\r\n            linkColor: (node) => {\r\n              const colors = {\r\n                0: \"rgba(0, 82, 255, 0.8)\", // 亮蓝色\r\n                1: \"rgba(0, 150, 0, 0.8)\", // 亮绿色\r\n                2: \"rgba(255, 102, 0, 0.8)\", // 亮橙色\r\n              };\r\n              return colors[node.depth] || \"rgba(128, 0, 255, 0.8)\";\r\n            },\r\n          },\r\n          root\r\n        );\r\n\r\n        // 修改初始化动画部分\r\n        setTimeout(() => {\r\n          mm.fit(); // 适应视图大小\r\n\r\n          // 重新设置数据以触发重绘\r\n          const fitRatio = 0.95; // 留出一些边距\r\n          const { minX, maxX, minY, maxY } = mm.state;\r\n          const width = maxX - minX;\r\n          const height = maxY - minY;\r\n          const containerWidth = svg.clientWidth;\r\n          const containerHeight = svg.clientHeight;\r\n\r\n          // 计算合适的缩放比例\r\n          const scale = Math.min(\r\n            (containerWidth / width) * fitRatio,\r\n            (containerHeight / height) * fitRatio\r\n          );\r\n\r\n          // 更新数据以应用新的缩放\r\n          mm.setData(root, {\r\n            initialScale: scale,\r\n            initialPosition: [\r\n              (containerWidth - width * scale) / 2,\r\n              (containerHeight - height * scale) / 2,\r\n            ],\r\n          });\r\n        }, 100);\r\n\r\n        // 监听窗口大小变化\r\n        const resizeHandler = () => mm.fit();\r\n        window.addEventListener(\"resize\", resizeHandler);\r\n\r\n        // 组件销毁时清理\r\n        this.$once(\"hook:beforeDestroy\", () => {\r\n          window.removeEventListener(\"resize\", resizeHandler);\r\n        });\r\n      } catch (error) {\r\n        console.error(\"Markmap rendering error:\", error);\r\n        this.$message.error(\"思维导图渲染失败\");\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    // 打开新浪舆情通\r\n    openSina() {\r\n      window.open(this.sinaUrl, \"_blank\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.two {\r\n  height: 100%;\r\n  width: 100%;\r\n  padding-bottom: 10px;\r\n\r\n  .left {\r\n    width: 520px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .center {\r\n    margin: 0 11px;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n\r\n    .center-top {\r\n      height: 150px;\r\n      display: flex;\r\n      justify-content: space-around;\r\n      align-items: center;\r\n\r\n      .top-content {\r\n        position: relative;\r\n        width: 315px;\r\n        height: 98px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/centerBg1.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bg1 {\r\n        position: absolute;\r\n        top: 17px;\r\n        left: 43px;\r\n        width: 60px;\r\n        height: 60px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/centerBg2.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .top-content-number {\r\n        font-size: 24px;\r\n        color: #00e5ff;\r\n        position: absolute;\r\n        left: 138px;\r\n        top: 44px;\r\n      }\r\n\r\n      .top-content-name {\r\n        font-size: 18px;\r\n        color: #ffffff;\r\n        position: absolute;\r\n        left: 138px;\r\n        top: 22px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .right {\r\n    width: 520px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n\r\n    .bsContentBox2 {\r\n      background-image: url(\"../../assets/bigScreenSanhao/contentBg1.png\");\r\n\r\n      .bsContentContent {\r\n        height: calc((100% - 43px) / 2);\r\n      }\r\n\r\n      .kejidongtai-box {\r\n        margin-top: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  .bsContentBox,\r\n  .bsContentBox2 {\r\n    background-image: url(\"../../assets/bigScreenSanhao/contentBg.png\");\r\n    background-size: 100% 100%;\r\n\r\n    .bsContentTitle {\r\n      height: 43px;\r\n      display: flex;\r\n      align-items: center;\r\n      padding-left: 10px;\r\n      position: relative;\r\n\r\n      .bsContentTitleIcon {\r\n        width: 22px;\r\n        height: 22px;\r\n        margin-right: 10px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/titleLogo.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bsContentTitleName {\r\n        height: 43px;\r\n        line-height: 43px;\r\n        font-weight: 800;\r\n        font-size: 20px;\r\n        color: #00abf4;\r\n      }\r\n\r\n      .bsContentTitleHelp {\r\n        cursor: pointer;\r\n        width: 21px;\r\n        height: 21px;\r\n        margin-left: 10px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/titleHelp.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bsContentTitleMore {\r\n        position: absolute;\r\n        right: 10px;\r\n        cursor: pointer;\r\n        width: 70px;\r\n        height: 43px;\r\n        line-height: 60px;\r\n        font-size: 17px;\r\n        color: #00c8ff;\r\n\r\n        &:after {\r\n          content: \"\";\r\n          display: inline-block;\r\n          position: absolute;\r\n          top: 22px;\r\n          width: 18px;\r\n          height: 18px;\r\n          margin-left: 5px;\r\n          background-image: url(\"../../assets/bigScreenSanhao/jiantou.png\");\r\n          background-size: 100% 100%;\r\n        }\r\n      }\r\n    }\r\n\r\n    .bsContentContent {\r\n      height: calc(100% - 43px);\r\n    }\r\n  }\r\n\r\n  .bsContentBox1 {\r\n    flex: 1;\r\n\r\n    .bsContentTitle1 {\r\n      height: 43px;\r\n      display: flex;\r\n      align-items: center;\r\n      padding-left: 10px;\r\n      background-image: url(\"../../assets/bigScreenSanhao/title1.png\");\r\n      background-size: 100% 100%;\r\n      position: relative;\r\n\r\n      .bsContentTitleIcon {\r\n        width: 22px;\r\n        height: 22px;\r\n        margin-right: 10px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/titleLogo.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bsContentTitleName {\r\n        height: 43px;\r\n        line-height: 43px;\r\n        font-weight: 800;\r\n        font-size: 20px;\r\n        color: #00abf4;\r\n\r\n        span {\r\n          font-weight: normal;\r\n          cursor: pointer;\r\n          color: rgba(0, 171, 244, 0.5);\r\n        }\r\n\r\n        .titleColor {\r\n          font-weight: 800;\r\n          color: #ffffff;\r\n        }\r\n      }\r\n\r\n      .bsContentTitleHelp {\r\n        cursor: pointer;\r\n        width: 21px;\r\n        height: 21px;\r\n        margin-left: 10px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/titleHelp.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bsContentTitleMore {\r\n        position: absolute;\r\n        right: 10px;\r\n        cursor: pointer;\r\n        width: 70px;\r\n        height: 43px;\r\n        line-height: 60px;\r\n        font-size: 17px;\r\n        color: #00c8ff;\r\n\r\n        &:after {\r\n          content: \"\";\r\n          display: inline-block;\r\n          position: absolute;\r\n          top: 22px;\r\n          width: 18px;\r\n          height: 18px;\r\n          margin-left: 5px;\r\n          background-image: url(\"../../assets/bigScreenSanhao/jiantou.png\");\r\n          background-size: 100% 100%;\r\n        }\r\n      }\r\n    }\r\n\r\n    .bsContentContent {\r\n      height: calc(100% - 43px);\r\n      display: flex;\r\n      flex-direction: column;\r\n      position: relative;\r\n\r\n      .trump-view-container {\r\n        height: 300px;\r\n      }\r\n\r\n      .view-tree-container {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        & > div {\r\n          flex: 1;\r\n        }\r\n      }\r\n\r\n      .bsContentTitleMore {\r\n        position: absolute;\r\n        bottom: 0;\r\n        right: 0;\r\n        z-index: 99;\r\n        cursor: pointer;\r\n        width: 70px;\r\n        height: 43px;\r\n        line-height: 60px;\r\n        font-size: 17px;\r\n        color: #00c8ff;\r\n\r\n        &:after {\r\n          content: \"\";\r\n          display: inline-block;\r\n          position: absolute;\r\n          top: 22px;\r\n          width: 18px;\r\n          height: 18px;\r\n          margin-left: 5px;\r\n          background-image: url(\"../../assets/bigScreenSanhao/jiantou.png\");\r\n          background-size: 100% 100%;\r\n        }\r\n      }\r\n\r\n      // Tab 按钮样式\r\n      .tab-buttons {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        gap: 10px;\r\n\r\n        .tab-button {\r\n          padding: 8px 8px;\r\n          background: rgba(0, 171, 244, 0.2);\r\n          border: 1px solid rgba(0, 171, 244, 0.5);\r\n          border-radius: 4px;\r\n          color: rgba(0, 171, 244, 0.8);\r\n          cursor: pointer;\r\n          font-size: 14px;\r\n          transition: all 0.3s ease;\r\n\r\n          &:hover {\r\n            background: rgba(0, 171, 244, 0.3);\r\n            color: #00abf4;\r\n          }\r\n\r\n          &.active {\r\n            background: rgba(0, 171, 244, 0.5);\r\n            color: #ffffff;\r\n            border-color: #00abf4;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Tab 内容样式\r\n      .tab-content {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        .trump-view-container {\r\n          height: 300px;\r\n        }\r\n\r\n        .view-tree-container {\r\n          flex: 1;\r\n          display: flex;\r\n          flex-direction: column;\r\n\r\n          & > div {\r\n            flex: 1;\r\n          }\r\n        }\r\n\r\n        .markmap-svg {\r\n          width: 100%;\r\n          height: 100%;\r\n          display: block;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 热点推荐滚动列表样式\r\n  .remengwenzhang-box {\r\n    width: 100%;\r\n    height: 100%;\r\n    padding: 20px;\r\n    border: 1px solid rgba(16, 216, 255, 0.4);\r\n    background: rgba(0, 0, 0, 0.15);\r\n    box-shadow: 0px 0px 8px 0px #0056ad;\r\n    overflow: hidden;\r\n    position: relative;\r\n\r\n    .scroll-wrapper {\r\n      height: 100%;\r\n      overflow-y: scroll;\r\n      overflow-x: hidden;\r\n      position: relative;\r\n\r\n      scrollbar-width: none;\r\n      -ms-overflow-style: none;\r\n\r\n      &::-webkit-scrollbar {\r\n        display: none;\r\n      }\r\n    }\r\n\r\n    &::after {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 20px;\r\n      right: 0;\r\n      height: calc(100% - 40px);\r\n      width: 6px;\r\n      background: rgba(16, 216, 255, 0.1);\r\n      opacity: 0;\r\n      transition: opacity 0.3s;\r\n      pointer-events: none;\r\n    }\r\n\r\n    .scroll-bar {\r\n      position: absolute;\r\n      top: 20px;\r\n      right: 0;\r\n      width: 6px;\r\n      height: var(--scrollbar-height, 100px);\r\n      background: rgba(16, 216, 255, 0.4);\r\n      border-radius: 3px;\r\n      opacity: 0;\r\n      transition: opacity 0.3s;\r\n      transform: translateY(var(--scrollbar-top, 0));\r\n      pointer-events: none;\r\n    }\r\n\r\n    &:hover {\r\n      &::after,\r\n      .scroll-bar {\r\n        opacity: 1;\r\n      }\r\n    }\r\n\r\n    .remengwenzhang-list {\r\n      position: relative;\r\n      height: 40px;\r\n      padding-left: 20px;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      cursor: pointer;\r\n\r\n      .title {\r\n        width: 330px;\r\n        overflow: hidden;\r\n        color: rgba(216, 240, 255, 0.8);\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n        font-family: \"Source Han Sans CN\";\r\n        font-size: 18px;\r\n        font-weight: 700;\r\n        line-height: 20px;\r\n      }\r\n\r\n      .time,\r\n      .sourceName {\r\n        width: 150px;\r\n        color: rgba(216, 240, 255, 0.8);\r\n        text-align: right;\r\n        font-family: \"Source Han Sans CN\";\r\n        font-size: 18px;\r\n        font-weight: 400;\r\n        line-height: 20px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .block {\r\n        position: absolute;\r\n        left: 0px;\r\n        top: 6px;\r\n        width: 10px;\r\n        height: 10px;\r\n        border-radius: 1px;\r\n        background: #1bdcff;\r\n      }\r\n    }\r\n  }\r\n\r\n  .remengwenzhang-box1 {\r\n    width: 100%;\r\n    height: 230px;\r\n    padding: 20px;\r\n    border: 1px solid rgba(16, 216, 255, 0.4);\r\n    background: rgba(0, 0, 0, 0.15);\r\n    box-shadow: 0px 0px 8px 0px #0056ad;\r\n    overflow: hidden;\r\n    position: relative;\r\n\r\n    .scroll-wrapper {\r\n      height: 100%;\r\n      overflow-y: scroll;\r\n      overflow-x: hidden;\r\n      position: relative;\r\n\r\n      scrollbar-width: none;\r\n      -ms-overflow-style: none;\r\n\r\n      &::-webkit-scrollbar {\r\n        display: none;\r\n      }\r\n    }\r\n\r\n    &::after {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 20px;\r\n      right: 0;\r\n      height: calc(100% - 40px);\r\n      width: 6px;\r\n      background: rgba(16, 216, 255, 0.1);\r\n      opacity: 0;\r\n      transition: opacity 0.3s;\r\n      pointer-events: none;\r\n    }\r\n\r\n    .scroll-bar {\r\n      position: absolute;\r\n      top: 20px;\r\n      right: 0;\r\n      width: 6px;\r\n      height: var(--scrollbar-height, 100px);\r\n      background: rgba(16, 216, 255, 0.4);\r\n      border-radius: 3px;\r\n      opacity: 0;\r\n      transition: opacity 0.3s;\r\n      transform: translateY(var(--scrollbar-top, 0));\r\n      pointer-events: none;\r\n    }\r\n\r\n    &:hover {\r\n      &::after,\r\n      .scroll-bar {\r\n        opacity: 1;\r\n      }\r\n    }\r\n\r\n    .remengwenzhang-list {\r\n      position: relative;\r\n      height: 40px;\r\n      padding-left: 20px;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      cursor: pointer;\r\n\r\n      .title {\r\n        width: 330px;\r\n        overflow: hidden;\r\n        color: rgba(216, 240, 255, 0.8);\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n        font-family: \"Source Han Sans CN\";\r\n        font-size: 18px;\r\n        font-weight: 700;\r\n        line-height: 20px;\r\n      }\r\n\r\n      .time,\r\n      .sourceName {\r\n        width: 150px;\r\n        color: rgba(216, 240, 255, 0.8);\r\n        text-align: right;\r\n        font-family: \"Source Han Sans CN\";\r\n        font-size: 18px;\r\n        font-weight: 400;\r\n        line-height: 20px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .block {\r\n        position: absolute;\r\n        left: 0px;\r\n        top: 6px;\r\n        width: 10px;\r\n        height: 10px;\r\n        border-radius: 1px;\r\n        background: #1bdcff;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 弹窗样式\r\n::v-deep .el-dialog {\r\n  background: url(\"../../assets/bigScreenTwo/dialogBackground.png\") no-repeat;\r\n  background-size: 100% 100% !important;\r\n  background-size: cover;\r\n  height: 970px;\r\n\r\n  .el-dialog__header {\r\n    background-color: #1d233400;\r\n    font-size: 30px;\r\n    color: #ffffff;\r\n    line-height: 120px;\r\n    text-shadow: 0px 0px 10px rgba(30, 198, 255, 0.8);\r\n    height: 120px;\r\n\r\n    .el-dialog__title {\r\n      display: inline-block;\r\n      width: calc(100% - 100px);\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n    }\r\n  }\r\n\r\n  .el-dialog__body {\r\n    background-color: #2a304000;\r\n    color: #f2f2f2;\r\n    height: calc(100% - 120px);\r\n    overflow: hidden;\r\n    padding: 20px 30px;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    background-color: #1d233400;\r\n    padding: 18px 20px;\r\n  }\r\n\r\n  .el-button {\r\n    background-color: #002766;\r\n    color: #fff;\r\n    border: 0px;\r\n  }\r\n\r\n  .el-dialog__headerbtn .el-dialog__close {\r\n    background: url(\"../../assets/bigScreenTwo/关闭小.png\") no-repeat;\r\n    background-size: 100% 100% !important;\r\n    background-size: cover;\r\n    width: 31px;\r\n    height: 31px;\r\n    top: 32px;\r\n\r\n    &::before {\r\n      content: none;\r\n    }\r\n  }\r\n}\r\n\r\n.dialog-art {\r\n  background: #1d293b;\r\n  padding: 20px;\r\n  height: calc(100% - 60px) !important;\r\n  overflow-y: auto;\r\n  line-height: 1.8em;\r\n  font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n    Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei, Arial,\r\n    sans-serif;\r\n\r\n  ::v-deep p {\r\n    text-indent: 2em;\r\n  }\r\n}\r\n\r\n/* 自定义滚动条样式 */\r\n::v-deep .dialog-art::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n::v-deep .dialog-art::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 4px;\r\n}\r\n\r\n::v-deep .dialog-art::-webkit-scrollbar-thumb {\r\n  background: rgba(14, 194, 244, 0.6);\r\n  border-radius: 4px;\r\n}\r\n\r\n::v-deep .dialog-art::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(14, 194, 244, 0.8);\r\n}\r\n\r\n.fz {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  margin-bottom: 20px;\r\n\r\n  .text {\r\n    font-weight: 400;\r\n    font-size: 20px;\r\n    color: #ffffff;\r\n    margin-right: 10px;\r\n  }\r\n\r\n  .btns {\r\n    display: flex;\r\n    align-items: center;\r\n    background: #1d293b;\r\n    border-radius: 14px;\r\n    padding: 0 10px;\r\n    height: 28px;\r\n\r\n    .btn-minus,\r\n    .btn-plus {\r\n      width: 24px;\r\n      height: 24px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      cursor: pointer;\r\n      font-size: 20px;\r\n      color: #ffffff;\r\n\r\n      &:hover {\r\n        color: #2f7cfe;\r\n      }\r\n    }\r\n\r\n    .font-size {\r\n      margin: 0 15px;\r\n      color: #ffffff;\r\n      font-size: 16px;\r\n      min-width: 45px;\r\n      text-align: center;\r\n    }\r\n  }\r\n}\r\n\r\n.kejidongtai-box {\r\n  width: 100%;\r\n  // height: 45px;\r\n  // padding-top: 11px;\r\n  // margin: 3px 0;\r\n  display: flex;\r\n  // justify-content: space-around;\r\n  flex-wrap: wrap;\r\n  column-gap: 10px;\r\n  row-gap: 10px;\r\n  padding: 10px;\r\n  margin-top: 10px;\r\n\r\n  .kejidongtai-button {\r\n    // width: 111px;\r\n    height: 33px;\r\n    line-height: 33px;\r\n    text-align: center;\r\n    font-size: 14px;\r\n    color: #ffffff;\r\n    background-image: url(\"../../assets/bigScreenSanhao/kejiqianyan1.png\");\r\n    background-size: 100% 100%;\r\n    padding: 0 12px;\r\n  }\r\n\r\n  .active {\r\n    background-image: url(\"../../assets/bigScreenSanhao/kejiqianyan2.png\");\r\n  }\r\n}\r\n\r\n:deep(.markmap-node) {\r\n  cursor: pointer;\r\n\r\n  &:hover {\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n:deep(.markmap-node-circle) {\r\n  fill: transparent; // 修改节点背景为透明\r\n  stroke-width: 2px;\r\n}\r\n\r\n:deep(.markmap-node-text) {\r\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto,\r\n    \"Helvetica Neue\", Arial;\r\n\r\n  tspan {\r\n    fill: #333 !important; // 修改文字颜色为深色\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n:deep(.markmap-link) {\r\n  fill: none;\r\n  stroke-width: 2.5px; // 加粗连线\r\n}\r\n\r\n// 根节点样式\r\n:deep(.markmap-node[data-depth=\"0\"]) {\r\n  .markmap-node-circle {\r\n    stroke: #0052ff; // 亮蓝色\r\n    stroke-width: 3px;\r\n  }\r\n\r\n  .markmap-node-text tspan {\r\n    font-size: 20px !important;\r\n    font-weight: bold !important;\r\n    fill: #333 !important;\r\n  }\r\n}\r\n\r\n// 二级节点样式\r\n:deep(.markmap-node[data-depth=\"1\"]) {\r\n  .markmap-node-circle {\r\n    stroke: #009600; // 亮绿色\r\n    stroke-width: 2.5px;\r\n  }\r\n\r\n  .markmap-node-text tspan {\r\n    font-size: 18px !important;\r\n    font-weight: 600 !important;\r\n    fill: #333 !important;\r\n  }\r\n}\r\n\r\n// 三级节点样式\r\n:deep(.markmap-node[data-depth=\"2\"]) {\r\n  .markmap-node-circle {\r\n    stroke: #ff6600; // 亮橙色\r\n    stroke-width: 2px;\r\n  }\r\n\r\n  .markmap-node-text tspan {\r\n    font-size: 16px !important;\r\n    font-weight: 500 !important;\r\n    fill: #333 !important;\r\n  }\r\n}\r\n\r\n// 其他层级节点样式\r\n:deep(.markmap-node[data-depth=\"3\"]),\r\n:deep(.markmap-node[data-depth=\"4\"]) {\r\n  .markmap-node-circle {\r\n    stroke: #8000ff; // 亮紫色\r\n    stroke-width: 2px;\r\n  }\r\n\r\n  .markmap-node-text tspan {\r\n    font-size: 14px !important;\r\n    font-weight: 500 !important;\r\n    fill: #333 !important;\r\n  }\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\expressDetails\\index2.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\expressDetails\\index2.vue", "mtime": 1754397040249}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAidnVleCI7DQppbXBvcnQgQVBJIGZyb20gIkAvYXBpL1NjaWVuY2VBcGkvaW5kZXguanMiOw0KaW1wb3J0IHsgZmVlZGJhY2tBZGQgfSBmcm9tICJAL2FwaS9hcnRpY2xlL2xlYXZlTWVzc2FnZSI7DQppbXBvcnQgew0KICBjb250YWluc0h0bWxUYWdzLA0KICBleHRyYWN0SHRtbFRhZ3MsDQogIGhhc1ZhbGlkSHRtbFN0cnVjdHVyZSwNCn0gZnJvbSAiQC91dGlscy9odG1sVXRpbHMiOw0KaW1wb3J0IHsgZm9ybWF0TWFya2Rvd24gfSBmcm9tICJAL3V0aWxzL21hcmtkb3duVXRpbHMiOw0KaW1wb3J0IE1hcmttYXBEaWFsb2cgZnJvbSAiLi9NYXJrbWFwRGlhbG9nLnZ1ZSI7DQppbXBvcnQgVGV4dEVsbGlwc2lzIGZyb20gIkAvY29tcG9uZW50cy9UZXh0RWxsaXBzaXMvaW5kZXgudnVlIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBkaWN0czogW10sDQogIGNvbXBvbmVudHM6IHsNCiAgICBNYXJrbWFwRGlhbG9nLA0KICAgIFRleHRFbGxpcHNpcywNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBjb250ZW50UmVhZHk6IGZhbHNlLA0KICAgICAgdHJhbnNsYXRpb25CdG5TaG93OiB0cnVlLA0KICAgICAgdGl0bGVTaG93OiB0cnVlLA0KICAgICAgZHJhd2VySW5mbzoge30sDQogICAgICBodG1sSnNvbjogIiIsDQogICAgICBwcm9jZXNzZWRIdG1sOiAiIiwNCiAgICAgIG9yaWdpbmFsQXJ0aWNsZVNob3c6IHRydWUsDQogICAgICBhcnRpY2xlTGlzdDogW10sDQogICAgICB0eXBlOiB0cnVlLA0KICAgICAgdGV4dGFyZWE6ICIiLA0KICAgICAgdXJsOiAiIiwNCiAgICAgIHRhYlBvc2l0aW9uOiAxLA0KICAgICAgdG90YWxJbWFnZXM6IDAsDQogICAgICBsb2FkZWRJbWFnZXM6IDAsDQogICAgICBpbWFnZUxvYWRUaW1lb3V0OiBudWxsLA0KICAgICAgbWFya2Rvd25Db250ZW50OiB7DQogICAgICAgIGtleVBvaW50czogIiIsDQogICAgICAgIGVudGl0aWVzOiAiIiwNCiAgICAgIH0sDQogICAgICBtYXJrbWFwVmlzaWJsZTogZmFsc2UsDQogICAgICBtYXJrbWFwQ29udGVudDogIiIsDQogICAgICBtYXJrbWFwVGl0bGU6ICIiLA0KICAgICAgbWFya21hcExvYWRpbmc6IGZhbHNlLA0KICAgIH07DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgLi4ubWFwR2V0dGVycyhbInJvbGVzIiwgIm5hbWUiLCAiYXZhdGFyIl0pLA0KICAgIGlzV2VpeGluQXJ0aWNsZSgpIHsNCiAgICAgIHJldHVybiAoDQogICAgICAgIHRoaXMuZHJhd2VySW5mby5vcmlnaW5hbFVybCAmJg0KICAgICAgICB0aGlzLmRyYXdlckluZm8ub3JpZ2luYWxVcmwuaW5jbHVkZXMoImh0dHBzOi8vbXAud2VpeGluLnFxLmNvbSIpDQogICAgICApOw0KICAgIH0sDQogICAga2V5UG9pbnRzRm9ybWF0dGVkKCkgew0KICAgICAgcmV0dXJuIGZvcm1hdE1hcmtkb3duKHRoaXMubWFya2Rvd25Db250ZW50LmtleVBvaW50cyk7DQogICAgfSwNCiAgICBlbnRpdGllc0Zvcm1hdHRlZCgpIHsNCiAgICAgIHJldHVybiBmb3JtYXRNYXJrZG93bih0aGlzLm1hcmtkb3duQ29udGVudC5lbnRpdGllcyk7DQogICAgfSwNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgIHRoaXMudHJhbnNsYXRpb25CdG5TaG93ID0gdHJ1ZTsNCg0KICAgIC8vIOa3u+WKoOWFqOWxgG1ldGHmoIfnrb7npoHnlKhyZWZlcnJlcg0KICAgIHRoaXMuYWRkTm9SZWZlcnJlck1ldGEoKTsNCg0KICAgIHRoaXMuZGV0YWlscygpOw0KICAgIHRoaXMuZ2V0SW5kZXhEYXRhKCk7DQogICAgLy8gdGhpcy5oYW5sZGVCcm93c2VBZGQoKTsNCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICB0YWJQb3NpdGlvbihuZXdWYWwsIG9sZFZhbCkgew0KICAgICAgY29uc29sZS5sb2coYFRhYiBwb3NpdGlvbiBjaGFuZ2VkIGZyb20gJHtvbGRWYWx9IHRvICR7bmV3VmFsfWApOw0KICAgIH0sDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDmt7vliqDlhajlsYBtZXRh5qCH562+5p2l56aB55So5omA5pyJ5byV55So5aS0DQogICAgYWRkTm9SZWZlcnJlck1ldGEoKSB7DQogICAgICAvLyDmo4Dmn6XmmK/lkKblt7Lnu4/lrZjlnKhtZXRh5qCH562+DQogICAgICBpZiAoDQogICAgICAgIGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJ21ldGFbbmFtZT0icmVmZXJyZXIiXVtjb250ZW50PSJuby1yZWZlcnJlciJdJykNCiAgICAgICkgew0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOWIm+W7uuW5tua3u+WKoG1ldGHmoIfnrb4NCiAgICAgIGNvbnN0IG1ldGEgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCJtZXRhIik7DQogICAgICBtZXRhLm5hbWUgPSAicmVmZXJyZXIiOw0KICAgICAgbWV0YS5jb250ZW50ID0gIm5vLXJlZmVycmVyIjsNCiAgICAgIGRvY3VtZW50LmhlYWQuYXBwZW5kQ2hpbGQobWV0YSk7DQogICAgICBjb25zb2xlLmxvZygi5bey5re75Yqgbm8tcmVmZXJyZXIgbWV0Yeagh+etviIpOw0KICAgIH0sDQoNCiAgICAvLyDovoXliqnmlrnms5XvvJrkv67lpI3lm77niYfnmoRyZWZlcnJlcuiuvue9rg0KICAgIGZpeEltYWdlUmVmZXJyZXIoY29udGVudCkgew0KICAgICAgLy8g56Gu5L+d5q+P5Liq5b6u5L+h5Zu+54mH5qCH562+6YO95pyJcmVmZXJyZXJwb2xpY3k9Im5vLXJlZmVycmVyIuWxnuaApw0KICAgICAgaWYgKCFjb250ZW50KSByZXR1cm4gY29udGVudDsNCg0KICAgICAgLy8g5pu/5o2i5omA5pyJ5b6u5L+h5Zu+54mH5qCH562+77yM56Gu5L+d5a6D5Lus5pyJcmVmZXJyZXJwb2xpY3nlsZ7mgKcNCiAgICAgIHJldHVybiBjb250ZW50LnJlcGxhY2UoDQogICAgICAgIC88aW1nKFtePl0qP3NyYz1bIiddaHR0cHM/OlwvXC9tbWJpelwucSg/OmxvZ298cGljKVwuY25cL1teIiddK1siJ11bXj5dKj8pPi9naSwNCiAgICAgICAgKG1hdGNoLCBhdHRyUGFydCkgPT4gew0KICAgICAgICAgIC8vIOWmguaenOW3sue7j+aciXJlZmVycmVycG9saWN55bGe5oCn77yM5LiN5YaN5re75YqgDQogICAgICAgICAgaWYgKGF0dHJQYXJ0LmluY2x1ZGVzKCJyZWZlcnJlcnBvbGljeSIpKSB7DQogICAgICAgICAgICByZXR1cm4gbWF0Y2g7DQogICAgICAgICAgfQ0KICAgICAgICAgIC8vIOa3u+WKoHJlZmVycmVycG9saWN55bGe5oCnDQogICAgICAgICAgcmV0dXJuIGA8aW1nJHthdHRyUGFydH0gcmVmZXJyZXJwb2xpY3k9Im5vLXJlZmVycmVyIj5gOw0KICAgICAgICB9DQogICAgICApOw0KICAgIH0sDQoNCiAgICAvLyDkvb/nlKhjYW52YXPmiJblm77niYfku6PnkIblop7lvLrlvq7kv6Hlm77niYfnmoTmlrnms5XvvIjkuI3mm7/mjaJET03oioLngrnvvIkNCiAgICByZXBsYWNlQWxsV2VjaGF0SW1hZ2VzKCkgew0KICAgICAgLy8g5aaC5p6c5LiN5piv5b6u5L+h5paH56ug77yM55u05o6l6L+U5ZueDQogICAgICBpZiAoIXRoaXMuaXNXZWl4aW5BcnRpY2xlKSB7DQogICAgICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoKTsNCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlKSA9PiB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgLy8g5om+5Yiw5omA5pyJ5b6u5L+h5Z+f5ZCN55qE5Zu+54mHDQogICAgICAgICAgY29uc3Qgd2VjaGF0SW1hZ2VzID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvckFsbCgNCiAgICAgICAgICAgICdpbWdbc3JjKj0ibW1iaXoucXBpYy5jbiJdLCBpbWdbc3JjKj0ibW1iaXoucWxvZ28uY24iXSwgaW1nW2RhdGEtc3JjKj0ibW1iaXoiXSwgaW1nW3NyYyo9Im1tc25zLnFwaWMuY24iXScNCiAgICAgICAgICApOw0KDQogICAgICAgICAgaWYgKHdlY2hhdEltYWdlcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgICAgIHJlc29sdmUoKTsNCiAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBjb25zb2xlLmxvZyhg5byA5aeL5aKe5by6JHt3ZWNoYXRJbWFnZXMubGVuZ3RofeW8oOW+ruS/oeWbvueJh++8jOS/neeVmeeOsOacieWbvueJh2ApOw0KICAgICAgICAgIGxldCBwcm9jZXNzZWRDb3VudCA9IDA7DQoNCiAgICAgICAgICAvLyDlpITnkIbmr4/kuIDlvKDlm77niYcNCiAgICAgICAgICB3ZWNoYXRJbWFnZXMuZm9yRWFjaCgoaW1nLCBpbmRleCkgPT4gew0KICAgICAgICAgICAgLy8g5aaC5p6c5Zu+54mH5bey57uP6KKr5pu/5o2i6L+H5LiU6Z2e56m655m977yM5YiZ6Lez6L+HDQogICAgICAgICAgICBpZiAoDQogICAgICAgICAgICAgIGltZy5oYXNBdHRyaWJ1dGUoImRhdGEtd3gtcmVwbGFjZWQiKSAmJg0KICAgICAgICAgICAgICBpbWcuY29tcGxldGUgJiYNCiAgICAgICAgICAgICAgaW1nLm5hdHVyYWxXaWR0aCA+IDANCiAgICAgICAgICAgICkgew0KICAgICAgICAgICAgICBwcm9jZXNzZWRDb3VudCsrOw0KICAgICAgICAgICAgICBpZiAocHJvY2Vzc2VkQ291bnQgPj0gd2VjaGF0SW1hZ2VzLmxlbmd0aCkgew0KICAgICAgICAgICAgICAgIHJlc29sdmUoKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIC8vIOiusOW9leWOn+Wni+WwuuWvuOWSjOagt+W8jw0KICAgICAgICAgICAgY29uc3Qgb3JpZ2luYWxXaWR0aCA9IGltZy5zdHlsZS53aWR0aCB8fCBpbWcud2lkdGggfHwgImF1dG8iOw0KICAgICAgICAgICAgY29uc3Qgb3JpZ2luYWxIZWlnaHQgPSBpbWcuc3R5bGUuaGVpZ2h0IHx8IGltZy5oZWlnaHQgfHwgImF1dG8iOw0KDQogICAgICAgICAgICAvLyDlj6rmnInlvZPlm77niYfml6Dms5XmmL7npLrml7bmiY3ov5vooYzlpITnkIYNCiAgICAgICAgICAgIGlmICghaW1nLmNvbXBsZXRlIHx8IGltZy5uYXR1cmFsV2lkdGggPT09IDApIHsNCiAgICAgICAgICAgICAgLy8g6I635Y+W5Zu+54mH5rqQDQogICAgICAgICAgICAgIGNvbnN0IG9yaWdpbmFsU3JjID0NCiAgICAgICAgICAgICAgICBpbWcuZ2V0QXR0cmlidXRlKCJkYXRhLW9yaWdpbmFsLXNyYyIpIHx8DQogICAgICAgICAgICAgICAgaW1nLmdldEF0dHJpYnV0ZSgic3JjIik7DQogICAgICAgICAgICAgIGlmICghb3JpZ2luYWxTcmMpIHsNCiAgICAgICAgICAgICAgICAvLyDml6Dms5Xojrflj5bmupDvvIzot7Pov4cNCiAgICAgICAgICAgICAgICBwcm9jZXNzZWRDb3VudCsrOw0KICAgICAgICAgICAgICAgIGlmIChwcm9jZXNzZWRDb3VudCA+PSB3ZWNoYXRJbWFnZXMubGVuZ3RoKSB7DQogICAgICAgICAgICAgICAgICByZXNvbHZlKCk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC8vIOa3u+WKoOW/heimgeeahOWxnuaApw0KICAgICAgICAgICAgICBpbWcuc2V0QXR0cmlidXRlKCJyZWZlcnJlcnBvbGljeSIsICJuby1yZWZlcnJlciIpOw0KICAgICAgICAgICAgICBpbWcuY2xhc3NMaXN0LmFkZCgid3gtaW1nIik7DQogICAgICAgICAgICAgIGltZy5zdHlsZS5tYXhXaWR0aCA9ICIxMDAlIjsNCiAgICAgICAgICAgICAgaW1nLnN0eWxlLmhlaWdodCA9ICJhdXRvIjsNCg0KICAgICAgICAgICAgICAvLyDorr7nva7moIfor4YNCiAgICAgICAgICAgICAgaW1nLnNldEF0dHJpYnV0ZSgiZGF0YS13eC1yZXBsYWNlZCIsICJ0cnVlIik7DQoNCiAgICAgICAgICAgICAgLy8g5bCd6K+V5L2/55SoSFRUUFPliqDovb0NCiAgICAgICAgICAgICAgaWYgKG9yaWdpbmFsU3JjLnN0YXJ0c1dpdGgoImh0dHA6IikpIHsNCiAgICAgICAgICAgICAgICBjb25zdCBodHRwc1VybCA9IG9yaWdpbmFsU3JjLnJlcGxhY2UoL15odHRwOi8sICJodHRwczoiKTsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhg5bCd6K+V5L2/55SoSFRUUFPljY/orq46ICR7aHR0cHNVcmx9YCk7DQogICAgICAgICAgICAgICAgaW1nLnNyYyA9IGh0dHBzVXJsOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIC8vIOWwneivlea3u+WKoOagvOW8j+WPguaVsA0KICAgICAgICAgICAgICBlbHNlIGlmICghb3JpZ2luYWxTcmMuaW5jbHVkZXMoInd4X2ZtdD0iKSkgew0KICAgICAgICAgICAgICAgIGNvbnN0IHNlcGFyYXRvciA9IG9yaWdpbmFsU3JjLmluY2x1ZGVzKCI/IikgPyAiJiIgOiAiPyI7DQogICAgICAgICAgICAgICAgY29uc3Qgc3JjV2l0aEZvcm1hdCA9IGAke29yaWdpbmFsU3JjfSR7c2VwYXJhdG9yfXd4X2ZtdD1qcGVnYDsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhg5bCd6K+V5re75Yqg5qC85byP5Y+C5pWwOiAke3NyY1dpdGhGb3JtYXR9YCk7DQogICAgICAgICAgICAgICAgaW1nLnNyYyA9IHNyY1dpdGhGb3JtYXQ7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgLy8g5peg6K665piv5ZCm5aSE55CG77yM6YO96K6h5pWwDQogICAgICAgICAgICBwcm9jZXNzZWRDb3VudCsrOw0KICAgICAgICAgICAgaWYgKHByb2Nlc3NlZENvdW50ID49IHdlY2hhdEltYWdlcy5sZW5ndGgpIHsNCiAgICAgICAgICAgICAgcmVzb2x2ZSgpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KDQogICAgICAgICAgLy8g6K6+572u6LaF5pe25L+d6ZqcDQogICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgICByZXNvbHZlKCk7DQogICAgICAgICAgfSwgMzAwMCk7DQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCLlop7lvLrlvq7kv6Hlm77niYflh7rplJk6IiwgZSk7DQogICAgICAgICAgcmVzb2x2ZSgpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLy8g5by65Yi26YeN5paw5Yqg6L295YaF5a655Yy65Z+f77yM6YG/5YWN5riF56m65a+86Ie05Zu+54mH5Lii5aSxDQogICAgZm9yY2VSZWxvYWRDb250ZW50KCkgew0KICAgICAgLy8g5aaC5p6c5rKh5pyJ5YaF5a655oiW5LiN5piv5b6u5L+h5paH56ug77yM55u05o6l6L+U5ZueDQogICAgICBpZiAoIXRoaXMuaHRtbEpzb24gfHwgIXRoaXMuaXNXZWl4aW5BcnRpY2xlKSB7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY29uc29sZS5sb2coIuW8gOWni+WinuW8uuWbvueJh+aYvuekuuWkhOeQhu+8jOS/neeVmeeOsOaciURPTee7k+aehCIpOw0KDQogICAgICAvLyDkuI3lho3muIXnqbrlhoXlrrnvvIznm7TmjqXlnKjnjrDmnIlET03kuIrlpITnkIblm77niYcNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgLy8g5YWI6L+b6KGM5Z+656GA5L+u5aSNDQogICAgICAgIHRoaXMuZml4V2VjaGF0SW1hZ2VzKCk7DQoNCiAgICAgICAgLy8g5bu26L+f5omn6KGM5b275bqV5pu/5o2iDQogICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgIHRoaXMucmVwbGFjZUFsbFdlY2hhdEltYWdlcygpLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgY29uc29sZS5sb2coIuWujOaIkOW9u+W6leabv+aNouW+ruS/oeWbvueJhyIpOw0KICAgICAgICAgICAgLy8g5pyA5ZCO5by65Yi25pu05paw5LiA5qyh6KeG5Zu+DQogICAgICAgICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9LCAxMDApOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOmihOWkhOeQhuW+ruS/oeWbvueJhw0KICAgIHByZWxvYWRXZWNoYXRJbWFnZXMoY29udGVudCkgew0KICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlKSA9PiB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgLy8g5YWI5bqU55So5YWo5bGAcmVmZXJyZXLnrZbnlaUNCiAgICAgICAgICB0aGlzLmFkZE5vUmVmZXJyZXJNZXRhKCk7DQoNCiAgICAgICAgICAvLyDlhYjkv67lpI3lhoXlrrnkuK3ljIXlkKvovazkuYnlrZfnrKbnmoTlm77niYfmoIfnrb4NCiAgICAgICAgICBjb250ZW50ID0gdGhpcy5maXhFc2NhcGVkSW1hZ2VUYWdzKGNvbnRlbnQpOw0KDQogICAgICAgICAgLy8g5L+u5aSN5YaF5a655Lit55qE5Zu+54mH5qCH562+DQogICAgICAgICAgY29udGVudCA9IHRoaXMuaGFuZGxlV2VjaGF0SW1hZ2VzKGNvbnRlbnQpOw0KDQogICAgICAgICAgLy8g5aSE55CG55u45a+56Lev5b6E5Zu+54mHDQogICAgICAgICAgY29udGVudCA9IHRoaXMuaGFuZGxlUmVsYXRpdmVJbWFnZVBhdGhzKGNvbnRlbnQpOw0KDQogICAgICAgICAgLy8g5qCH6K6w5YaF5a655YeG5aSH5aW95LqGDQogICAgICAgICAgdGhpcy5jb250ZW50UmVhZHkgPSB0cnVlOw0KICAgICAgICAgIHJlc29sdmUoY29udGVudCk7DQoNCiAgICAgICAgICAvLyDlnKjlhoXlrrnliqDovb3lkI7vvIzkuIvkuIDkuKrkuovku7blvqrnjq/lho3lupTnlKjkv67lpI0NCiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuZml4V2VjaGF0SW1hZ2VzKCk7DQogICAgICAgICAgfSwgMCk7DQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCLpooTlpITnkIblvq7kv6Hlm77niYflh7rplJk6IiwgZSk7DQogICAgICAgICAgdGhpcy5jb250ZW50UmVhZHkgPSB0cnVlOw0KICAgICAgICAgIHJlc29sdmUoY29udGVudCk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDkv67lpI3ljIXlkKvovazkuYnlrZfnrKbnmoTlm77niYfmoIfnrb4gLSDmm7TlvbvlupXnmoTlpITnkIYNCiAgICBmaXhFc2NhcGVkSW1hZ2VUYWdzKGNvbnRlbnQpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGlmICghY29udGVudCkgcmV0dXJuIGNvbnRlbnQ7DQoNCiAgICAgICAgLy8g5YWI5aSE55CG5YWo5bGA55qE6L2s5LmJ5a2X56ym77yM566A5YyW5ZCO57ut5aSE55CGDQogICAgICAgIGNvbnRlbnQgPSBjb250ZW50DQogICAgICAgICAgLnJlcGxhY2UoL1xcIi9nLCAnIicpDQogICAgICAgICAgLnJlcGxhY2UoL1xcJy9nLCAiJyIpDQogICAgICAgICAgLnJlcGxhY2UoL1xcXFwvZywgIlxcIikNCiAgICAgICAgICAucmVwbGFjZSgvXFwmcXVvdDsvZywgIiZxdW90OyIpDQogICAgICAgICAgLnJlcGxhY2UoLyZhbXA7L2csICImIik7DQoNCiAgICAgICAgLy8g5omp5bGV5Yy56YWN5qih5byP77yM5o2V6I635omA5pyJ5Y+v6IO955qE6Zeu6aKY5qC85byPDQogICAgICAgIGNvbnN0IGVzY2FwZWRUYWdSZWdleCA9DQogICAgICAgICAgLzxpbWdbXj5dKj8oPzpzcmN8ZGF0YS1zcmMpPVsiJ10/KD86XFwmcXVvdDt8XFwifCZxdW90O3wlMjJ8XFxcXCZxdW90O3xcXFxcIikoW14iJ10rPykoPzpcXCZxdW90O3xcXCJ8JnF1b3Q7fCUyMnxcXFxcJnF1b3Q7fFxcXFwiKVsiJ10/W14+XSo/Pi9naTsNCiAgICAgICAgY29uc3QgYmFkU3R5bGVSZWdleCA9DQogICAgICAgICAgLzxpbWdbXj5dKj9zdHlsZT1bIiddW14iJ10qPyg/OjE2cHh8d2hpdGUtc3BhY2UpW14iJ10qP1siJ11bXj5dKj8+L2dpOw0KICAgICAgICBjb25zdCBicm9rZW5QYXRoUmVnZXggPQ0KICAgICAgICAgIC88aW1nW14+XSo/c3JjPVsiJ11bXiInXSo/bW1iaXpbXiInXSo/WyInXVtePl0qPz4vZ2k7DQoNCiAgICAgICAgLy8g5ZCI5bm25omA5pyJ5Yy56YWN57uT5p6cDQogICAgICAgIGNvbnN0IGVzY2FwZWRUYWdzID0gY29udGVudC5tYXRjaChlc2NhcGVkVGFnUmVnZXgpIHx8IFtdOw0KICAgICAgICBjb25zdCBiYWRTdHlsZVRhZ3MgPSBjb250ZW50Lm1hdGNoKGJhZFN0eWxlUmVnZXgpIHx8IFtdOw0KICAgICAgICBjb25zdCBicm9rZW5QYXRoVGFncyA9IGNvbnRlbnQubWF0Y2goYnJva2VuUGF0aFJlZ2V4KSB8fCBbXTsNCg0KICAgICAgICAvLyDljrvph40gLSDovazmjaLkuLpTZXTnhLblkI7lho3ovazlm57mlbDnu4QNCiAgICAgICAgY29uc3QgYWxsVGFncyA9IFsNCiAgICAgICAgICAuLi5uZXcgU2V0KFsuLi5lc2NhcGVkVGFncywgLi4uYmFkU3R5bGVUYWdzLCAuLi5icm9rZW5QYXRoVGFnc10pLA0KICAgICAgICBdOw0KDQogICAgICAgIGlmIChhbGxUYWdzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICAgIHJldHVybiBjb250ZW50OyAvLyDmsqHmnInmib7liLDpnIDopoHkv67lpI3nmoTmoIfnrb4NCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnNvbGUubG9nKGDmib7liLAke2FsbFRhZ3MubGVuZ3RofeS4quWPr+iDveaciemXrumimOeahOWbvueJh+agh+etvmApOw0KDQogICAgICAgIC8vIOWkhOeQhuavj+S4qumXrumimOagh+etvg0KICAgICAgICBmb3IgKGNvbnN0IHRhZyBvZiBhbGxUYWdzKSB7DQogICAgICAgICAgLy8g5o+Q5Y+W5Zu+54mHVVJMIC0g5bCd6K+V5aSa56eN5qih5byPDQogICAgICAgICAgbGV0IGltZ1VybCA9ICIiOw0KDQogICAgICAgICAgLy8g5bCd6K+V5Yy56YWN5ZCE56eN5Y+v6IO955qEc3Jj5qC85byPDQogICAgICAgICAgY29uc3QgcGF0dGVybnMgPSBbDQogICAgICAgICAgICAvc3JjPVsiJ10/KD86XFwmcXVvdDt8XFwifCZxdW90O3wlMjJ8XFxcXCZxdW90O3xcXFxcIik/KFteIic8PlxzXSs/bW1iaXpbXiInPD5cc10rKSg/OlxcJnF1b3Q7fFxcInwmcXVvdDt8JTIyfFxcXFwmcXVvdDt8XFxcXCIpP1siJ10/L2ksDQogICAgICAgICAgICAvZGF0YS1zcmM9WyInXT8oPzpcXCZxdW90O3xcXCJ8JnF1b3Q7fCUyMnxcXFxcJnF1b3Q7fFxcXFwiKT8oW14iJzw+XHNdKz9tbWJpelteIic8PlxzXSspKD86XFwmcXVvdDt8XFwifCZxdW90O3wlMjJ8XFxcXCZxdW90O3xcXFxcIik/WyInXT8vaSwNCiAgICAgICAgICAgIC9vcmlnaW5hbC1zcmM9WyInXT8oPzpcXCZxdW90O3xcXCJ8JnF1b3Q7fCUyMnxcXFxcJnF1b3Q7fFxcXFwiKT8oW14iJzw+XHNdKz9tbWJpelteIic8PlxzXSspKD86XFwmcXVvdDt8XFwifCZxdW90O3wlMjJ8XFxcXCZxdW90O3xcXFxcIik/WyInXT8vaSwNCiAgICAgICAgICBdOw0KDQogICAgICAgICAgLy8g5bCd6K+V5omA5pyJ5qih5byP55u05Yiw5om+5Yiw5Yy56YWN6aG5DQogICAgICAgICAgZm9yIChjb25zdCBwYXR0ZXJuIG9mIHBhdHRlcm5zKSB7DQogICAgICAgICAgICBjb25zdCBtYXRjaCA9IHRhZy5tYXRjaChwYXR0ZXJuKTsNCiAgICAgICAgICAgIGlmIChtYXRjaCAmJiBtYXRjaFsxXSkgew0KICAgICAgICAgICAgICBpbWdVcmwgPSBtYXRjaFsxXTsNCiAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KDQogICAgICAgICAgaWYgKCFpbWdVcmwpIHsNCiAgICAgICAgICAgIC8vIOWmguaenOS7jeeEtuaXoOazleaPkOWPllVSTO+8jOi3s+i/h+atpOagh+etvg0KICAgICAgICAgICAgY29udGludWU7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g5riF55CGVVJM5bm25re75Yqg5b+F6KaB55qE5Y+C5pWwDQogICAgICAgICAgaW1nVXJsID0gaW1nVXJsLnJlcGxhY2UoDQogICAgICAgICAgICAvXFwmcXVvdDt8XFwifCZxdW90O3wlMjJ8XFxcXCZxdW90O3xcXFxcIi9nLA0KICAgICAgICAgICAgIiINCiAgICAgICAgICApOw0KICAgICAgICAgIGlmICghaW1nVXJsLmluY2x1ZGVzKCJ3eF9mbXQ9IikgJiYgaW1nVXJsLmluY2x1ZGVzKCJtbWJpeiIpKSB7DQogICAgICAgICAgICBjb25zdCBzZXBhcmF0b3IgPSBpbWdVcmwuaW5jbHVkZXMoIj8iKSA/ICImIiA6ICI/IjsNCiAgICAgICAgICAgIGltZ1VybCArPSBgJHtzZXBhcmF0b3J9d3hfZm10PWpwZWdgOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOWkhOeQhuWPr+iDveWtmOWcqOeahOWNj+iurumXrumimA0KICAgICAgICAgIGlmICghaW1nVXJsLnN0YXJ0c1dpdGgoImh0dHAiKSkgew0KICAgICAgICAgICAgaWYgKGltZ1VybC5zdGFydHNXaXRoKCIvLyIpKSB7DQogICAgICAgICAgICAgIGltZ1VybCA9ICJodHRwczoiICsgaW1nVXJsOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgaW1nVXJsID0gImh0dHBzOi8vIiArIGltZ1VybDsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDliJvlu7rlubLlh4DnmoTmm7/mjaLmoIfnrb4gLSDmm7TnroDmtIHkvYblrozmlbQNCiAgICAgICAgICBjb25zdCBuZXdUYWcgPSBgPGltZyByZWZlcnJlcnBvbGljeT0ibm8tcmVmZXJyZXIiIGNsYXNzPSJ3eC1pbWciIHNyYz0iJHtpbWdVcmx9IiBkYXRhLXNyYz0iJHtpbWdVcmx9IiBkYXRhLW9yaWdpbmFsLXNyYz0iJHtpbWdVcmx9IiBzdHlsZT0ibWF4LXdpZHRoOjEwMCU7aGVpZ2h0OmF1dG8haW1wb3J0YW50OyIgLz5gOw0KDQogICAgICAgICAgLy8g5pu/5o2i5Y6f5aeL5qCH562+DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSh0YWcsIG5ld1RhZyk7DQogICAgICAgIH0NCg0KICAgICAgICByZXR1cm4gY29udGVudDsNCiAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi5L+u5aSN5Zu+54mH5qCH562+5Ye66ZSZOiIsIGUpOw0KICAgICAgICByZXR1cm4gY29udGVudDsgLy8g5Ye66ZSZ5pe26L+U5Zue5Y6f5aeL5YaF5a65DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWKqOaAgeS/ruWkjeWbvueJh+aWueazlSAtIOWinuW8uueJiO+8jOWunueOsERPTemHjeaWsOa4suafkw0KICAgIGZpeFdlY2hhdEltYWdlcygpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOa3u+WKoOWFqOWxgOagt+W8j++8jOehruS/neaJgOacieW+ruS/oeWbvueJh+mDveaciW5vLXJlZmVycmVyDQogICAgICAgIGNvbnN0IHN0eWxlID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgic3R5bGUiKTsNCiAgICAgICAgc3R5bGUudGV4dENvbnRlbnQgPSBgDQogICAgICAgICAgaW1nW3NyYyo9Im1tYml6LnFwaWMuY24iXSwgaW1nW3NyYyo9Im1tYml6LnFsb2dvLmNuIl0gew0KICAgICAgICAgICAgbWF4LXdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7DQogICAgICAgICAgICBoZWlnaHQ6IGF1dG8gIWltcG9ydGFudDsNCiAgICAgICAgICAgIGRpc3BsYXk6IGJsb2NrICFpbXBvcnRhbnQ7DQogICAgICAgICAgICBtYXJnaW46IDEwcHggYXV0byAhaW1wb3J0YW50Ow0KICAgICAgICAgICAgb2JqZWN0LWZpdDogY29udGFpbiAhaW1wb3J0YW50Ow0KICAgICAgICAgICAgLXdlYmtpdC1yZWZlcnJlcjogbm8tcmVmZXJyZXIgIWltcG9ydGFudDsNCiAgICAgICAgICAgIHJlZmVycmVycG9saWN5OiBuby1yZWZlcnJlciAhaW1wb3J0YW50Ow0KICAgICAgICAgIH0NCiAgICAgICAgYDsNCiAgICAgICAgZG9jdW1lbnQuaGVhZC5hcHBlbmRDaGlsZChzdHlsZSk7DQoNCiAgICAgICAgLy8g5om+5Yiw5omA5pyJ5b6u5L+h5Z+f5ZCN55qE5Zu+54mHDQogICAgICAgIGNvbnN0IHdlY2hhdEltYWdlcyA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoDQogICAgICAgICAgJ2ltZ1tzcmMqPSJtbWJpei5xcGljLmNuIl0sIGltZ1tzcmMqPSJtbWJpei5xbG9nby5jbiJdLCBpbWdbc3JjKj0ibW1zbnMucXBpYy5jbiJdJw0KICAgICAgICApOw0KDQogICAgICAgIGlmICh3ZWNoYXRJbWFnZXMubGVuZ3RoID4gMCkgew0KICAgICAgICAgIGNvbnNvbGUubG9nKA0KICAgICAgICAgICAgYOmhtemdouS4reaJvuWIsCR7d2VjaGF0SW1hZ2VzLmxlbmd0aH3lvKDlvq7kv6Hlm77niYfvvIzlupTnlKjlhajlsYDkv67lpI1gDQogICAgICAgICAgKTsNCg0KICAgICAgICAgIHdlY2hhdEltYWdlcy5mb3JFYWNoKChpbWcpID0+IHsNCiAgICAgICAgICAgIC8vIOa3u+WKoOW/heimgeeahOWxnuaApw0KICAgICAgICAgICAgaW1nLnNldEF0dHJpYnV0ZSgicmVmZXJyZXJwb2xpY3kiLCAibm8tcmVmZXJyZXIiKTsNCiAgICAgICAgICAgIGltZy5jbGFzc0xpc3QuYWRkKCJ3eC1pbWciKTsNCg0KICAgICAgICAgICAgLy8g5aaC5p6c5Zu+54mH5bCa5pyq6L+b6KGM6ZSZ6K+v5aSE55CG77yM5re75Yqg6ZSZ6K+v5aSE55CGDQogICAgICAgICAgICBpZiAoIWltZy5oYXNBdHRyaWJ1dGUoImRhdGEtZXJyb3ItaGFuZGxlZCIpKSB7DQogICAgICAgICAgICAgIGltZy5zZXRBdHRyaWJ1dGUoImRhdGEtZXJyb3ItaGFuZGxlZCIsICJ0cnVlIik7DQoNCiAgICAgICAgICAgICAgLy8g5re75Yqg6ZSZ6K+v5aSE55CGDQogICAgICAgICAgICAgIGltZy5vbmVycm9yID0gZnVuY3Rpb24gKCkgew0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCLlm77niYfliqDovb3lpLHotKXvvIzlupTnlKjljaDkvY3moLflvI8iKTsNCiAgICAgICAgICAgICAgICB0aGlzLnN0eWxlLmJvcmRlciA9ICIxcHggZGFzaGVkICNjY2MiOw0KICAgICAgICAgICAgICAgIHRoaXMuc3R5bGUucGFkZGluZyA9ICIxMHB4IjsNCiAgICAgICAgICAgICAgICB0aGlzLnN0eWxlLndpZHRoID0gImF1dG8iOw0KICAgICAgICAgICAgICAgIHRoaXMuc3R5bGUuaGVpZ2h0ID0gImF1dG8iOw0KICAgICAgICAgICAgICAgIHRoaXMuc3R5bGUubWluSGVpZ2h0ID0gIjEwMHB4IjsNCiAgICAgICAgICAgICAgICB0aGlzLmFsdCA9ICLlm77niYfliqDovb3lpLHotKUiOw0KICAgICAgICAgICAgICB9Ow0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuS/ruWkjeW+ruS/oeWbvueJh+WHuumUmToiLCBlKTsNCiAgICAgIH0NCg0KICAgICAgLy8g6L+U5Zue5LiA5LiqUHJvbWlzZe+8jOaWueS+v+WklumDqOajgOa1i+WujOaIkOeKtuaAgQ0KICAgICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZSgpOw0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbnm7jlr7not6/lvoTlm77niYfnmoTmlrnms5UNCiAgICBoYW5kbGVSZWxhdGl2ZUltYWdlUGF0aHMoY29udGVudCkgew0KICAgICAgaWYgKCFjb250ZW50IHx8ICF0aGlzLmRyYXdlckluZm8/Lm9yaWdpbmFsVXJsKSB7DQogICAgICAgIHJldHVybiBjb250ZW50Ow0KICAgICAgfQ0KDQogICAgICAvLyDku47ljp/mlofpk77mjqXkuK3mj5Dlj5bln7rnoYBVUkwNCiAgICAgIGNvbnN0IG9yaWdpbmFsVXJsID0gdGhpcy5kcmF3ZXJJbmZvLm9yaWdpbmFsVXJsOw0KICAgICAgY29uc3QgdXJsUGFydHMgPSBvcmlnaW5hbFVybC5zcGxpdCgiLyIpOw0KICAgICAgdXJsUGFydHMucG9wKCk7IC8vIOenu+mZpOaWh+S7tuWQjQ0KICAgICAgY29uc3QgYmFzZVVybCA9IHVybFBhcnRzLmpvaW4oIi8iKSArICIvIjsNCg0KICAgICAgLy8g5aSE55CG5omA5pyJ55u45a+56Lev5b6E5Zu+54mHDQogICAgICByZXR1cm4gY29udGVudC5yZXBsYWNlKA0KICAgICAgICAvPGltZyhbXj5dKj8pc3JjXHMqPVxzKlsiJ10oW14iJ10rKVsiJ10oW14+XSo/KT4vZ2ksDQogICAgICAgIChtYXRjaCwgYmVmb3JlLCBzcmMsIGFmdGVyKSA9PiB7DQogICAgICAgICAgLy8g6Lez6L+H57ud5a+56Lev5b6EDQogICAgICAgICAgaWYgKHNyYy5zdGFydHNXaXRoKCJodHRwIikgfHwgc3JjLnN0YXJ0c1dpdGgoIi8vIikpIHsNCiAgICAgICAgICAgIHJldHVybiBtYXRjaDsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDot7Pov4fpnZ7lm77niYfmlofku7YNCiAgICAgICAgICBpZiAoIS9cLihwbmd8anBnfGpwZWd8Z2lmfHdlYnB8Ym1wfHN2ZykoXD8uKik/JC9pLnRlc3Qoc3JjKSkgew0KICAgICAgICAgICAgcmV0dXJuIG1hdGNoOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOi9rOaNouebuOWvuei3r+W+hA0KICAgICAgICAgIGxldCBuZXdTcmMgPSAiIjsNCiAgICAgICAgICBpZiAoc3JjLnN0YXJ0c1dpdGgoIi4vIikpIHsNCiAgICAgICAgICAgIG5ld1NyYyA9IGJhc2VVcmwgKyBzcmMuc3Vic3RyaW5nKDIpOw0KICAgICAgICAgIH0gZWxzZSBpZiAoc3JjLnN0YXJ0c1dpdGgoIi8iKSkgew0KICAgICAgICAgICAgY29uc3QgdXJsT2JqID0gbmV3IFVSTChvcmlnaW5hbFVybCk7DQogICAgICAgICAgICBuZXdTcmMgPSB1cmxPYmoucHJvdG9jb2wgKyAiLy8iICsgdXJsT2JqLmhvc3QgKyBzcmM7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIG5ld1NyYyA9IGJhc2VVcmwgKyBzcmM7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g5p6E5bu65paw55qEaW1n5qCH562+77yM56e76Zmk5omA5pyJb2xkKuWxnuaApw0KICAgICAgICAgIGxldCBuZXdUYWcgPSBgPGltZyR7YmVmb3JlfSBzcmM9IiR7bmV3U3JjfSIke2FmdGVyfT5gOw0KICAgICAgICAgIG5ld1RhZyA9IG5ld1RhZy5yZXBsYWNlKA0KICAgICAgICAgICAgL1xzKihvbGRzcmN8b2xkU3JjfG9sZC1zcmMpXHMqPVxzKlsiJ11bXiInXSpbIiddL2dpLA0KICAgICAgICAgICAgIiINCiAgICAgICAgICApOw0KDQogICAgICAgICAgcmV0dXJuIG5ld1RhZzsNCiAgICAgICAgfQ0KICAgICAgKTsNCiAgICB9LA0KDQogICAgLy8g5aSE55CG5b6u5L+h5Zu+54mH55qE5YWs5YWx5pa55rOVDQogICAgaGFuZGxlV2VjaGF0SW1hZ2VzKGNvbnRlbnQpIHsNCiAgICAgIGlmICghY29udGVudCkgcmV0dXJuIGNvbnRlbnQ7DQoNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOWFiOWkhOeQhui9rOS5ieWtl+espumXrumimA0KICAgICAgICBjb250ZW50ID0gY29udGVudA0KICAgICAgICAgIC5yZXBsYWNlKC9cXCIvZywgJyInKQ0KICAgICAgICAgIC5yZXBsYWNlKC9cXCcvZywgIiciKQ0KICAgICAgICAgIC5yZXBsYWNlKC9cXFxcL2csICJcXCIpOw0KDQogICAgICAgIC8vIOWwhuaJgOacieW+ruS/oeWbvueJh1VSTOi/m+ihjOaPkOWPluWSjOabv+aNog0KICAgICAgICBjb25zdCByZWdleCA9DQogICAgICAgICAgLzxpbWdbXj5dKj9zcmM9WyInXShodHRwcz86XC9cL21tYml6XC5xKD86bG9nb3xwaWMpXC5jblwvW14iJ10rKVsiJ11bXj5dKj8+L2dpOw0KDQogICAgICAgIC8vIOeugOWNleebtOaOpeeahOabv+aNou+8jOehruS/neavj+S4quW+ruS/oeWbvueJh+mDveacieato+ehruWxnuaApw0KICAgICAgICBsZXQgbmV3Q29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgNCiAgICAgICAgICByZWdleCwNCiAgICAgICAgICAnPGltZyBzcmM9IiQxIiByZWZlcnJlcnBvbGljeT0ibm8tcmVmZXJyZXIiIGNsYXNzPSJ3eC1pbWciIHN0eWxlPSJtYXgtd2lkdGg6MTAwJTtoZWlnaHQ6YXV0bzsiIC8+Jw0KICAgICAgICApOw0KDQogICAgICAgIC8vIOi/mOmcgOimgeWkhOeQhuW3suiiq+i9rOS5ieeahOWbvueJh1VSTA0KICAgICAgICBjb25zdCBlc2NhcGVkUmVnZXggPQ0KICAgICAgICAgIC88aW1nW14+XSo/c3JjPVsiJ10/KFxcPyZxdW90O3xcXD8ifCZxdW90O3wlMjIpKGh0dHBzPzpcL1wvbW1iaXpbXiInJl0rKShcXD8mcXVvdDt8XFw/InwmcXVvdDt8JTIyKVsiJ10/W14+XSo/Pi9naTsNCiAgICAgICAgbmV3Q29udGVudCA9IG5ld0NvbnRlbnQucmVwbGFjZSgNCiAgICAgICAgICBlc2NhcGVkUmVnZXgsDQogICAgICAgICAgJzxpbWcgc3JjPSIkMiIgcmVmZXJyZXJwb2xpY3k9Im5vLXJlZmVycmVyIiBjbGFzcz0id3gtaW1nIiBzdHlsZT0ibWF4LXdpZHRoOjEwMCU7aGVpZ2h0OmF1dG87IiAvPicNCiAgICAgICAgKTsNCg0KICAgICAgICByZXR1cm4gbmV3Q29udGVudDsNCiAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi5aSE55CG5b6u5L+h5Zu+54mHSFRNTOWHuumUmToiLCBlKTsNCiAgICAgICAgcmV0dXJuIGNvbnRlbnQ7DQogICAgICB9DQogICAgfSwNCg0KICAgIGFzeW5jIGdldEluZGV4RGF0YSgpIHsNCiAgICAgIGF3YWl0IEFQSS5yZWNvbW1lbmRIb3QoKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICB0aGlzLmFydGljbGVMaXN0ID0gcmVzcG9uc2UuZGF0YS5zbGljZSgwLCA1KS5tYXAoKGl0ZW0pID0+IHsNCiAgICAgICAgICAgIGl0ZW0uY25UaXRsZSA9IGl0ZW0udGl0bGU7DQogICAgICAgICAgICBpdGVtLmlkID0gaXRlbS5hcnRpY2xlSWQ7DQogICAgICAgICAgICByZXR1cm4gaXRlbTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIGFzeW5jIGRldGFpbHMoKSB7DQogICAgICBsZXQgcGFyYW1zOw0KICAgICAgaWYgKHRoaXMuJHJvdXRlLnF1ZXJ5LmlkKSB7DQogICAgICAgIHBhcmFtcyA9IHsNCiAgICAgICAgICBpZDogdGhpcy4kcm91dGUucXVlcnkuaWQsDQogICAgICAgIH07DQogICAgICB9IGVsc2Ugew0KICAgICAgICBwYXJhbXMgPSB7IGFydGljbGVTbjogdGhpcy4kcm91dGUucXVlcnkuYXJ0aWNsZVNuIH07DQogICAgICB9DQogICAgICAvLyBhd2FpdCBBUEkuQXJlYUluZm8odGhpcy4kcm91dGUucXVlcnkuaWQgfHwgdGhpcy4kcm91dGUucXVlcnkuYXJ0aWNsZVNuKS50aGVuKGFzeW5jIChyZXMpID0+IHsNCiAgICAgIGF3YWl0IEFQSS5hcnRpY2xlRGV0YWlsKHBhcmFtcykudGhlbihhc3luYyAocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICB0aGlzLmhhbmxkZUJyb3dzZUFkZChyZXMuZGF0YS5pZCk7DQogICAgICAgICAgdGhpcy5kcmF3ZXJJbmZvID0gcmVzLmRhdGE7DQogICAgICAgICAgdGhpcy5kcmF3ZXJJbmZvLnNvdXJjZVR5cGUgIT0gIjEiICYmIHRoaXMuZHJhd2VySW5mby5zb3VyY2VUeXBlICE9ICIzIg0KICAgICAgICAgICAgPyAodGhpcy50cmFuc2xhdGlvbkJ0blNob3cgPSB0cnVlKQ0KICAgICAgICAgICAgOiAodGhpcy50cmFuc2xhdGlvbkJ0blNob3cgPSBmYWxzZSk7DQoNCiAgICAgICAgICAvLyDlpoLmnpzmmK/lvq7kv6Hmlofnq6DvvIzlhYjmmL7npLrliqDovb3nirbmgIENCiAgICAgICAgICBpZiAoDQogICAgICAgICAgICB0aGlzLmRyYXdlckluZm8ub3JpZ2luYWxVcmwgJiYNCiAgICAgICAgICAgIHRoaXMuZHJhd2VySW5mby5vcmlnaW5hbFVybC5pbmNsdWRlcygiaHR0cHM6Ly9tcC53ZWl4aW4ucXEuY29tIikNCiAgICAgICAgICApIHsNCiAgICAgICAgICAgIHRoaXMuY29udGVudFJlYWR5ID0gZmFsc2U7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuY29udGVudFJlYWR5ID0gdHJ1ZTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDpooTlpITnkIblhoXlrrkNCiAgICAgICAgICBjb25zdCByYXdDb250ZW50ID0NCiAgICAgICAgICAgIHRoaXMuZHJhd2VySW5mby5jbkNvbnRlbnQgfHwgdGhpcy5kcmF3ZXJJbmZvLmNvbnRlbnQ7DQogICAgICAgICAgbGV0IHByb2Nlc3NlZENvbnRlbnQgPSB0aGlzLmZvcm1hdHRpbmdKc29uKHJhd0NvbnRlbnQpOw0KDQogICAgICAgICAgLy8g5aSE55CGc3dkdOaVsOaNrg0KICAgICAgICAgIGxldCBrZXlQb2ludHMgPSAiIjsNCiAgICAgICAgICBsZXQgZW50aXRpZXMgPSAiIjsNCg0KICAgICAgICAgIGlmICh0aGlzLmRyYXdlckluZm8uc3dkdCAmJiBBcnJheS5pc0FycmF5KHRoaXMuZHJhd2VySW5mby5zd2R0KSkgew0KICAgICAgICAgICAgdGhpcy5kcmF3ZXJJbmZvLnN3ZHQuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgICAgICBpZiAoaXRlbS5zd2R0VGFza2lkID09PSAiMSIgJiYgaXRlbS5zd2R0Q29udGVudCkgew0KICAgICAgICAgICAgICAgIC8vIOWGheWuueimgeeCuQ0KICAgICAgICAgICAgICAgIGtleVBvaW50cyA9IGl0ZW0uc3dkdENvbnRlbnQ7DQogICAgICAgICAgICAgIH0gZWxzZSBpZiAoaXRlbS5zd2R0VGFza2lkID09PSAiMiIgJiYgaXRlbS5zd2R0Q29udGVudCkgew0KICAgICAgICAgICAgICAgIC8vIOS6uuWRmC/mnLrmnoQv5oqA5pyvL+S6p+WTgQ0KICAgICAgICAgICAgICAgIGVudGl0aWVzID0gaXRlbS5zd2R0Q29udGVudDsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g6aKE5aSE55CGIG1hcmtkb3duIOWGheWuue+8jOehruS/neaNouihjOespuato+ehruWkhOeQhg0KICAgICAgICAgIGNvbnN0IHByZXByb2Nlc3NNYXJrZG93biA9ICh0ZXh0KSA9PiB7DQogICAgICAgICAgICBpZiAoIXRleHQpIHJldHVybiAiIjsNCiAgICAgICAgICAgIC8vIOWkhOeQhuWQhOenjei9rOS5ieWtl+espuWSjOagvOW8j+mXrumimA0KICAgICAgICAgICAgcmV0dXJuIHRleHQNCiAgICAgICAgICAgICAgLnJlcGxhY2UoL2BgYG1hcmtkb3duXHMqfGBgYFxzKi9nLCAiIikgLy8g56e76ZmkIG1hcmtkb3duIOS7o+eggeWdl+agh+iusA0KICAgICAgICAgICAgICAucmVwbGFjZSgvYGBgW2EtekEtWl0qXHMqL2csICIiKSAvLyDnp7vpmaTluKbmnInor63oqIDmoIforrDnmoTku6PnoIHlnZfmoIforrANCiAgICAgICAgICAgICAgLnJlcGxhY2UoL1xcbi9nLCAiXG4iKSAvLyDlpITnkIblj4zlj43mlpzmnaDmjaLooYznrKYNCiAgICAgICAgICAgICAgLnJlcGxhY2UoL1xcXG4vZywgIlxuIikgLy8g5aSE55CG5LiJ5Y+N5pac5p2g5o2i6KGM56ymDQogICAgICAgICAgICAgIC5yZXBsYWNlKC9cclxuL2csICJcbiIpIC8vIOWkhOeQhiBXaW5kb3dzIOmjjuagvOaNouihjOespg0KICAgICAgICAgICAgICAucmVwbGFjZSgvXCRce1tefV0rXH0vZywgIiIpOyAvLyDnp7vpmaTmqKHmnb/lrZfnrKbkuLLljaDkvY3nrKYNCiAgICAgICAgICB9Ow0KDQogICAgICAgICAgLy8g6K6+572uIG1hcmtkb3duIOWGheWuue+8jOS9v+eUqOmihOWkhOeQhuWHveaVsA0KICAgICAgICAgIHRoaXMubWFya2Rvd25Db250ZW50LmtleVBvaW50cyA9IHByZXByb2Nlc3NNYXJrZG93bihrZXlQb2ludHMpOw0KICAgICAgICAgIHRoaXMubWFya2Rvd25Db250ZW50LmVudGl0aWVzID0gcHJlcHJvY2Vzc01hcmtkb3duKGVudGl0aWVzKTsNCg0KICAgICAgICAgIC8vIOWmguaenOaYr+W+ruS/oeWFrOS8l+WPt+aWh+eroO+8jOi/m+ihjOmihOWKoOi9veWkhOeQhg0KICAgICAgICAgIGlmICgNCiAgICAgICAgICAgIHRoaXMuZHJhd2VySW5mby5vcmlnaW5hbFVybCAmJg0KICAgICAgICAgICAgdGhpcy5kcmF3ZXJJbmZvLm9yaWdpbmFsVXJsLmluY2x1ZGVzKCJodHRwczovL21wLndlaXhpbi5xcS5jb20iKQ0KICAgICAgICAgICkgew0KICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgLy8g6aKE5Yqg6L295b6u5L+h5Zu+54mHIC0g5LiN5Lya6YCg5oiQ6Zeq54OBDQogICAgICAgICAgICAgIHByb2Nlc3NlZENvbnRlbnQgPSBhd2FpdCB0aGlzLnByZWxvYWRXZWNoYXRJbWFnZXMoDQogICAgICAgICAgICAgICAgcHJvY2Vzc2VkQ29udGVudA0KICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgICBjb25zb2xlLmxvZygi5b6u5L+h5Zu+54mH6aKE5aSE55CG5a6M5oiQIik7DQogICAgICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuW+ruS/oeWbvueJh+mihOWkhOeQhuWksei0pToiLCBlKTsNCiAgICAgICAgICAgICAgLy8g5Ye66ZSZ5pe277yM5pi+56S65YaF5a65DQogICAgICAgICAgICAgIHRoaXMuY29udGVudFJlYWR5ID0gdHJ1ZTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDorr7nva7lpITnkIblkI7nmoTlhoXlrrkNCiAgICAgICAgICB0aGlzLiRzZXQodGhpcywgImh0bWxKc29uIiwgcHJvY2Vzc2VkQ29udGVudCk7DQoNCiAgICAgICAgICBpZiAodGhpcy5kcmF3ZXJJbmZvLnRpdGxlID09IHRoaXMuZHJhd2VySW5mby5jblRpdGxlKSB7DQogICAgICAgICAgICB0aGlzLnRpdGxlU2hvdyA9IGZhbHNlOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGRvY3VtZW50LnRpdGxlID0gdGhpcy5kcmF3ZXJJbmZvLmNuVGl0bGUgfHwgdGhpcy5kcmF3ZXJJbmZvLnRpdGxlOw0KICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KDQogICAgICAgICAgLy8g5YaF5a655pi+56S65ZCO77yM5bqU55So5YWo5bGA5L+u5aSNDQogICAgICAgICAgaWYgKHRoaXMuaXNXZWl4aW5BcnRpY2xlKSB7DQogICAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICAgIC8vIOW6lOeUqOWbvueJh+S/ruWkjQ0KICAgICAgICAgICAgICB0aGlzLmZpeFdlY2hhdEltYWdlcygpOw0KDQogICAgICAgICAgICAgIC8vIDLnp5LlkI7lho3mo4Dmn6XkuIDmrKENCiAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgICAgICAgdGhpcy5maXhXZWNoYXRJbWFnZXMoKTsNCiAgICAgICAgICAgICAgfSwgMjAwMCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgZm9ybWF0dGluZ0pzb24oY29udGVudCkgew0KICAgICAgaWYgKGNvbnRlbnQpIHsNCiAgICAgICAgaWYgKGNvbnRhaW5zSHRtbFRhZ3MoY29udGVudCkpIHsNCiAgICAgICAgICBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKC88YnI+L2csICIiKTsNCiAgICAgICAgICBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKC9cbi9nLCAiIik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvXFxuL2csICIiKTsNCiAgICAgICAgICBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKC9cXFxuL2csICIiKTsNCiAgICAgICAgICBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKCJ8eGEwIiwgIiIpOw0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoIm9wYWNpdHk6IDAiLCAiIik7DQoNCiAgICAgICAgICAvLyDlpITnkIblvq7kv6HlhazkvJflj7flm77niYfpmLLnm5fpk77pl67popgNCiAgICAgICAgICBpZiAoDQogICAgICAgICAgICB0aGlzLmRyYXdlckluZm8ub3JpZ2luYWxVcmwgJiYNCiAgICAgICAgICAgIHRoaXMuZHJhd2VySW5mby5vcmlnaW5hbFVybC5pbmNsdWRlcygiaHR0cHM6Ly9tcC53ZWl4aW4ucXEuY29tIikNCiAgICAgICAgICApIHsNCiAgICAgICAgICAgIGNvbnRlbnQgPSB0aGlzLmhhbmRsZVdlY2hhdEltYWdlcyhjb250ZW50KTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDlpITnkIbnm7jlr7not6/lvoTlm77niYcNCiAgICAgICAgICBjb250ZW50ID0gdGhpcy5oYW5kbGVSZWxhdGl2ZUltYWdlUGF0aHMoY29udGVudCk7DQoNCiAgICAgICAgICBjb25zb2xlLmxvZygi5YyF5ZCr55qESFRNTOagh+etviIsIGV4dHJhY3RIdG1sVGFncyhjb250ZW50KSk7DQogICAgICAgICAgY29uc29sZS5sb2coIkhUTUzmmK/lkKbnu5PmnoTmraPnoa4iLCBoYXNWYWxpZEh0bWxTdHJ1Y3R1cmUoY29udGVudCkpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoL1xuL2csICI8YnI+Iik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvXFxuL2csICI8YnI+Iik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvXFxcbi9nLCAiPGJyPiIpOw0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoL1wke1tefV0rfS9nLCAiPGJyPiIpOw0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoInx4YTAiLCAiIik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgib3BhY2l0eTogMCIsICIiKTsNCg0KICAgICAgICAgIC8vIOWkhOeQhuW+ruS/oeWFrOS8l+WPt+WbvueJh+mYsuebl+mTvumXrumimA0KICAgICAgICAgIGlmICgNCiAgICAgICAgICAgIHRoaXMuZHJhd2VySW5mby5vcmlnaW5hbFVybCAmJg0KICAgICAgICAgICAgdGhpcy5kcmF3ZXJJbmZvLm9yaWdpbmFsVXJsLmluY2x1ZGVzKCJodHRwczovL21wLndlaXhpbi5xcS5jb20iKQ0KICAgICAgICAgICkgew0KICAgICAgICAgICAgY29udGVudCA9IHRoaXMuaGFuZGxlV2VjaGF0SW1hZ2VzKGNvbnRlbnQpOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOWkhOeQhuebuOWvuei3r+W+hOWbvueJhw0KICAgICAgICAgIGNvbnRlbnQgPSB0aGlzLmhhbmRsZVJlbGF0aXZlSW1hZ2VQYXRocyhjb250ZW50KTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGNvbnRlbnQ7DQogICAgfSwNCiAgICAvLyDnv7vor5Hmlofnq6ANCiAgICB0cmFuc2xhdGVFdmVudChyb3cpIHsNCiAgICAgIGNvbnN0IGZ1biA9ICgpID0+IHsNCiAgICAgICAgY29uc3QgbG9hZGluZyA9IHRoaXMuJGxvYWRpbmcoew0KICAgICAgICAgIGxvY2s6IHRydWUsDQogICAgICAgICAgdGV4dDogIkxvYWRpbmciLA0KICAgICAgICAgIHNwaW5uZXI6ICJlbC1pY29uLWxvYWRpbmciLA0KICAgICAgICAgIGJhY2tncm91bmQ6ICJyZ2JhKDAsIDAsIDAsIDAuNykiLA0KICAgICAgICB9KTsNCiAgICAgICAgQVBJLnRyYW5zbGF0aW9uVGl0bGUoew0KICAgICAgICAgIG9yaWdpbmFsVGV4dDogcm93LmNvbnRlbnQsDQogICAgICAgICAgZG9jSWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LmlkLA0KICAgICAgICAgIGlkOiByb3cuaWQsDQogICAgICAgICAgdHJhbnNsYXRpb25GaWVsZDogImNvbnRlbnQiLA0KICAgICAgICAgIHRyYW5zbGF0aW9uVHlwZTogMSwNCiAgICAgICAgfSkNCiAgICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICB0aGlzLmRyYXdlckluZm8uY25Db250ZW50ID0gcmVzLmRhdGE7DQogICAgICAgICAgICB0aGlzLiRzZXQoDQogICAgICAgICAgICAgIHRoaXMsDQogICAgICAgICAgICAgICJodG1sSnNvbiIsDQogICAgICAgICAgICAgIHRoaXMuZm9ybWF0dGluZ0pzb24oDQogICAgICAgICAgICAgICAgdGhpcy5kcmF3ZXJJbmZvLmNuQ29udGVudCB8fCB0aGlzLmRyYXdlckluZm8uY29udGVudA0KICAgICAgICAgICAgICApDQogICAgICAgICAgICApOw0KICAgICAgICAgICAgdGhpcy5vcmlnaW5hbEFydGljbGVTaG93ID0gdHJ1ZTsNCiAgICAgICAgICAgIGxvYWRpbmcuY2xvc2UoKTsNCiAgICAgICAgICB9KQ0KICAgICAgICAgIC5jYXRjaCgoZXJyKSA9PiB7DQogICAgICAgICAgICBsb2FkaW5nLmNsb3NlKCk7DQogICAgICAgICAgfSk7DQogICAgICB9Ow0KDQogICAgICBpZiAocm93LmNuQ29udGVudCkgew0KICAgICAgICAvLyDmj5DnpLrmmK/lkKbnoa7orqTlho3mrKHnv7vor5ENCiAgICAgICAgdGhpcy4kY29uZmlybSgi5piv5ZCm56Gu6K6k5YaN5qyh57+76K+RPyIsICLmj5DnpLoiLCB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgfSkNCiAgICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgICBmdW4oKTsNCiAgICAgICAgICB9KQ0KICAgICAgICAgIC5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgICB0aGlzLiRzZXQoDQogICAgICAgICAgICAgIHRoaXMsDQogICAgICAgICAgICAgICJodG1sSnNvbiIsDQogICAgICAgICAgICAgIHRoaXMuZm9ybWF0dGluZ0pzb24oDQogICAgICAgICAgICAgICAgdGhpcy5kcmF3ZXJJbmZvLmNuQ29udGVudCB8fCB0aGlzLmRyYXdlckluZm8uY29udGVudA0KICAgICAgICAgICAgICApDQogICAgICAgICAgICApOw0KICAgICAgICAgICAgdGhpcy5vcmlnaW5hbEFydGljbGVTaG93ID0gdHJ1ZTsNCiAgICAgICAgICB9KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGZ1bigpOw0KICAgICAgfQ0KICAgIH0sDQogICAgdmlld09yaWdpbmFsKCkgew0KICAgICAgaWYgKHRoaXMub3JpZ2luYWxBcnRpY2xlU2hvdykgew0KICAgICAgICB0aGlzLiRzZXQoDQogICAgICAgICAgdGhpcywNCiAgICAgICAgICAiaHRtbEpzb24iLA0KICAgICAgICAgIHRoaXMuZm9ybWF0dGluZ0pzb24odGhpcy5kcmF3ZXJJbmZvLmNvbnRlbnQpDQogICAgICAgICk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRzZXQoDQogICAgICAgICAgdGhpcywNCiAgICAgICAgICAiaHRtbEpzb24iLA0KICAgICAgICAgIHRoaXMuZm9ybWF0dGluZ0pzb24oDQogICAgICAgICAgICB0aGlzLmRyYXdlckluZm8uY25Db250ZW50IHx8IHRoaXMuZHJhd2VySW5mby5jb250ZW50DQogICAgICAgICAgKQ0KICAgICAgICApOw0KICAgICAgfQ0KICAgICAgdGhpcy5vcmlnaW5hbEFydGljbGVTaG93ID0gIXRoaXMub3JpZ2luYWxBcnRpY2xlU2hvdzsNCiAgICB9LA0KICAgIGJhY2tUb1RvcCgpIHsNCiAgICAgIHdpbmRvdy5zY3JvbGxUbyh7IHRvcDogMCwgYmVoYXZpb3I6ICJzbW9vdGgiIH0pOw0KICAgIH0sDQogICAgY2xvc2UoKSB7DQogICAgICB0aGlzLnR5cGUgPSB0cnVlOw0KICAgICAgdGhpcy50ZXh0YXJlYSA9ICIiOw0KICAgIH0sDQogICAgc3VibWl0KCkgew0KICAgICAgZmVlZGJhY2tBZGQoew0KICAgICAgICBhcnRpY2xlSWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LmlkLA0KICAgICAgICBkb2NJZDogdGhpcy4kcm91dGUucXVlcnkuaWQsDQogICAgICAgIGNuVGl0bGU6IHRoaXMuZHJhd2VySW5mby5jblRpdGxlIHx8IHRoaXMuZHJhd2VySW5mby50aXRsZSwNCiAgICAgICAgY29udGVudDogdGhpcy50ZXh0YXJlYSwNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiAi55WZ6KiA5oiQ5YqfIiwNCiAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwNCiAgICAgICAgICB9KTsNCiAgICAgICAgICB0aGlzLmNsb3NlKCk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgb3Blbk5ld1ZpZXcoaXRlbSkgew0KICAgICAgd2luZG93Lm9wZW4oYC9leHByZXNzRGV0YWlscz9pZD0ke2l0ZW0uaWR9JmRvY0lkPSR7aXRlbS5pZH1gLCAiX2JsYW5rIik7DQogICAgfSwNCiAgICBoYW5kbGVDb2xsZWN0aW9ucyhpdGVtKSB7DQogICAgICBpZiAoIWl0ZW0uY29sbGVjdGlvbikgew0KICAgICAgICBBUEkuY29sbGVjdEFwaShbaXRlbS5pZF0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiAi5pS26JeP5oiQ5YqfLOivt+WJjeW+gOS4quS6uuS4reW/g+afpeeciyIsDQogICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZHJhd2VySW5mbywgImNvbGxlY3Rpb24iLCAhaXRlbS5jb2xsZWN0aW9uKTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLmlLbol4/lpLHotKUiLCB0eXBlOiAiaW5mbyIgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIEFQSS5jb2NlbENvbGxlY3QoW2l0ZW0uaWRdKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuW3suWPlua2iOaUtuiXjyIsIHR5cGU6ICJzdWNjZXNzIiB9KTsNCiAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmRyYXdlckluZm8sICJjb2xsZWN0aW9uIiwgIWl0ZW0uY29sbGVjdGlvbik7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi5Y+W5raI5pS26JeP5aSx6LSlIiwgdHlwZTogImluZm8iIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVSZWNvbW1lbmQoaXRlbSkgew0KICAgICAgbGV0IHF1ZXJ5ID0gbmV3IEZvcm1EYXRhKCk7DQogICAgICBxdWVyeS5hcHBlbmQoImFydGljbGVJZCIsIGl0ZW0uaWQpOw0KICAgICAgaWYgKCFpdGVtLnJlY29tbWVuZCkgew0KICAgICAgICBBUEkucmVjb21tZW5kQWRkKHF1ZXJ5KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogIuaOqOiNkOaIkOWKnyzor7fliY3lvoDkuKrkurrkuK3lv4Pmn6XnnIsiLA0KICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsDQogICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmRyYXdlckluZm8sICJyZWNvbW1lbmQiLCAhaXRlbS5yZWNvbW1lbmQpOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuaOqOiNkOWksei0pSIsIHR5cGU6ICJpbmZvIiB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgQVBJLnJlY29tbWVuZENhbmNlbChxdWVyeSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLlt7Llj5bmtojmjqjojZAiLCB0eXBlOiAic3VjY2VzcyIgfSk7DQogICAgICAgICAgICB0aGlzLiRzZXQodGhpcy5kcmF3ZXJJbmZvLCAicmVjb21tZW5kIiwgIWl0ZW0ucmVjb21tZW5kKTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLlj5bmtojmjqjojZDlpLHotKUiLCB0eXBlOiAiaW5mbyIgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmxkZUJyb3dzZUFkZChpZCkgew0KICAgICAgbGV0IHF1ZXJ5ID0gbmV3IEZvcm1EYXRhKCk7DQogICAgICBxdWVyeS5hcHBlbmQoImFydGljbGVJZCIsIGlkKTsNCiAgICAgIEFQSS5icm93c2VBZGQocXVlcnkpOw0KICAgIH0sDQogICAgdmlld09yaWdpbmFsQXJ0aWNsZShpdGVtKSB7DQogICAgICB3aW5kb3cub3BlbihpdGVtLm9yaWdpbmFsVXJsKTsNCiAgICB9LA0KICAgIC8vIOaJk+W8gOaAnee7tOWvvOWbvuW8ueeqlw0KICAgIG9wZW5NYXJrbWFwKHR5cGUpIHsNCiAgICAgIGlmICghdGhpcy5tYXJrZG93bkNvbnRlbnRbdHlwZV0pIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLmmoLml6DmgJ3nu7Tlr7zlm77mlbDmja4iKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDorr7nva7moIfpopjlkozlhoXlrrkNCiAgICAgIGxldCB0aXRsZSA9ICIiOw0KICAgICAgaWYgKHR5cGUgPT09ICJrZXlQb2ludHMiKSB7DQogICAgICAgIHRpdGxlID0gIuWGheWuueimgeeCueaAnee7tOWvvOWbviI7DQogICAgICB9IGVsc2UgaWYgKHR5cGUgPT09ICJlbnRpdGllcyIpIHsNCiAgICAgICAgdGl0bGUgPSAi5Lq65ZGYL+acuuaehC/mioDmnK8v5Lqn5ZOB5oCd57u05a+85Zu+IjsNCiAgICAgIH0NCg0KICAgICAgLy8g6K6+572u5YaF5a65DQogICAgICBjb25zdCBjb250ZW50ID0gdGhpcy5tYXJrZG93bkNvbnRlbnRbdHlwZV07DQoNCiAgICAgIC8vIOS9v+eUqGxvY2FsU3RvcmFnZeWtmOWCqOWkp+Wei+WGheWuue+8jOiAjOS4jeaYr+mAmui/h1VSTOS8oOmAkg0KICAgICAgY29uc3Qgc3RvcmFnZUtleSA9IGBtYXJrbWFwX2RhdGFfJHtEYXRlLm5vdygpfWA7DQogICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShzdG9yYWdlS2V5LCBjb250ZW50KTsNCg0KICAgICAgLy8g5p6E5bu6VVJM5Y+C5pWwIC0g5Y+q5Lyg6YCS5qCH6aKY5ZKM5a2Y5YKo6ZSu77yM5LiN5Lyg6YCS5aSn5Z6L5YaF5a65DQogICAgICBjb25zdCBwYXJhbXMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKCk7DQogICAgICBwYXJhbXMuYXBwZW5kKCJ0aXRsZSIsIHRpdGxlKTsNCiAgICAgIHBhcmFtcy5hcHBlbmQoDQogICAgICAgICJhcnRpY2xlVGl0bGUiLA0KICAgICAgICB0aGlzLmRyYXdlckluZm8uY25UaXRsZSB8fCB0aGlzLmRyYXdlckluZm8udGl0bGUNCiAgICAgICk7DQogICAgICBwYXJhbXMuYXBwZW5kKCJzdG9yYWdlS2V5Iiwgc3RvcmFnZUtleSk7DQoNCiAgICAgIC8vIOiOt+WPluWxj+W5leWwuuWvuO+8jOWItuWumuWQiOmAgueahOeql+WPo+Wkp+Wwjw0KICAgICAgY29uc3Qgc2NyZWVuV2lkdGggPSB3aW5kb3cuc2NyZWVuLndpZHRoOw0KICAgICAgY29uc3Qgc2NyZWVuSGVpZ2h0ID0gd2luZG93LnNjcmVlbi5oZWlnaHQ7DQoNCiAgICAgIC8vIOeql+WPo+WuvemrmOS4uuWxj+W5leeahDgwJe+8jOW5tuWxheS4reaYvuekug0KICAgICAgY29uc3Qgd2lkdGggPSBNYXRoLnJvdW5kKHNjcmVlbldpZHRoICogMC44KTsNCiAgICAgIGNvbnN0IGhlaWdodCA9IE1hdGgucm91bmQoc2NyZWVuSGVpZ2h0ICogMC44KTsNCiAgICAgIGNvbnN0IGxlZnQgPSBNYXRoLnJvdW5kKChzY3JlZW5XaWR0aCAtIHdpZHRoKSAvIDIpOw0KICAgICAgY29uc3QgdG9wID0gTWF0aC5yb3VuZCgoc2NyZWVuSGVpZ2h0IC0gaGVpZ2h0KSAvIDIpOw0KDQogICAgICAvLyDlsJ3or5XmiZPlvIDmlrDnqpflj6MNCiAgICAgIGNvbnN0IHdpbmRvd0ZlYXR1cmVzID0gYHdpZHRoPSR7d2lkdGh9LGhlaWdodD0ke2hlaWdodH0sbGVmdD0ke2xlZnR9LHRvcD0ke3RvcH0scmVzaXphYmxlPXllcyxzY3JvbGxiYXJzPXllcyxzdGF0dXM9eWVzYDsNCiAgICAgIGNvbnN0IG5ld1dpbmRvdyA9IHdpbmRvdy5vcGVuKA0KICAgICAgICBgL21hcmttYXA/JHtwYXJhbXMudG9TdHJpbmcoKX1gLA0KICAgICAgICAiX2JsYW5rIiwNCiAgICAgICAgd2luZG93RmVhdHVyZXMNCiAgICAgICk7DQoNCiAgICAgIC8vIOajgOafpeaYr+WQpuaIkOWKn+aJk+W8gOeql+WPo++8jOWmguaenOiiq+a1j+iniOWZqOmYu+atouWImeaJk+W8gOaWsOagh+etvumhtQ0KICAgICAgaWYgKA0KICAgICAgICAhbmV3V2luZG93IHx8DQogICAgICAgIG5ld1dpbmRvdy5jbG9zZWQgfHwNCiAgICAgICAgdHlwZW9mIG5ld1dpbmRvdy5jbG9zZWQgPT09ICJ1bmRlZmluZWQiDQogICAgICApIHsNCiAgICAgICAgLy8g5rWP6KeI5Zmo5Y+v6IO96Zi75q2i5LqG5by556qX77yM5L2/55So5paw5qCH562+6aG1DQogICAgICAgIHdpbmRvdy5vcGVuKGAvbWFya21hcD8ke3BhcmFtcy50b1N0cmluZygpfWAsICJfYmxhbmsiKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCLmlrDnqpflj6PooqvmtY/op4jlmajpmLvmraLvvIzlt7LlnKjmlrDmoIfnrb7pobXmiZPlvIAiKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOWFs+mXreaAnee7tOWvvOWbvuW8ueeqlyAtIOS/neeVmeS7pemYsumcgOimgQ0KICAgIGhhbmRsZU1hcmttYXBDbG9zZSgpIHsNCiAgICAgIHRoaXMubWFya21hcFZpc2libGUgPSBmYWxzZTsNCiAgICAgIHRoaXMubWFya21hcENvbnRlbnQgPSAiIjsNCiAgICB9LA0KICAgIC8vIOWkhOeQhuWPkeW4g+aXtumXtOeahOaYvuekug0KICAgIGZvcm1hdFB1Ymxpc2hUaW1lKHB1Ymxpc2hUaW1lLCBwdWJsaXNoTG9jYWx0aW1lKSB7DQogICAgICAvLyDmoLzlvI/ljJZwdWJsaXNoVGltZeS4uuW5tOaciOaXpQ0KICAgICAgY29uc3QgZm9ybWF0dGVkUHVibGlzaFRpbWUgPSB0aGlzLnBhcnNlVGltZShwdWJsaXNoVGltZSwgInt5fS17bX0te2R9Iik7DQoNCiAgICAgIC8vIOWmguaenHdlYnN0ZVB1Ymxpc2hUaW1l5LiN5a2Y5Zyo77yM55u05o6l6L+U5ZuecHVibGlzaFRpbWUNCiAgICAgIGlmICghcHVibGlzaExvY2FsdGltZSkgew0KICAgICAgICByZXR1cm4gIlvljJfkuqxdIiArIGZvcm1hdHRlZFB1Ymxpc2hUaW1lOw0KICAgICAgfQ0KDQogICAgICBsZXQgZm9ybWF0dGVkV2Vic3RlVGltZSA9ICIiOw0KICAgICAgLy8g5aSE55CG5LiN5ZCM5qC85byP55qEd2Vic3RlUHVibGlzaFRpbWUNCiAgICAgIGlmIChwdWJsaXNoTG9jYWx0aW1lKSB7DQogICAgICAgIGZvcm1hdHRlZFdlYnN0ZVRpbWUgPSB0aGlzLnBhcnNlVGltZShwdWJsaXNoTG9jYWx0aW1lLCAie3l9LXttfS17ZH0iKTsNCiAgICAgIH0NCg0KICAgICAgLy8g5q+U6L6D5bm05pyI5pel5piv5ZCm55u45ZCMDQogICAgICBpZiAoZm9ybWF0dGVkUHVibGlzaFRpbWUgPT09IGZvcm1hdHRlZFdlYnN0ZVRpbWUpIHsNCiAgICAgICAgcmV0dXJuICJb5YyX5LqsXSIgKyBmb3JtYXR0ZWRQdWJsaXNoVGltZTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiBgW+W9k+WcsF0ke2Zvcm1hdHRlZFdlYnN0ZVRpbWV9IC8gW+WMl+S6rF0ke2Zvcm1hdHRlZFB1Ymxpc2hUaW1lfWA7DQogICAgICB9DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["index2.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAi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file": "index2.vue", "sourceRoot": "src/views/expressDetails", "sourcesContent": ["<template>\r\n  <div class=\"drawer_box\" v-loading=\"loading\">\r\n    <div\r\n      class=\"drawer_Style\"\r\n      :style=\"{ marginBottom: type ? '40px' : '100px' }\"\r\n    >\r\n      <div>\r\n        <!--p class=\"title\">\r\n          {{ drawerInfo.cnTitle || drawerInfo.title }}\r\n        </p-->\r\n        <p class=\"title\">\r\n          {{ drawerInfo.cnTitle }}\r\n        </p>\r\n        <p class=\"title\" v-if=\"titleShow\">\r\n          {{ drawerInfo.title }}\r\n        </p>\r\n        <p>\r\n          <span class=\"source\">{{ drawerInfo.sourceName }}</span>\r\n          <span class=\"time\">{{\r\n            formatPublishTime(\r\n              drawerInfo.publishTime,\r\n              drawerInfo.publishLocaltime\r\n            )\r\n          }}</span>\r\n          <span class=\"author\">{{ drawerInfo.author }}</span>\r\n        </p>\r\n        <el-divider></el-divider>\r\n        <div class=\"summary\" v-if=\"drawerInfo.cnSummary || drawerInfo.summary\">\r\n          <div class=\"summary-item1\">\r\n            小信导读：<span style=\"color: #838484\">【内容由大模型生成】</span>\r\n          </div>\r\n          <div class=\"summary-item2\">\r\n            <text-ellipsis\r\n              :text=\"drawerInfo.cnSummary || drawerInfo.summary\"\r\n              :max-lines=\"10\"\r\n              more-text=\"展开\"\r\n              less-text=\"收起\"\r\n            ></text-ellipsis>\r\n          </div>\r\n        </div>\r\n        <!-- 内容区域 -->\r\n        <div v-if=\"!contentReady && isWeixinArticle\" class=\"content-loading\">\r\n          <el-alert\r\n            title=\"正在处理微信图片，请稍等...\"\r\n            type=\"info\"\r\n            center\r\n            :closable=\"false\"\r\n            show-icon\r\n          ></el-alert>\r\n          <div v-if=\"totalImages > 0\" class=\"loading-progress\">\r\n            <div class=\"loading-text\">\r\n              图片加载进度: {{ loadedImages }}/{{ totalImages }}\r\n            </div>\r\n            <el-progress\r\n              :percentage=\"Math.floor((loadedImages / totalImages) * 100)\"\r\n              :show-text=\"false\"\r\n              status=\"success\"\r\n            ></el-progress>\r\n          </div>\r\n        </div>\r\n        <div\r\n          v-else\r\n          style=\"line-height: 30px; white-space: normal; word-break: break-word\"\r\n          v-html=\"htmlJson\"\r\n        ></div>\r\n        <el-empty description=\"当前文章暂无数据\" v-if=\"!htmlJson\"></el-empty>\r\n      </div>\r\n      <div class=\"liuyanBox\" :style=\"{ height: type ? '50px' : '110px' }\">\r\n        <div class=\"morenzhuangtai\" v-if=\"type\">\r\n          <div class=\"uesr\">\r\n            <img :src=\"avatar\" class=\"avatar\" />\r\n            <div class=\"name\">{{ name }}</div>\r\n          </div>\r\n          <div class=\"button\">\r\n            <el-tooltip\r\n              class=\"item\"\r\n              effect=\"dark\"\r\n              :content=\"drawerInfo.recommend ? '取消推荐' : '推荐'\"\r\n              placement=\"top\"\r\n            >\r\n              <el-button\r\n                :icon=\"\r\n                  drawerInfo.recommend\r\n                    ? 'el-icon-message-solid'\r\n                    : 'el-icon-bell'\r\n                \"\r\n                :type=\"drawerInfo.recommend ? 'primary' : ''\"\r\n                circle\r\n                @click=\"handleRecommend(drawerInfo)\"\r\n              ></el-button>\r\n            </el-tooltip>\r\n            <el-tooltip\r\n              class=\"item\"\r\n              effect=\"dark\"\r\n              :content=\"drawerInfo.collection ? '取消收藏' : '收藏'\"\r\n              placement=\"top\"\r\n            >\r\n              <el-button\r\n                :icon=\"\r\n                  drawerInfo.collection ? 'el-icon-star-on' : 'el-icon-star-off'\r\n                \"\r\n                :type=\"drawerInfo.collection ? 'primary' : ''\"\r\n                circle\r\n                @click=\"handleCollections(drawerInfo)\"\r\n              ></el-button>\r\n            </el-tooltip>\r\n            <el-button type=\"text\" icon=\"el-icon-edit\" @click=\"type = false\"\r\n              >写留言</el-button\r\n            >\r\n          </div>\r\n        </div>\r\n        <div class=\"shuruzhuangtai\" v-else>\r\n          <div class=\"top\">\r\n            <div class=\"uesr\">\r\n              <img :src=\"avatar\" class=\"avatar\" />\r\n              <div class=\"name\">{{ name }}</div>\r\n            </div>\r\n            <div class=\"button\">\r\n              <el-button size=\"mini\" type=\"primary\" @click=\"submit\"\r\n                >确定</el-button\r\n              >\r\n              <el-button size=\"mini\" type=\"info\" @click=\"close\">取消</el-button>\r\n            </div>\r\n          </div>\r\n          <div class=\"bottom\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              placeholder=\"请输入内容\"\r\n              v-model=\"textarea\"\r\n              maxlength=\"300\"\r\n              show-word-limit\r\n              resize=\"none\"\r\n            ></el-input>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"right\">\r\n      <div class=\"sisterDetails\">\r\n        <div class=\"right_title\">媒体详情</div>\r\n        <el-divider class=\"el-divider-right\"></el-divider>\r\n        <div class=\"media-info\">\r\n          <div class=\"media-info-item\">\r\n            <div class=\"name\">\r\n              媒体名称:<span class=\"value\">{{ drawerInfo.sourceName }}</span>\r\n            </div>\r\n            <div class=\"name\">媒体等级:<span class=\"value\"></span></div>\r\n          </div>\r\n          <div class=\"media-info-item\">\r\n            <div class=\"name\">媒体地域:<span class=\"value\"></span></div>\r\n            <div class=\"name\">媒体行业:<span class=\"value\"></span></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div\r\n        class=\"markdown\"\r\n        v-if=\"keyPointsFormatted || entitiesFormatted\"\r\n        :style=\"{\r\n          height: keyPointsFormatted && entitiesFormatted ? '810px' : '430px',\r\n        }\"\r\n      >\r\n        <div class=\"right_title\">\r\n          <span style=\"color: #971231\">小信解读</span>\r\n          <span style=\"color: #838484\">【内容由大模型生成】</span>\r\n        </div>\r\n        <el-divider class=\"el-divider-markdown\"></el-divider>\r\n        <div\r\n          class=\"markdown-content\"\r\n          style=\"margin-top: 10px\"\r\n          v-if=\"keyPointsFormatted\"\r\n        >\r\n          <div class=\"markdown-content-title\">\r\n            内容要点\r\n            <span class=\"view-markmap\" @click=\"openMarkmap('keyPoints')\"\r\n              >点击放大查看</span\r\n            >\r\n          </div>\r\n          <div\r\n            class=\"markdown-content-text\"\r\n            v-html=\"keyPointsFormatted\"\r\n            style=\"background-color: #e8d9cc\"\r\n          ></div>\r\n        </div>\r\n        <div\r\n          class=\"markdown-content\"\r\n          :style=\"{ marginTop: keyPointsFormatted ? '20px' : '10px' }\"\r\n          v-if=\"entitiesFormatted\"\r\n        >\r\n          <div class=\"markdown-content-title\">\r\n            人员/机构/技术/产品\r\n            <span class=\"view-markmap\" @click=\"openMarkmap('entities')\"\r\n              >点击放大查看</span\r\n            >\r\n          </div>\r\n          <div\r\n            class=\"markdown-content-text\"\r\n            v-html=\"entitiesFormatted\"\r\n            style=\"background-color: #dce4d4\"\r\n          ></div>\r\n        </div>\r\n      </div>\r\n      <div class=\"recommendedArticle\">\r\n        <div class=\"right_title\">推荐文章</div>\r\n        <el-divider class=\"el-divider-right\"></el-divider>\r\n        <div class=\"articleBox\" v-for=\"item in articleList\" :key=\"item.id\">\r\n          <div class=\"article\" @click=\"openNewView(item)\">\r\n            {{ item.cnTitle }}\r\n          </div>\r\n          <div class=\"bottom\">\r\n            <div class=\"time\">{{ item.publishTime }}</div>\r\n            <div class=\"sourceName\">{{ item.sourceName }}</div>\r\n            <span class=\"count\">{{ `推荐数量：${item.count}` }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- <div class=\"keyword\">\r\n        <div class=\"right_title\">关键词</div>\r\n        <el-divider class=\"el-divider-right\"></el-divider>\r\n        <div class=\"chartBox\">\r\n          <el-image style=\"width: 100%; height: 290px\" :src=\"url\">\r\n          </el-image>\r\n        </div>\r\n      </div> -->\r\n      <!-- <div class=\"articleEntity\">\r\n        <div class=\"right_title\">文章实体</div>\r\n        <el-divider class=\"el-divider-right\"></el-divider>\r\n        <div class=\"chartBox\">\r\n          <el-radio-group v-model=\"tabPosition\" style=\"margin-bottom: 30px; position: absolute;   z-index: 3;\">\r\n            <el-radio-button label=\"1\">通用</el-radio-button>\r\n            <el-radio-button label=\"2\">人物</el-radio-button>\r\n            <el-radio-button label=\"3\">地域</el-radio-button>\r\n            <el-radio-button label=\"4\">机构</el-radio-button>\r\n          </el-radio-group>\r\n          <el-image style=\"width: 100%; height: 290px\" :src=\"url\">\r\n          </el-image>\r\n        </div>\r\n      </div> -->\r\n    </div>\r\n    <div class=\"tabs-all\">\r\n      <div class=\"tabs\" v-if=\"translationBtnShow\">\r\n        <span @click=\"viewOriginal()\">{{\r\n          this.originalArticleShow ? \"查看原文\" : \"查看中文\"\r\n        }}</span>\r\n      </div>\r\n      <div class=\"tabs\">\r\n        <span @click=\"viewOriginalArticle(drawerInfo)\">原文链接</span>\r\n      </div>\r\n      <div\r\n        class=\"tabs\"\r\n        v-hasPermi=\"['article:articleList:monitoringAndSpecial']\"\r\n        v-if=\"translationBtnShow\"\r\n      >\r\n        <span @click=\"translateEvent(drawerInfo)\">机器翻译</span>\r\n      </div>\r\n      <!-- <div class=\"tabs\" v-if=\"translationBtnShow\">\r\n        <span @click=\"\">人工翻译</span>\r\n      </div> -->\r\n\r\n      <div class=\"tabs\">\r\n        <span @click=\"backToTop()\">返回顶部</span>\r\n      </div>\r\n    </div>\r\n    <markmap-dialog\r\n      :visible.sync=\"markmapVisible\"\r\n      :content=\"markmapContent\"\r\n      :title=\"markmapTitle\"\r\n      :loading=\"markmapLoading\"\r\n      @close=\"handleMarkmapClose\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from \"vuex\";\r\nimport API from \"@/api/ScienceApi/index.js\";\r\nimport { feedbackAdd } from \"@/api/article/leaveMessage\";\r\nimport {\r\n  containsHtmlTags,\r\n  extractHtmlTags,\r\n  hasValidHtmlStructure,\r\n} from \"@/utils/htmlUtils\";\r\nimport { formatMarkdown } from \"@/utils/markdownUtils\";\r\nimport MarkmapDialog from \"./MarkmapDialog.vue\";\r\nimport TextEllipsis from \"@/components/TextEllipsis/index.vue\";\r\n\r\nexport default {\r\n  dicts: [],\r\n  components: {\r\n    MarkmapDialog,\r\n    TextEllipsis,\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      contentReady: false,\r\n      translationBtnShow: true,\r\n      titleShow: true,\r\n      drawerInfo: {},\r\n      htmlJson: \"\",\r\n      processedHtml: \"\",\r\n      originalArticleShow: true,\r\n      articleList: [],\r\n      type: true,\r\n      textarea: \"\",\r\n      url: \"\",\r\n      tabPosition: 1,\r\n      totalImages: 0,\r\n      loadedImages: 0,\r\n      imageLoadTimeout: null,\r\n      markdownContent: {\r\n        keyPoints: \"\",\r\n        entities: \"\",\r\n      },\r\n      markmapVisible: false,\r\n      markmapContent: \"\",\r\n      markmapTitle: \"\",\r\n      markmapLoading: false,\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"roles\", \"name\", \"avatar\"]),\r\n    isWeixinArticle() {\r\n      return (\r\n        this.drawerInfo.originalUrl &&\r\n        this.drawerInfo.originalUrl.includes(\"https://mp.weixin.qq.com\")\r\n      );\r\n    },\r\n    keyPointsFormatted() {\r\n      return formatMarkdown(this.markdownContent.keyPoints);\r\n    },\r\n    entitiesFormatted() {\r\n      return formatMarkdown(this.markdownContent.entities);\r\n    },\r\n  },\r\n  mounted() {\r\n    this.loading = true;\r\n    this.translationBtnShow = true;\r\n\r\n    // 添加全局meta标签禁用referrer\r\n    this.addNoReferrerMeta();\r\n\r\n    this.details();\r\n    this.getIndexData();\r\n    // this.hanldeBrowseAdd();\r\n  },\r\n  watch: {\r\n    tabPosition(newVal, oldVal) {\r\n      console.log(`Tab position changed from ${oldVal} to ${newVal}`);\r\n    },\r\n  },\r\n  methods: {\r\n    // 添加全局meta标签来禁用所有引用头\r\n    addNoReferrerMeta() {\r\n      // 检查是否已经存在meta标签\r\n      if (\r\n        document.querySelector('meta[name=\"referrer\"][content=\"no-referrer\"]')\r\n      ) {\r\n        return;\r\n      }\r\n\r\n      // 创建并添加meta标签\r\n      const meta = document.createElement(\"meta\");\r\n      meta.name = \"referrer\";\r\n      meta.content = \"no-referrer\";\r\n      document.head.appendChild(meta);\r\n      console.log(\"已添加no-referrer meta标签\");\r\n    },\r\n\r\n    // 辅助方法：修复图片的referrer设置\r\n    fixImageReferrer(content) {\r\n      // 确保每个微信图片标签都有referrerpolicy=\"no-referrer\"属性\r\n      if (!content) return content;\r\n\r\n      // 替换所有微信图片标签，确保它们有referrerpolicy属性\r\n      return content.replace(\r\n        /<img([^>]*?src=[\"']https?:\\/\\/mmbiz\\.q(?:logo|pic)\\.cn\\/[^\"']+[\"'][^>]*?)>/gi,\r\n        (match, attrPart) => {\r\n          // 如果已经有referrerpolicy属性，不再添加\r\n          if (attrPart.includes(\"referrerpolicy\")) {\r\n            return match;\r\n          }\r\n          // 添加referrerpolicy属性\r\n          return `<img${attrPart} referrerpolicy=\"no-referrer\">`;\r\n        }\r\n      );\r\n    },\r\n\r\n    // 使用canvas或图片代理增强微信图片的方法（不替换DOM节点）\r\n    replaceAllWechatImages() {\r\n      // 如果不是微信文章，直接返回\r\n      if (!this.isWeixinArticle) {\r\n        return Promise.resolve();\r\n      }\r\n\r\n      return new Promise((resolve) => {\r\n        try {\r\n          // 找到所有微信域名的图片\r\n          const wechatImages = document.querySelectorAll(\r\n            'img[src*=\"mmbiz.qpic.cn\"], img[src*=\"mmbiz.qlogo.cn\"], img[data-src*=\"mmbiz\"], img[src*=\"mmsns.qpic.cn\"]'\r\n          );\r\n\r\n          if (wechatImages.length === 0) {\r\n            resolve();\r\n            return;\r\n          }\r\n\r\n          console.log(`开始增强${wechatImages.length}张微信图片，保留现有图片`);\r\n          let processedCount = 0;\r\n\r\n          // 处理每一张图片\r\n          wechatImages.forEach((img, index) => {\r\n            // 如果图片已经被替换过且非空白，则跳过\r\n            if (\r\n              img.hasAttribute(\"data-wx-replaced\") &&\r\n              img.complete &&\r\n              img.naturalWidth > 0\r\n            ) {\r\n              processedCount++;\r\n              if (processedCount >= wechatImages.length) {\r\n                resolve();\r\n              }\r\n              return;\r\n            }\r\n\r\n            // 记录原始尺寸和样式\r\n            const originalWidth = img.style.width || img.width || \"auto\";\r\n            const originalHeight = img.style.height || img.height || \"auto\";\r\n\r\n            // 只有当图片无法显示时才进行处理\r\n            if (!img.complete || img.naturalWidth === 0) {\r\n              // 获取图片源\r\n              const originalSrc =\r\n                img.getAttribute(\"data-original-src\") ||\r\n                img.getAttribute(\"src\");\r\n              if (!originalSrc) {\r\n                // 无法获取源，跳过\r\n                processedCount++;\r\n                if (processedCount >= wechatImages.length) {\r\n                  resolve();\r\n                }\r\n                return;\r\n              }\r\n\r\n              // 添加必要的属性\r\n              img.setAttribute(\"referrerpolicy\", \"no-referrer\");\r\n              img.classList.add(\"wx-img\");\r\n              img.style.maxWidth = \"100%\";\r\n              img.style.height = \"auto\";\r\n\r\n              // 设置标识\r\n              img.setAttribute(\"data-wx-replaced\", \"true\");\r\n\r\n              // 尝试使用HTTPS加载\r\n              if (originalSrc.startsWith(\"http:\")) {\r\n                const httpsUrl = originalSrc.replace(/^http:/, \"https:\");\r\n                console.log(`尝试使用HTTPS协议: ${httpsUrl}`);\r\n                img.src = httpsUrl;\r\n              }\r\n              // 尝试添加格式参数\r\n              else if (!originalSrc.includes(\"wx_fmt=\")) {\r\n                const separator = originalSrc.includes(\"?\") ? \"&\" : \"?\";\r\n                const srcWithFormat = `${originalSrc}${separator}wx_fmt=jpeg`;\r\n                console.log(`尝试添加格式参数: ${srcWithFormat}`);\r\n                img.src = srcWithFormat;\r\n              }\r\n            }\r\n\r\n            // 无论是否处理，都计数\r\n            processedCount++;\r\n            if (processedCount >= wechatImages.length) {\r\n              resolve();\r\n            }\r\n          });\r\n\r\n          // 设置超时保障\r\n          setTimeout(() => {\r\n            resolve();\r\n          }, 3000);\r\n        } catch (e) {\r\n          console.error(\"增强微信图片出错:\", e);\r\n          resolve();\r\n        }\r\n      });\r\n    },\r\n\r\n    // 强制重新加载内容区域，避免清空导致图片丢失\r\n    forceReloadContent() {\r\n      // 如果没有内容或不是微信文章，直接返回\r\n      if (!this.htmlJson || !this.isWeixinArticle) {\r\n        return;\r\n      }\r\n\r\n      console.log(\"开始增强图片显示处理，保留现有DOM结构\");\r\n\r\n      // 不再清空内容，直接在现有DOM上处理图片\r\n      this.$nextTick(() => {\r\n        // 先进行基础修复\r\n        this.fixWechatImages();\r\n\r\n        // 延迟执行彻底替换\r\n        setTimeout(() => {\r\n          this.replaceAllWechatImages().then(() => {\r\n            console.log(\"完成彻底替换微信图片\");\r\n            // 最后强制更新一次视图\r\n            this.$forceUpdate();\r\n          });\r\n        }, 100);\r\n      });\r\n    },\r\n\r\n    // 预处理微信图片\r\n    preloadWechatImages(content) {\r\n      return new Promise((resolve) => {\r\n        try {\r\n          // 先应用全局referrer策略\r\n          this.addNoReferrerMeta();\r\n\r\n          // 先修复内容中包含转义字符的图片标签\r\n          content = this.fixEscapedImageTags(content);\r\n\r\n          // 修复内容中的图片标签\r\n          content = this.handleWechatImages(content);\r\n\r\n          // 处理相对路径图片\r\n          content = this.handleRelativeImagePaths(content);\r\n\r\n          // 标记内容准备好了\r\n          this.contentReady = true;\r\n          resolve(content);\r\n\r\n          // 在内容加载后，下一个事件循环再应用修复\r\n          setTimeout(() => {\r\n            this.fixWechatImages();\r\n          }, 0);\r\n        } catch (e) {\r\n          console.error(\"预处理微信图片出错:\", e);\r\n          this.contentReady = true;\r\n          resolve(content);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 修复包含转义字符的图片标签 - 更彻底的处理\r\n    fixEscapedImageTags(content) {\r\n      try {\r\n        if (!content) return content;\r\n\r\n        // 先处理全局的转义字符，简化后续处理\r\n        content = content\r\n          .replace(/\\\\\"/g, '\"')\r\n          .replace(/\\\\'/g, \"'\")\r\n          .replace(/\\\\\\\\/g, \"\\\\\")\r\n          .replace(/\\\\&quot;/g, \"&quot;\")\r\n          .replace(/&amp;/g, \"&\");\r\n\r\n        // 扩展匹配模式，捕获所有可能的问题格式\r\n        const escapedTagRegex =\r\n          /<img[^>]*?(?:src|data-src)=[\"']?(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")([^\"']+?)(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")[\"']?[^>]*?>/gi;\r\n        const badStyleRegex =\r\n          /<img[^>]*?style=[\"'][^\"']*?(?:16px|white-space)[^\"']*?[\"'][^>]*?>/gi;\r\n        const brokenPathRegex =\r\n          /<img[^>]*?src=[\"'][^\"']*?mmbiz[^\"']*?[\"'][^>]*?>/gi;\r\n\r\n        // 合并所有匹配结果\r\n        const escapedTags = content.match(escapedTagRegex) || [];\r\n        const badStyleTags = content.match(badStyleRegex) || [];\r\n        const brokenPathTags = content.match(brokenPathRegex) || [];\r\n\r\n        // 去重 - 转换为Set然后再转回数组\r\n        const allTags = [\r\n          ...new Set([...escapedTags, ...badStyleTags, ...brokenPathTags]),\r\n        ];\r\n\r\n        if (allTags.length === 0) {\r\n          return content; // 没有找到需要修复的标签\r\n        }\r\n\r\n        console.log(`找到${allTags.length}个可能有问题的图片标签`);\r\n\r\n        // 处理每个问题标签\r\n        for (const tag of allTags) {\r\n          // 提取图片URL - 尝试多种模式\r\n          let imgUrl = \"\";\r\n\r\n          // 尝试匹配各种可能的src格式\r\n          const patterns = [\r\n            /src=[\"']?(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")?([^\"'<>\\s]+?mmbiz[^\"'<>\\s]+)(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")?[\"']?/i,\r\n            /data-src=[\"']?(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")?([^\"'<>\\s]+?mmbiz[^\"'<>\\s]+)(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")?[\"']?/i,\r\n            /original-src=[\"']?(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")?([^\"'<>\\s]+?mmbiz[^\"'<>\\s]+)(?:\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\")?[\"']?/i,\r\n          ];\r\n\r\n          // 尝试所有模式直到找到匹配项\r\n          for (const pattern of patterns) {\r\n            const match = tag.match(pattern);\r\n            if (match && match[1]) {\r\n              imgUrl = match[1];\r\n              break;\r\n            }\r\n          }\r\n\r\n          if (!imgUrl) {\r\n            // 如果仍然无法提取URL，跳过此标签\r\n            continue;\r\n          }\r\n\r\n          // 清理URL并添加必要的参数\r\n          imgUrl = imgUrl.replace(\r\n            /\\\\&quot;|\\\\\"|&quot;|%22|\\\\\\\\&quot;|\\\\\\\\\"/g,\r\n            \"\"\r\n          );\r\n          if (!imgUrl.includes(\"wx_fmt=\") && imgUrl.includes(\"mmbiz\")) {\r\n            const separator = imgUrl.includes(\"?\") ? \"&\" : \"?\";\r\n            imgUrl += `${separator}wx_fmt=jpeg`;\r\n          }\r\n\r\n          // 处理可能存在的协议问题\r\n          if (!imgUrl.startsWith(\"http\")) {\r\n            if (imgUrl.startsWith(\"//\")) {\r\n              imgUrl = \"https:\" + imgUrl;\r\n            } else {\r\n              imgUrl = \"https://\" + imgUrl;\r\n            }\r\n          }\r\n\r\n          // 创建干净的替换标签 - 更简洁但完整\r\n          const newTag = `<img referrerpolicy=\"no-referrer\" class=\"wx-img\" src=\"${imgUrl}\" data-src=\"${imgUrl}\" data-original-src=\"${imgUrl}\" style=\"max-width:100%;height:auto!important;\" />`;\r\n\r\n          // 替换原始标签\r\n          content = content.replace(tag, newTag);\r\n        }\r\n\r\n        return content;\r\n      } catch (e) {\r\n        console.error(\"修复图片标签出错:\", e);\r\n        return content; // 出错时返回原始内容\r\n      }\r\n    },\r\n\r\n    // 动态修复图片方法 - 增强版，实现DOM重新渲染\r\n    fixWechatImages() {\r\n      try {\r\n        // 添加全局样式，确保所有微信图片都有no-referrer\r\n        const style = document.createElement(\"style\");\r\n        style.textContent = `\r\n          img[src*=\"mmbiz.qpic.cn\"], img[src*=\"mmbiz.qlogo.cn\"] {\r\n            max-width: 100% !important;\r\n            height: auto !important;\r\n            display: block !important;\r\n            margin: 10px auto !important;\r\n            object-fit: contain !important;\r\n            -webkit-referrer: no-referrer !important;\r\n            referrerpolicy: no-referrer !important;\r\n          }\r\n        `;\r\n        document.head.appendChild(style);\r\n\r\n        // 找到所有微信域名的图片\r\n        const wechatImages = document.querySelectorAll(\r\n          'img[src*=\"mmbiz.qpic.cn\"], img[src*=\"mmbiz.qlogo.cn\"], img[src*=\"mmsns.qpic.cn\"]'\r\n        );\r\n\r\n        if (wechatImages.length > 0) {\r\n          console.log(\r\n            `页面中找到${wechatImages.length}张微信图片，应用全局修复`\r\n          );\r\n\r\n          wechatImages.forEach((img) => {\r\n            // 添加必要的属性\r\n            img.setAttribute(\"referrerpolicy\", \"no-referrer\");\r\n            img.classList.add(\"wx-img\");\r\n\r\n            // 如果图片尚未进行错误处理，添加错误处理\r\n            if (!img.hasAttribute(\"data-error-handled\")) {\r\n              img.setAttribute(\"data-error-handled\", \"true\");\r\n\r\n              // 添加错误处理\r\n              img.onerror = function () {\r\n                console.log(\"图片加载失败，应用占位样式\");\r\n                this.style.border = \"1px dashed #ccc\";\r\n                this.style.padding = \"10px\";\r\n                this.style.width = \"auto\";\r\n                this.style.height = \"auto\";\r\n                this.style.minHeight = \"100px\";\r\n                this.alt = \"图片加载失败\";\r\n              };\r\n            }\r\n          });\r\n        }\r\n      } catch (e) {\r\n        console.error(\"修复微信图片出错:\", e);\r\n      }\r\n\r\n      // 返回一个Promise，方便外部检测完成状态\r\n      return Promise.resolve();\r\n    },\r\n\r\n    // 处理相对路径图片的方法\r\n    handleRelativeImagePaths(content) {\r\n      if (!content || !this.drawerInfo?.originalUrl) {\r\n        return content;\r\n      }\r\n\r\n      // 从原文链接中提取基础URL\r\n      const originalUrl = this.drawerInfo.originalUrl;\r\n      const urlParts = originalUrl.split(\"/\");\r\n      urlParts.pop(); // 移除文件名\r\n      const baseUrl = urlParts.join(\"/\") + \"/\";\r\n\r\n      // 处理所有相对路径图片\r\n      return content.replace(\r\n        /<img([^>]*?)src\\s*=\\s*[\"']([^\"']+)[\"']([^>]*?)>/gi,\r\n        (match, before, src, after) => {\r\n          // 跳过绝对路径\r\n          if (src.startsWith(\"http\") || src.startsWith(\"//\")) {\r\n            return match;\r\n          }\r\n\r\n          // 跳过非图片文件\r\n          if (!/\\.(png|jpg|jpeg|gif|webp|bmp|svg)(\\?.*)?$/i.test(src)) {\r\n            return match;\r\n          }\r\n\r\n          // 转换相对路径\r\n          let newSrc = \"\";\r\n          if (src.startsWith(\"./\")) {\r\n            newSrc = baseUrl + src.substring(2);\r\n          } else if (src.startsWith(\"/\")) {\r\n            const urlObj = new URL(originalUrl);\r\n            newSrc = urlObj.protocol + \"//\" + urlObj.host + src;\r\n          } else {\r\n            newSrc = baseUrl + src;\r\n          }\r\n\r\n          // 构建新的img标签，移除所有old*属性\r\n          let newTag = `<img${before} src=\"${newSrc}\"${after}>`;\r\n          newTag = newTag.replace(\r\n            /\\s*(oldsrc|oldSrc|old-src)\\s*=\\s*[\"'][^\"']*[\"']/gi,\r\n            \"\"\r\n          );\r\n\r\n          return newTag;\r\n        }\r\n      );\r\n    },\r\n\r\n    // 处理微信图片的公共方法\r\n    handleWechatImages(content) {\r\n      if (!content) return content;\r\n\r\n      try {\r\n        // 先处理转义字符问题\r\n        content = content\r\n          .replace(/\\\\\"/g, '\"')\r\n          .replace(/\\\\'/g, \"'\")\r\n          .replace(/\\\\\\\\/g, \"\\\\\");\r\n\r\n        // 将所有微信图片URL进行提取和替换\r\n        const regex =\r\n          /<img[^>]*?src=[\"'](https?:\\/\\/mmbiz\\.q(?:logo|pic)\\.cn\\/[^\"']+)[\"'][^>]*?>/gi;\r\n\r\n        // 简单直接的替换，确保每个微信图片都有正确属性\r\n        let newContent = content.replace(\r\n          regex,\r\n          '<img src=\"$1\" referrerpolicy=\"no-referrer\" class=\"wx-img\" style=\"max-width:100%;height:auto;\" />'\r\n        );\r\n\r\n        // 还需要处理已被转义的图片URL\r\n        const escapedRegex =\r\n          /<img[^>]*?src=[\"']?(\\\\?&quot;|\\\\?\"|&quot;|%22)(https?:\\/\\/mmbiz[^\"'&]+)(\\\\?&quot;|\\\\?\"|&quot;|%22)[\"']?[^>]*?>/gi;\r\n        newContent = newContent.replace(\r\n          escapedRegex,\r\n          '<img src=\"$2\" referrerpolicy=\"no-referrer\" class=\"wx-img\" style=\"max-width:100%;height:auto;\" />'\r\n        );\r\n\r\n        return newContent;\r\n      } catch (e) {\r\n        console.error(\"处理微信图片HTML出错:\", e);\r\n        return content;\r\n      }\r\n    },\r\n\r\n    async getIndexData() {\r\n      await API.recommendHot().then((response) => {\r\n        if (response.code == 200) {\r\n          this.articleList = response.data.slice(0, 5).map((item) => {\r\n            item.cnTitle = item.title;\r\n            item.id = item.articleId;\r\n            return item;\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    async details() {\r\n      let params;\r\n      if (this.$route.query.id) {\r\n        params = {\r\n          id: this.$route.query.id,\r\n        };\r\n      } else {\r\n        params = { articleSn: this.$route.query.articleSn };\r\n      }\r\n      // await API.AreaInfo(this.$route.query.id || this.$route.query.articleSn).then(async (res) => {\r\n      await API.articleDetail(params).then(async (res) => {\r\n        if (res.code == 200) {\r\n          this.hanldeBrowseAdd(res.data.id);\r\n          this.drawerInfo = res.data;\r\n          this.drawerInfo.sourceType != \"1\" && this.drawerInfo.sourceType != \"3\"\r\n            ? (this.translationBtnShow = true)\r\n            : (this.translationBtnShow = false);\r\n\r\n          // 如果是微信文章，先显示加载状态\r\n          if (\r\n            this.drawerInfo.originalUrl &&\r\n            this.drawerInfo.originalUrl.includes(\"https://mp.weixin.qq.com\")\r\n          ) {\r\n            this.contentReady = false;\r\n          } else {\r\n            this.contentReady = true;\r\n          }\r\n\r\n          // 预处理内容\r\n          const rawContent =\r\n            this.drawerInfo.cnContent || this.drawerInfo.content;\r\n          let processedContent = this.formattingJson(rawContent);\r\n\r\n          // 处理swdt数据\r\n          let keyPoints = \"\";\r\n          let entities = \"\";\r\n\r\n          if (this.drawerInfo.swdt && Array.isArray(this.drawerInfo.swdt)) {\r\n            this.drawerInfo.swdt.forEach((item) => {\r\n              if (item.swdtTaskid === \"1\" && item.swdtContent) {\r\n                // 内容要点\r\n                keyPoints = item.swdtContent;\r\n              } else if (item.swdtTaskid === \"2\" && item.swdtContent) {\r\n                // 人员/机构/技术/产品\r\n                entities = item.swdtContent;\r\n              }\r\n            });\r\n          }\r\n\r\n          // 预处理 markdown 内容，确保换行符正确处理\r\n          const preprocessMarkdown = (text) => {\r\n            if (!text) return \"\";\r\n            // 处理各种转义字符和格式问题\r\n            return text\r\n              .replace(/```markdown\\s*|```\\s*/g, \"\") // 移除 markdown 代码块标记\r\n              .replace(/```[a-zA-Z]*\\s*/g, \"\") // 移除带有语言标记的代码块标记\r\n              .replace(/\\\\n/g, \"\\n\") // 处理双反斜杠换行符\r\n              .replace(/\\\\\\n/g, \"\\n\") // 处理三反斜杠换行符\r\n              .replace(/\\r\\n/g, \"\\n\") // 处理 Windows 风格换行符\r\n              .replace(/\\$\\{[^}]+\\}/g, \"\"); // 移除模板字符串占位符\r\n          };\r\n\r\n          // 设置 markdown 内容，使用预处理函数\r\n          this.markdownContent.keyPoints = preprocessMarkdown(keyPoints);\r\n          this.markdownContent.entities = preprocessMarkdown(entities);\r\n\r\n          // 如果是微信公众号文章，进行预加载处理\r\n          if (\r\n            this.drawerInfo.originalUrl &&\r\n            this.drawerInfo.originalUrl.includes(\"https://mp.weixin.qq.com\")\r\n          ) {\r\n            try {\r\n              // 预加载微信图片 - 不会造成闪烁\r\n              processedContent = await this.preloadWechatImages(\r\n                processedContent\r\n              );\r\n              console.log(\"微信图片预处理完成\");\r\n            } catch (e) {\r\n              console.error(\"微信图片预处理失败:\", e);\r\n              // 出错时，显示内容\r\n              this.contentReady = true;\r\n            }\r\n          }\r\n\r\n          // 设置处理后的内容\r\n          this.$set(this, \"htmlJson\", processedContent);\r\n\r\n          if (this.drawerInfo.title == this.drawerInfo.cnTitle) {\r\n            this.titleShow = false;\r\n          }\r\n\r\n          document.title = this.drawerInfo.cnTitle || this.drawerInfo.title;\r\n          this.loading = false;\r\n\r\n          // 内容显示后，应用全局修复\r\n          if (this.isWeixinArticle) {\r\n            this.$nextTick(() => {\r\n              // 应用图片修复\r\n              this.fixWechatImages();\r\n\r\n              // 2秒后再检查一次\r\n              setTimeout(() => {\r\n                this.fixWechatImages();\r\n              }, 2000);\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    formattingJson(content) {\r\n      if (content) {\r\n        if (containsHtmlTags(content)) {\r\n          content = content.replace(/<br>/g, \"\");\r\n          content = content.replace(/\\n/g, \"\");\r\n          content = content.replace(/\\\\n/g, \"\");\r\n          content = content.replace(/\\\\\\n/g, \"\");\r\n          content = content.replace(\"|xa0\", \"\");\r\n          content = content.replace(\"opacity: 0\", \"\");\r\n\r\n          // 处理微信公众号图片防盗链问题\r\n          if (\r\n            this.drawerInfo.originalUrl &&\r\n            this.drawerInfo.originalUrl.includes(\"https://mp.weixin.qq.com\")\r\n          ) {\r\n            content = this.handleWechatImages(content);\r\n          }\r\n\r\n          // 处理相对路径图片\r\n          content = this.handleRelativeImagePaths(content);\r\n\r\n          console.log(\"包含的HTML标签\", extractHtmlTags(content));\r\n          console.log(\"HTML是否结构正确\", hasValidHtmlStructure(content));\r\n        } else {\r\n          content = content.replace(/\\n/g, \"<br>\");\r\n          content = content.replace(/\\\\n/g, \"<br>\");\r\n          content = content.replace(/\\\\\\n/g, \"<br>\");\r\n          content = content.replace(/\\${[^}]+}/g, \"<br>\");\r\n          content = content.replace(\"|xa0\", \"\");\r\n          content = content.replace(\"opacity: 0\", \"\");\r\n\r\n          // 处理微信公众号图片防盗链问题\r\n          if (\r\n            this.drawerInfo.originalUrl &&\r\n            this.drawerInfo.originalUrl.includes(\"https://mp.weixin.qq.com\")\r\n          ) {\r\n            content = this.handleWechatImages(content);\r\n          }\r\n\r\n          // 处理相对路径图片\r\n          content = this.handleRelativeImagePaths(content);\r\n        }\r\n      }\r\n      return content;\r\n    },\r\n    // 翻译文章\r\n    translateEvent(row) {\r\n      const fun = () => {\r\n        const loading = this.$loading({\r\n          lock: true,\r\n          text: \"Loading\",\r\n          spinner: \"el-icon-loading\",\r\n          background: \"rgba(0, 0, 0, 0.7)\",\r\n        });\r\n        API.translationTitle({\r\n          originalText: row.content,\r\n          docId: this.$route.query.id,\r\n          id: row.id,\r\n          translationField: \"content\",\r\n          translationType: 1,\r\n        })\r\n          .then((res) => {\r\n            this.drawerInfo.cnContent = res.data;\r\n            this.$set(\r\n              this,\r\n              \"htmlJson\",\r\n              this.formattingJson(\r\n                this.drawerInfo.cnContent || this.drawerInfo.content\r\n              )\r\n            );\r\n            this.originalArticleShow = true;\r\n            loading.close();\r\n          })\r\n          .catch((err) => {\r\n            loading.close();\r\n          });\r\n      };\r\n\r\n      if (row.cnContent) {\r\n        // 提示是否确认再次翻译\r\n        this.$confirm(\"是否确认再次翻译?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        })\r\n          .then(() => {\r\n            fun();\r\n          })\r\n          .catch(() => {\r\n            this.$set(\r\n              this,\r\n              \"htmlJson\",\r\n              this.formattingJson(\r\n                this.drawerInfo.cnContent || this.drawerInfo.content\r\n              )\r\n            );\r\n            this.originalArticleShow = true;\r\n          });\r\n      } else {\r\n        fun();\r\n      }\r\n    },\r\n    viewOriginal() {\r\n      if (this.originalArticleShow) {\r\n        this.$set(\r\n          this,\r\n          \"htmlJson\",\r\n          this.formattingJson(this.drawerInfo.content)\r\n        );\r\n      } else {\r\n        this.$set(\r\n          this,\r\n          \"htmlJson\",\r\n          this.formattingJson(\r\n            this.drawerInfo.cnContent || this.drawerInfo.content\r\n          )\r\n        );\r\n      }\r\n      this.originalArticleShow = !this.originalArticleShow;\r\n    },\r\n    backToTop() {\r\n      window.scrollTo({ top: 0, behavior: \"smooth\" });\r\n    },\r\n    close() {\r\n      this.type = true;\r\n      this.textarea = \"\";\r\n    },\r\n    submit() {\r\n      feedbackAdd({\r\n        articleId: this.$route.query.id,\r\n        docId: this.$route.query.id,\r\n        cnTitle: this.drawerInfo.cnTitle || this.drawerInfo.title,\r\n        content: this.textarea,\r\n      }).then((res) => {\r\n        if (res.code == 200) {\r\n          this.$message({\r\n            message: \"留言成功\",\r\n            type: \"success\",\r\n          });\r\n          this.close();\r\n        }\r\n      });\r\n    },\r\n    openNewView(item) {\r\n      window.open(`/expressDetails?id=${item.id}&docId=${item.id}`, \"_blank\");\r\n    },\r\n    handleCollections(item) {\r\n      if (!item.collection) {\r\n        API.collectApi([item.id]).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$message({\r\n              message: \"收藏成功,请前往个人中心查看\",\r\n              type: \"success\",\r\n            });\r\n            this.$set(this.drawerInfo, \"collection\", !item.collection);\r\n          } else {\r\n            this.$message({ message: \"收藏失败\", type: \"info\" });\r\n          }\r\n        });\r\n      } else {\r\n        API.cocelCollect([item.id]).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$message({ message: \"已取消收藏\", type: \"success\" });\r\n            this.$set(this.drawerInfo, \"collection\", !item.collection);\r\n          } else {\r\n            this.$message({ message: \"取消收藏失败\", type: \"info\" });\r\n          }\r\n        });\r\n      }\r\n    },\r\n    handleRecommend(item) {\r\n      let query = new FormData();\r\n      query.append(\"articleId\", item.id);\r\n      if (!item.recommend) {\r\n        API.recommendAdd(query).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$message({\r\n              message: \"推荐成功,请前往个人中心查看\",\r\n              type: \"success\",\r\n            });\r\n            this.$set(this.drawerInfo, \"recommend\", !item.recommend);\r\n          } else {\r\n            this.$message({ message: \"推荐失败\", type: \"info\" });\r\n          }\r\n        });\r\n      } else {\r\n        API.recommendCancel(query).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$message({ message: \"已取消推荐\", type: \"success\" });\r\n            this.$set(this.drawerInfo, \"recommend\", !item.recommend);\r\n          } else {\r\n            this.$message({ message: \"取消推荐失败\", type: \"info\" });\r\n          }\r\n        });\r\n      }\r\n    },\r\n    hanldeBrowseAdd(id) {\r\n      let query = new FormData();\r\n      query.append(\"articleId\", id);\r\n      API.browseAdd(query);\r\n    },\r\n    viewOriginalArticle(item) {\r\n      window.open(item.originalUrl);\r\n    },\r\n    // 打开思维导图弹窗\r\n    openMarkmap(type) {\r\n      if (!this.markdownContent[type]) {\r\n        this.$message.warning(\"暂无思维导图数据\");\r\n        return;\r\n      }\r\n\r\n      // 设置标题和内容\r\n      let title = \"\";\r\n      if (type === \"keyPoints\") {\r\n        title = \"内容要点思维导图\";\r\n      } else if (type === \"entities\") {\r\n        title = \"人员/机构/技术/产品思维导图\";\r\n      }\r\n\r\n      // 设置内容\r\n      const content = this.markdownContent[type];\r\n\r\n      // 使用localStorage存储大型内容，而不是通过URL传递\r\n      const storageKey = `markmap_data_${Date.now()}`;\r\n      localStorage.setItem(storageKey, content);\r\n\r\n      // 构建URL参数 - 只传递标题和存储键，不传递大型内容\r\n      const params = new URLSearchParams();\r\n      params.append(\"title\", title);\r\n      params.append(\r\n        \"articleTitle\",\r\n        this.drawerInfo.cnTitle || this.drawerInfo.title\r\n      );\r\n      params.append(\"storageKey\", storageKey);\r\n\r\n      // 获取屏幕尺寸，制定合适的窗口大小\r\n      const screenWidth = window.screen.width;\r\n      const screenHeight = window.screen.height;\r\n\r\n      // 窗口宽高为屏幕的80%，并居中显示\r\n      const width = Math.round(screenWidth * 0.8);\r\n      const height = Math.round(screenHeight * 0.8);\r\n      const left = Math.round((screenWidth - width) / 2);\r\n      const top = Math.round((screenHeight - height) / 2);\r\n\r\n      // 尝试打开新窗口\r\n      const windowFeatures = `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes,status=yes`;\r\n      const newWindow = window.open(\r\n        `/markmap?${params.toString()}`,\r\n        \"_blank\",\r\n        windowFeatures\r\n      );\r\n\r\n      // 检查是否成功打开窗口，如果被浏览器阻止则打开新标签页\r\n      if (\r\n        !newWindow ||\r\n        newWindow.closed ||\r\n        typeof newWindow.closed === \"undefined\"\r\n      ) {\r\n        // 浏览器可能阻止了弹窗，使用新标签页\r\n        window.open(`/markmap?${params.toString()}`, \"_blank\");\r\n        this.$message.info(\"新窗口被浏览器阻止，已在新标签页打开\");\r\n      }\r\n    },\r\n    // 关闭思维导图弹窗 - 保留以防需要\r\n    handleMarkmapClose() {\r\n      this.markmapVisible = false;\r\n      this.markmapContent = \"\";\r\n    },\r\n    // 处理发布时间的显示\r\n    formatPublishTime(publishTime, publishLocaltime) {\r\n      // 格式化publishTime为年月日\r\n      const formattedPublishTime = this.parseTime(publishTime, \"{y}-{m}-{d}\");\r\n\r\n      // 如果webstePublishTime不存在，直接返回publishTime\r\n      if (!publishLocaltime) {\r\n        return \"[北京]\" + formattedPublishTime;\r\n      }\r\n\r\n      let formattedWebsteTime = \"\";\r\n      // 处理不同格式的webstePublishTime\r\n      if (publishLocaltime) {\r\n        formattedWebsteTime = this.parseTime(publishLocaltime, \"{y}-{m}-{d}\");\r\n      }\r\n\r\n      // 比较年月日是否相同\r\n      if (formattedPublishTime === formattedWebsteTime) {\r\n        return \"[北京]\" + formattedPublishTime;\r\n      } else {\r\n        return `[当地]${formattedWebsteTime} / [北京]${formattedPublishTime}`;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n/* 全局样式 */\r\n::v-deep img[src*=\"mmbiz\"] {\r\n  max-width: 100% !important;\r\n  height: auto !important;\r\n  object-fit: contain !important;\r\n  margin: 10px auto !important;\r\n  display: block !important;\r\n  -webkit-referrer: no-referrer !important;\r\n  referrerpolicy: no-referrer !important;\r\n}\r\n\r\n.drawer_box {\r\n  display: flex;\r\n  user-select: text !important;\r\n  background: #f5f7fa;\r\n  min-height: 100vh;\r\n  justify-content: center;\r\n\r\n  .drawer_Style {\r\n    position: relative;\r\n    z-index: 2;\r\n    margin: 0px 20px 0px;\r\n    width: 800px;\r\n    background: #ffffff;\r\n    padding: 10px 30px;\r\n\r\n    .title {\r\n      font-size: 22px;\r\n      font-weight: 500px;\r\n      // text-align: center;\r\n    }\r\n\r\n    .source {\r\n      color: #0798f8;\r\n      // text-align: center;\r\n      font-size: 14px;\r\n    }\r\n\r\n    .time {\r\n      font-size: 14px;\r\n      // text-align: center;\r\n      margin-left: 10px;\r\n      color: #9b9b9b;\r\n    }\r\n\r\n    .author {\r\n      color: #1a0997;\r\n      // text-align: center;\r\n      margin-left: 10px;\r\n      font-size: 14px;\r\n    }\r\n\r\n    .summary {\r\n      background-color: #f5f7fa;\r\n      padding: 10px;\r\n      border-radius: 8px;\r\n      margin-bottom: 20px;\r\n    }\r\n    .summary .summary-item1 {\r\n      color: #971231;\r\n      font-weight: 600;\r\n      margin-bottom: 10px;\r\n    }\r\n    .summary .summary-item2 {\r\n      line-height: 30px;\r\n      word-break: break-word;\r\n    }\r\n\r\n    /* 文本展开折叠组件样式调整 */\r\n    .summary ::v-deep .text-ellipsis {\r\n      width: 100%;\r\n    }\r\n\r\n    .summary ::v-deep .text-content {\r\n      line-height: 30px;\r\n      font-size: 16px;\r\n      word-break: break-word;\r\n    }\r\n\r\n    .summary ::v-deep .text-limited {\r\n      -webkit-line-clamp: 10 !important;\r\n    }\r\n\r\n    .summary ::v-deep .ellipsis-actions {\r\n      text-align: right;\r\n      // margin-top: 8px;\r\n    }\r\n\r\n    .summary ::v-deep .show-more,\r\n    .summary ::v-deep .show-less {\r\n      color: #0798f8;\r\n      font-size: 14px;\r\n      transition: color 0.3s ease;\r\n    }\r\n\r\n    .summary ::v-deep .show-more:hover,\r\n    .summary ::v-deep .show-less:hover {\r\n      color: #0071ce;\r\n    }\r\n\r\n    .summary ::v-deep .show-more i,\r\n    .summary ::v-deep .show-less i {\r\n      margin-left: 3px;\r\n      font-size: 12px;\r\n    }\r\n  }\r\n}\r\n\r\n.right {\r\n  width: 500px;\r\n\r\n  .sisterDetails {\r\n    background-color: #ffffff;\r\n    padding: 10px 20px;\r\n    width: 100%;\r\n    color: #000000;\r\n\r\n    .media-info {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      .media-info-item {\r\n        flex: 1;\r\n      }\r\n    }\r\n\r\n    .name {\r\n      color: #9b9b9b;\r\n      line-height: 1.8;\r\n      font-size: 14px;\r\n\r\n      .value {\r\n        margin-left: 15px;\r\n        color: #000000;\r\n      }\r\n    }\r\n  }\r\n\r\n  .markdown {\r\n    margin-top: 20px;\r\n    background-color: #f2e3c5;\r\n    padding: 10px 20px;\r\n    width: 100%;\r\n    color: #000000;\r\n    height: 810px;\r\n\r\n    ::v-deep .el-divider {\r\n      background-color: #838484;\r\n      margin: 8px 0;\r\n    }\r\n\r\n    .markdown-content {\r\n      margin-bottom: 10px;\r\n      .markdown-content-title {\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n        margin-bottom: 10px;\r\n      }\r\n      .markdown-content-text {\r\n        height: 325px;\r\n        overflow-y: auto;\r\n        padding: 10px;\r\n        line-height: 1.6;\r\n        font-size: 16px;\r\n        color: #333333;\r\n        border-radius: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .recommendedArticle {\r\n    margin-top: 20px;\r\n    background-color: #ffffff;\r\n    padding: 10px 20px;\r\n    width: 100%;\r\n    color: #000000;\r\n\r\n    .articleBox {\r\n      font-size: 14px;\r\n      margin: 10px 0;\r\n\r\n      .article {\r\n        margin-bottom: 5px;\r\n        cursor: pointer;\r\n        position: relative;\r\n        // width: calc(100% - 75px);\r\n        line-height: 1.8;\r\n\r\n        &:before {\r\n          content: \"•\";\r\n          color: red;\r\n          font-weight: bold;\r\n        }\r\n      }\r\n\r\n      .bottom {\r\n        display: flex;\r\n        color: #9b9b9b;\r\n        align-items: baseline;\r\n\r\n        .sourceName {\r\n          margin-left: 10px;\r\n        }\r\n\r\n        .count {\r\n          margin-left: 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .keyword {\r\n    margin-top: 20px;\r\n    background-color: #ffffff;\r\n    padding: 10px 20px;\r\n    width: 100%;\r\n    color: #000000;\r\n    height: 350px;\r\n\r\n    .chartBox {\r\n      height: 300px;\r\n    }\r\n  }\r\n\r\n  .articleEntity {\r\n    margin-top: 20px;\r\n    background-color: #ffffff;\r\n    padding: 10px 20px;\r\n    width: 100%;\r\n    color: #000000;\r\n    height: 350px;\r\n\r\n    .chartBox {\r\n      height: 300px;\r\n      position: relative;\r\n    }\r\n  }\r\n}\r\n\r\n.right_title {\r\n  font-size: 20px;\r\n}\r\n\r\n.el-divider-right {\r\n  margin: 8px 0;\r\n}\r\n\r\n.liuyanBox {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: calc(50vw - 653px);\r\n  width: 800px;\r\n  z-index: 99;\r\n  background-color: #e3e3ef;\r\n  padding: 5px 10px;\r\n  border-radius: 5px;\r\n\r\n  .morenzhuangtai {\r\n    height: 40px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .shuruzhuangtai {\r\n    height: 90px;\r\n\r\n    .top {\r\n      height: 40px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n    }\r\n\r\n    .bottom {\r\n      height: 50px;\r\n    }\r\n  }\r\n}\r\n\r\n.uesr {\r\n  display: flex;\r\n  align-items: center;\r\n  height: 30px;\r\n\r\n  .avatar {\r\n    cursor: pointer;\r\n    width: 30px;\r\n    height: 30px;\r\n    border-radius: 50%;\r\n  }\r\n\r\n  .name {\r\n    margin-left: 10px;\r\n  }\r\n}\r\n\r\n.tabs-all {\r\n  position: fixed;\r\n  left: calc(50vw - 693px);\r\n  top: 0px;\r\n  z-index: 99;\r\n  height: 800px;\r\n  width: 40px;\r\n\r\n  @media screen and (max-width: 1400px) {\r\n    left: 0;\r\n  }\r\n\r\n  .tabs {\r\n    writing-mode: vertical-rl;\r\n    /* 文字从上到下，从右到左 */\r\n    height: 120px;\r\n    width: 40px;\r\n    font-weight: 800;\r\n    font-size: 16px;\r\n    color: #ffffff;\r\n    line-height: 35px;\r\n    text-align: center;\r\n    font-style: normal;\r\n    background: url(\"../../assets/bigScreenTwo/tab-active.png\") no-repeat 0px\r\n      0px !important;\r\n    background-size: 100% 100% !important;\r\n    letter-spacing: 2px;\r\n    margin-bottom: 10px;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.content-loading {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 100px;\r\n  width: 100%;\r\n  margin: 20px 0;\r\n}\r\n\r\n.loading-progress {\r\n  width: 80%;\r\n  max-width: 500px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.loading-text {\r\n  text-align: center;\r\n  margin-bottom: 10px;\r\n  color: #409eff;\r\n  font-size: 14px;\r\n}\r\n\r\n.preloading-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 300px;\r\n  width: 100%;\r\n}\r\n\r\n::v-deep img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: contain;\r\n}\r\n\r\n::v-deep video {\r\n  width: 100%;\r\n}\r\n\r\n/* 添加微信图片特殊样式 */\r\n::v-deep .wx-img {\r\n  max-width: 100%;\r\n  height: auto !important;\r\n  display: block;\r\n  margin: 10px auto;\r\n}\r\n\r\n/* 替换后的图片样式 */\r\n::v-deep .wx-img-replaced {\r\n  max-width: 100%;\r\n  height: auto !important;\r\n  display: block;\r\n  margin: 10px auto;\r\n  border: 1px solid transparent;\r\n}\r\n\r\n/* Canvas创建的图片样式 */\r\n::v-deep .wx-img-canvas {\r\n  max-width: 100%;\r\n  height: auto !important;\r\n  display: block;\r\n  margin: 10px auto;\r\n  border: 1px solid transparent;\r\n}\r\n\r\n/* 占位图样式 */\r\n::v-deep .wx-img-placeholder {\r\n  max-width: 100%;\r\n  min-height: 100px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 10px auto;\r\n  border: 1px dashed #ccc;\r\n  padding: 20px;\r\n  font-size: 12px;\r\n  color: #999;\r\n  text-align: center;\r\n}\r\n\r\n/* markdown显示样式 */\r\n::v-deep .md-h1,\r\n::v-deep h1.md-h1 {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin: 12px 0 8px 0;\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .md-h2,\r\n::v-deep h2.md-h2 {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin: 10px 0 6px 0;\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .md-h3,\r\n::v-deep h3.md-h3 {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin: 12px 0 6px 0;\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .md-li {\r\n  margin: 5px 0;\r\n  padding-left: 10px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n::v-deep .md-bullet,\r\n::v-deep .md-number {\r\n  margin-right: 8px;\r\n  color: #333333;\r\n  font-weight: bold;\r\n  flex-shrink: 0;\r\n}\r\n\r\n::v-deep .md-blockquote,\r\n::v-deep blockquote.md-blockquote {\r\n  border-left: 4px solid #d0d0d0;\r\n  padding: 5px 10px;\r\n  margin: 10px 0;\r\n  background-color: #f9f9f9;\r\n  font-style: italic;\r\n  color: #555;\r\n}\r\n\r\n::v-deep .markdown-content-text strong {\r\n  font-weight: bold;\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .markdown-content-text em {\r\n  font-style: italic;\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .md-code-block,\r\n::v-deep pre.md-code-block {\r\n  background-color: #f6f8fa;\r\n  border-radius: 3px;\r\n  padding: 10px;\r\n  margin: 10px 0;\r\n  font-family: monospace;\r\n  overflow-x: auto;\r\n  white-space: pre;\r\n  font-size: 13px;\r\n}\r\n\r\n::v-deep .md-code-inline,\r\n::v-deep code.md-code-inline {\r\n  background-color: #f6f8fa;\r\n  border-radius: 3px;\r\n  padding: 2px 4px;\r\n  font-family: monospace;\r\n  font-size: 13px;\r\n}\r\n\r\n/* 添加列表和段落样式 */\r\n::v-deep .md-paragraph {\r\n  margin: 5px 0;\r\n  line-height: 1.6;\r\n}\r\n\r\n::v-deep .md-paragraph-space {\r\n  height: 6px; /* 仅在需要的情况下调整为更小的值 */\r\n}\r\n\r\n::v-deep .md-ul,\r\n::v-deep .md-ol {\r\n  margin: 10px 0;\r\n  padding-left: 25px;\r\n}\r\n\r\n::v-deep .md-ul {\r\n  list-style-type: disc;\r\n}\r\n\r\n::v-deep .md-ul li {\r\n  margin: 5px 0;\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .md-ul li::marker {\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .md-ol {\r\n  list-style-type: decimal;\r\n}\r\n\r\n::v-deep .md-ol li {\r\n  margin: 5px 0;\r\n  color: #333333;\r\n}\r\n\r\n::v-deep .md-ol li::marker {\r\n  color: #333333;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 添加滚动条样式： */\r\n/* 自定义滚动条样式 */\r\n::v-deep .markdown-content-text::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n::v-deep .markdown-content-text::-webkit-scrollbar-track {\r\n  background: transparent; /* 去掉滚动条背景色 */\r\n}\r\n\r\n::v-deep .markdown-content-text::-webkit-scrollbar-thumb {\r\n  background-color: #ffffff; /* 中等灰色 */\r\n  border-radius: 3px;\r\n}\r\n\r\n::v-deep .markdown-content-text::-webkit-scrollbar-thumb:hover {\r\n  background-color: #f7f7f7; /* 悬停时的颜色 */\r\n}\r\n\r\n/* 点击看大图按钮样式 */\r\n.view-markmap {\r\n  display: inline-block;\r\n  margin-left: 10px;\r\n  font-size: 14px;\r\n  color: #1890ff;\r\n  cursor: pointer;\r\n  background-color: rgba(24, 144, 255, 0.1);\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.view-markmap:hover {\r\n  background-color: rgba(24, 144, 255, 0.2);\r\n  text-decoration: underline;\r\n}\r\n\r\n.markdown-content-title {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n::v-deep pre {\r\n  white-space: normal;\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\kejizixuntuijian\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\kejizixuntuijian\\index.vue", "mtime": 1754447229704}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\babel.config.js", "mtime": 1745890588273}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_splitpanes", "require", "_index", "_interopRequireDefault", "name", "components", "Splitpanes", "Pane", "data", "globalLoading", "leftTableData", "leftLoading", "leftTotal", "leftCurrentPage", "leftPageSize", "leftSearchTimer", "selectedLeftRow", "rightTableData", "rightLoading", "rightTotal", "rightCurrentPage", "rightPageSize", "created", "initData", "methods", "_this", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getLeftTableData", "t0", "console", "error", "$message", "finish", "stop", "_this2", "_callee2", "params", "response", "mockData", "_callee2$", "_context2", "pageNum", "pageSize", "API", "getCategoryList", "sent", "rows", "total", "warn", "generateMockLeftData", "length", "$nextTick", "handleLeftRowClick", "t1", "categories", "startIndex", "endIndex", "Math", "min", "filteredCategories", "slice", "map", "title", "index", "id", "getRightTableData", "_this3", "_callee3", "_callee3$", "_context3", "abrupt", "categoryId", "getRecommendList", "generateMockRightData", "_this4", "mockArticles", "score", "sourceName", "publishTime", "field", "categoryRelatedArticles", "article", "_objectSpread2", "concat", "row", "$refs", "leftTable", "setCurrentRow", "handleLeftPagination", "_ref", "page", "limit", "handleRightPagination", "_ref2", "getScoreTagType", "openDetail", "url", "window", "open"], "sources": ["src/views/kejizixuntuijian/index.vue"], "sourcesContent": ["<template>\r\n  <div v-loading=\"globalLoading\" element-loading-text=\"数据加载中\">\r\n    <splitpanes class=\"default-theme\">\r\n      <!-- 左侧面板 -->\r\n      <pane\r\n        class=\"leftLink\"\r\n        ref=\"leftLink\"\r\n        min-size=\"20\"\r\n        max-size=\"50\"\r\n        size=\"30\"\r\n      >\r\n        <div class=\"left-panel\">\r\n          <!-- 左侧表格 -->\r\n          <div class=\"left-table-container\">\r\n            <el-table\r\n              :data=\"leftTableData\"\r\n              v-loading=\"leftLoading\"\r\n              @row-click=\"handleLeftRowClick\"\r\n              :highlight-current-row=\"true\"\r\n              ref=\"leftTable\"\r\n              height=\"auto\"\r\n              size=\"small\"\r\n              border\r\n              :show-header=\"false\"\r\n              style=\"font-size: 14px\"\r\n            >\r\n              <el-table-column\r\n                type=\"index\"\r\n                label=\"序号\"\r\n                width=\"60\"\r\n                align=\"center\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  {{ (leftCurrentPage - 1) * leftPageSize + scope.$index + 1 }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"title\" label=\"标题\" show-overflow-tooltip>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <!-- 左侧分页 -->\r\n            <div class=\"left-pagination\">\r\n              <pagination\r\n                v-show=\"leftTotal > 0\"\r\n                :total=\"leftTotal\"\r\n                :page.sync=\"leftCurrentPage\"\r\n                :limit.sync=\"leftPageSize\"\r\n                @pagination=\"handleLeftPagination\"\r\n                :layout=\"'total, prev, pager, next'\"\r\n                :background=\"false\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </pane>\r\n\r\n      <!-- 右侧面板 -->\r\n      <pane min-size=\"50\" max-size=\"80\" size=\"70\">\r\n        <div class=\"right-panel\">\r\n          <!-- 右侧表格 -->\r\n          <div class=\"right-table-container\">\r\n            <el-table\r\n              :data=\"rightTableData\"\r\n              v-loading=\"rightLoading\"\r\n              height=\"auto\"\r\n              size=\"small\"\r\n              border\r\n              style=\"font-size: 14px\"\r\n            >\r\n              <el-table-column\r\n                prop=\"score\"\r\n                label=\"推荐分值\"\r\n                width=\"100\"\r\n                align=\"center\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag :type=\"getScoreTagType(scope.row.score)\" size=\"small\">\r\n                    {{ scope.row.score }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"title\" label=\"标题\" show-overflow-tooltip>\r\n                <template slot-scope=\"scope\">\r\n                  <el-link\r\n                    type=\"primary\"\r\n                    @click=\"openDetail(scope.row)\"\r\n                    :underline=\"false\"\r\n                  >\r\n                    {{ scope.row.title }}\r\n                  </el-link>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"sourceName\"\r\n                label=\"来源名称\"\r\n                width=\"150\"\r\n                show-overflow-tooltip\r\n              >\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"publishTime\"\r\n                label=\"发布日期\"\r\n                width=\"120\"\r\n                align=\"center\"\r\n              >\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"field\"\r\n                label=\"所属领域\"\r\n                width=\"120\"\r\n                show-overflow-tooltip\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag size=\"mini\" v-if=\"scope.row.field\">\r\n                    {{ scope.row.field }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <!-- 右侧分页 -->\r\n            <div class=\"right-pagination\">\r\n              <pagination\r\n                v-show=\"rightTotal > 0\"\r\n                :total=\"rightTotal\"\r\n                :page.sync=\"rightCurrentPage\"\r\n                :limit.sync=\"rightPageSize\"\r\n                @pagination=\"handleRightPagination\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </pane>\r\n    </splitpanes>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { Splitpanes, Pane } from \"splitpanes\";\r\nimport \"splitpanes/dist/splitpanes.css\";\r\nimport API from \"@/api/ScienceApi/index.js\";\r\n\r\nexport default {\r\n  name: \"KejizixuntuijianIndex\",\r\n  components: {\r\n    Splitpanes,\r\n    Pane,\r\n  },\r\n  data() {\r\n    return {\r\n      // 全局加载状态\r\n      globalLoading: false,\r\n\r\n      // 左侧数据\r\n      leftTableData: [],\r\n      leftLoading: false,\r\n      leftTotal: 0,\r\n      leftCurrentPage: 1,\r\n      leftPageSize: 20,\r\n      leftSearchTimer: null,\r\n      selectedLeftRow: null, // 当前选中的左侧行\r\n\r\n      // 右侧数据\r\n      rightTableData: [],\r\n      rightLoading: false,\r\n      rightTotal: 0,\r\n      rightCurrentPage: 1,\r\n      rightPageSize: 20,\r\n    };\r\n  },\r\n  created() {\r\n    this.initData();\r\n  },\r\n  methods: {\r\n    // 初始化数据\r\n    async initData() {\r\n      this.globalLoading = true;\r\n      try {\r\n        await this.getLeftTableData();\r\n      } catch (error) {\r\n        console.error(\"初始化数据失败:\", error);\r\n        this.$message.error(\"数据加载失败，请重试\");\r\n      } finally {\r\n        this.globalLoading = false;\r\n      }\r\n    },\r\n\r\n    // 获取左侧表格数据\r\n    async getLeftTableData() {\r\n      this.leftLoading = true;\r\n      try {\r\n        // 尝试调用真实API，如果失败则使用模拟数据\r\n        try {\r\n          const params = {\r\n            pageNum: this.leftCurrentPage,\r\n            pageSize: this.leftPageSize,\r\n          };\r\n          const response = await API.getCategoryList(params);\r\n          this.leftTableData = response.rows || response.data || [];\r\n          this.leftTotal = response.total || 0;\r\n        } catch (apiError) {\r\n          console.warn(\"API调用失败，使用模拟数据:\", apiError);\r\n          // 使用模拟数据作为后备\r\n          const mockData = this.generateMockLeftData();\r\n          this.leftTableData = mockData.rows;\r\n          this.leftTotal = mockData.total;\r\n        }\r\n\r\n        // 如果是第一次加载且有数据，自动选中第一行\r\n        if (\r\n          this.leftCurrentPage === 1 &&\r\n          this.leftTableData.length > 0 &&\r\n          !this.selectedLeftRow\r\n        ) {\r\n          this.$nextTick(() => {\r\n            this.handleLeftRowClick(this.leftTableData[0]);\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取左侧数据失败:\", error);\r\n        this.$message.error(\"获取分类数据失败\");\r\n      } finally {\r\n        this.leftLoading = false;\r\n      }\r\n    },\r\n\r\n    // 生成模拟左侧数据\r\n    generateMockLeftData() {\r\n      const categories = [\r\n        \"人工智能技术\",\r\n        \"量子计算研究\",\r\n        \"生物医学工程\",\r\n        \"新能源技术\",\r\n        \"航空航天技术\",\r\n        \"材料科学\",\r\n        \"信息安全技术\",\r\n        \"机器人技术\",\r\n        \"区块链技术\",\r\n        \"物联网技术\",\r\n        \"5G通信技术\",\r\n        \"自动驾驶技术\",\r\n        \"虚拟现实技术\",\r\n        \"基因编辑技术\",\r\n        \"纳米技术\",\r\n      ];\r\n\r\n      const total = categories.length;\r\n      const startIndex = (this.leftCurrentPage - 1) * this.leftPageSize;\r\n      const endIndex = Math.min(startIndex + this.leftPageSize, total);\r\n\r\n      let filteredCategories = categories;\r\n\r\n      const rows = filteredCategories\r\n        .slice(startIndex, endIndex)\r\n        .map((title, index) => ({\r\n          id: startIndex + index + 1,\r\n          title: title,\r\n        }));\r\n\r\n      return {\r\n        rows,\r\n        total: filteredCategories.length,\r\n      };\r\n    },\r\n\r\n    // 获取右侧表格数据\r\n    async getRightTableData() {\r\n      if (!this.selectedLeftRow) {\r\n        this.rightTableData = [];\r\n        this.rightTotal = 0;\r\n        return;\r\n      }\r\n\r\n      this.rightLoading = true;\r\n      try {\r\n        // 尝试调用真实API，如果失败则使用模拟数据\r\n        try {\r\n          const params = {\r\n            categoryId: this.selectedLeftRow.id,\r\n            pageNum: this.rightCurrentPage,\r\n            pageSize: this.rightPageSize,\r\n          };\r\n          const response = await API.getRecommendList(params);\r\n          this.rightTableData = response.rows || response.data || [];\r\n          this.rightTotal = response.total || 0;\r\n        } catch (apiError) {\r\n          console.warn(\"API调用失败，使用模拟数据:\", apiError);\r\n          // 使用模拟数据作为后备\r\n          const mockData = this.generateMockRightData();\r\n          this.rightTableData = mockData.rows;\r\n          this.rightTotal = mockData.total;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取右侧数据失败:\", error);\r\n        this.$message.error(\"获取推荐数据失败\");\r\n      } finally {\r\n        this.rightLoading = false;\r\n      }\r\n    },\r\n\r\n    // 生成模拟右侧数据\r\n    generateMockRightData() {\r\n      const mockArticles = [\r\n        {\r\n          id: 1,\r\n          title: \"人工智能在医疗诊断中的最新突破\",\r\n          score: 95,\r\n          sourceName: \"科技日报\",\r\n          publishTime: \"2024-01-15\",\r\n          field: \"人工智能\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"量子计算机实现新的里程碑\",\r\n          score: 88,\r\n          sourceName: \"自然杂志\",\r\n          publishTime: \"2024-01-14\",\r\n          field: \"量子计算\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"基因编辑技术的伦理考量\",\r\n          score: 82,\r\n          sourceName: \"生物技术周刊\",\r\n          publishTime: \"2024-01-13\",\r\n          field: \"生物技术\",\r\n        },\r\n        {\r\n          id: 4,\r\n          title: \"新能源汽车电池技术革新\",\r\n          score: 76,\r\n          sourceName: \"能源观察\",\r\n          publishTime: \"2024-01-12\",\r\n          field: \"新能源\",\r\n        },\r\n        {\r\n          id: 5,\r\n          title: \"5G网络安全防护新方案\",\r\n          score: 71,\r\n          sourceName: \"通信世界\",\r\n          publishTime: \"2024-01-11\",\r\n          field: \"通信技术\",\r\n        },\r\n      ];\r\n\r\n      // 根据选中的分类生成相关数据\r\n      const categoryRelatedArticles = mockArticles.map((article, index) => ({\r\n        ...article,\r\n        id: (this.rightCurrentPage - 1) * this.rightPageSize + index + 1,\r\n        title: `${this.selectedLeftRow.title}相关：${article.title}`,\r\n        field: this.selectedLeftRow.title,\r\n      }));\r\n\r\n      const total = 50; // 模拟总数\r\n      const startIndex = (this.rightCurrentPage - 1) * this.rightPageSize;\r\n      const rows = categoryRelatedArticles.slice(\r\n        0,\r\n        Math.min(this.rightPageSize, total - startIndex)\r\n      );\r\n\r\n      return {\r\n        rows,\r\n        total,\r\n      };\r\n    },\r\n\r\n    // 处理左侧行点击\r\n    handleLeftRowClick(row) {\r\n      this.selectedLeftRow = row;\r\n      this.$refs.leftTable.setCurrentRow(row);\r\n\r\n      // 重置右侧分页并加载数据\r\n      this.rightCurrentPage = 1;\r\n      this.getRightTableData();\r\n    },\r\n\r\n    // 处理左侧分页\r\n    handleLeftPagination({ page, limit }) {\r\n      this.leftCurrentPage = page;\r\n      this.leftPageSize = limit;\r\n      this.getLeftTableData();\r\n    },\r\n\r\n    // 处理右侧分页\r\n    handleRightPagination({ page, limit }) {\r\n      this.rightCurrentPage = page;\r\n      this.rightPageSize = limit;\r\n      this.getRightTableData();\r\n    },\r\n\r\n    // 获取分值标签类型\r\n    getScoreTagType(score) {\r\n      if (score >= 90) return \"success\";\r\n      if (score >= 80) return \"warning\";\r\n      if (score >= 70) return \"info\";\r\n      return \"danger\";\r\n    },\r\n\r\n    // 打开详情页\r\n    openDetail(row) {\r\n      // 这里可以根据实际需求打开详情页\r\n      // 例如：跳转到新页面或打开弹窗\r\n      const url = `/expressDetails?id=${row.id}`;\r\n      window.open(url, \"_blank\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.left-panel {\r\n  height: calc(100vh - 60px);\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.left-table-container {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: white;\r\n  overflow: hidden;\r\n}\r\n\r\n.left-pagination {\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  text-align: center;\r\n}\r\n\r\n.right-panel {\r\n  height: calc(100vh - 60px);\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.right-table-container {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: white;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.right-pagination {\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  text-align: center;\r\n}\r\n\r\n// 左侧表格行选中样式\r\n.left-table-container {\r\n  ::v-deep .el-table__row {\r\n    cursor: pointer;\r\n  }\r\n\r\n  ::v-deep .el-table__row:hover {\r\n    background-color: #f5f7fa;\r\n  }\r\n\r\n  ::v-deep .current-row {\r\n    background-color: #ecf5ff !important;\r\n  }\r\n}\r\n\r\n// 分页组件样式调整\r\n::v-deep .el-pagination {\r\n  top: -2px;\r\n  .el-pagination__sizes {\r\n    margin-top: -2px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;AA0IA,IAAAA,WAAA,GAAAC,OAAA;AACAA,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA,sBAAA;IACAC,IAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,aAAA;MAEA;MACAC,aAAA;MACAC,WAAA;MACAC,SAAA;MACAC,eAAA;MACAC,YAAA;MACAC,eAAA;MACAC,eAAA;MAAA;;MAEA;MACAC,cAAA;MACAC,YAAA;MACAC,UAAA;MACAC,gBAAA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;EACA;EACAC,OAAA;IACA;IACAD,QAAA,WAAAA,SAAA;MAAA,IAAAE,KAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,KAAA,CAAAhB,aAAA;cAAAwB,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAE,IAAA;cAAA,OAEAV,KAAA,CAAAW,gBAAA;YAAA;cAAAH,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAI,EAAA,GAAAJ,QAAA;cAEAK,OAAA,CAAAC,KAAA,aAAAN,QAAA,CAAAI,EAAA;cACAZ,KAAA,CAAAe,QAAA,CAAAD,KAAA;YAAA;cAAAN,QAAA,CAAAC,IAAA;cAEAT,KAAA,CAAAhB,aAAA;cAAA,OAAAwB,QAAA,CAAAQ,MAAA;YAAA;YAAA;cAAA,OAAAR,QAAA,CAAAS,IAAA;UAAA;QAAA,GAAAZ,OAAA;MAAA;IAEA;IAEA;IACAM,gBAAA,WAAAA,iBAAA;MAAA,IAAAO,MAAA;MAAA,WAAAjB,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAe,SAAA;QAAA,IAAAC,MAAA,EAAAC,QAAA,EAAAC,QAAA;QAAA,WAAAnB,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAiB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAf,IAAA,GAAAe,SAAA,CAAAd,IAAA;YAAA;cACAQ,MAAA,CAAAhC,WAAA;cAAAsC,SAAA,CAAAf,IAAA;cAAAe,SAAA,CAAAf,IAAA;cAIAW,MAAA;gBACAK,OAAA,EAAAP,MAAA,CAAA9B,eAAA;gBACAsC,QAAA,EAAAR,MAAA,CAAA7B;cACA;cAAAmC,SAAA,CAAAd,IAAA;cAAA,OACAiB,cAAA,CAAAC,eAAA,CAAAR,MAAA;YAAA;cAAAC,QAAA,GAAAG,SAAA,CAAAK,IAAA;cACAX,MAAA,CAAAjC,aAAA,GAAAoC,QAAA,CAAAS,IAAA,IAAAT,QAAA,CAAAtC,IAAA;cACAmC,MAAA,CAAA/B,SAAA,GAAAkC,QAAA,CAAAU,KAAA;cAAAP,SAAA,CAAAd,IAAA;cAAA;YAAA;cAAAc,SAAA,CAAAf,IAAA;cAAAe,SAAA,CAAAZ,EAAA,GAAAY,SAAA;cAEAX,OAAA,CAAAmB,IAAA,oBAAAR,SAAA,CAAAZ,EAAA;cACA;cACAU,QAAA,GAAAJ,MAAA,CAAAe,oBAAA;cACAf,MAAA,CAAAjC,aAAA,GAAAqC,QAAA,CAAAQ,IAAA;cACAZ,MAAA,CAAA/B,SAAA,GAAAmC,QAAA,CAAAS,KAAA;YAAA;cAGA;cACA,IACAb,MAAA,CAAA9B,eAAA,UACA8B,MAAA,CAAAjC,aAAA,CAAAiD,MAAA,QACA,CAAAhB,MAAA,CAAA3B,eAAA,EACA;gBACA2B,MAAA,CAAAiB,SAAA;kBACAjB,MAAA,CAAAkB,kBAAA,CAAAlB,MAAA,CAAAjC,aAAA;gBACA;cACA;cAAAuC,SAAA,CAAAd,IAAA;cAAA;YAAA;cAAAc,SAAA,CAAAf,IAAA;cAAAe,SAAA,CAAAa,EAAA,GAAAb,SAAA;cAEAX,OAAA,CAAAC,KAAA,cAAAU,SAAA,CAAAa,EAAA;cACAnB,MAAA,CAAAH,QAAA,CAAAD,KAAA;YAAA;cAAAU,SAAA,CAAAf,IAAA;cAEAS,MAAA,CAAAhC,WAAA;cAAA,OAAAsC,SAAA,CAAAR,MAAA;YAAA;YAAA;cAAA,OAAAQ,SAAA,CAAAP,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IAEA;IAEA;IACAc,oBAAA,WAAAA,qBAAA;MACA,IAAAK,UAAA,IACA,UACA,UACA,UACA,SACA,UACA,QACA,UACA,SACA,SACA,SACA,UACA,UACA,UACA,UACA,OACA;MAEA,IAAAP,KAAA,GAAAO,UAAA,CAAAJ,MAAA;MACA,IAAAK,UAAA,SAAAnD,eAAA,aAAAC,YAAA;MACA,IAAAmD,QAAA,GAAAC,IAAA,CAAAC,GAAA,CAAAH,UAAA,QAAAlD,YAAA,EAAA0C,KAAA;MAEA,IAAAY,kBAAA,GAAAL,UAAA;MAEA,IAAAR,IAAA,GAAAa,kBAAA,CACAC,KAAA,CAAAL,UAAA,EAAAC,QAAA,EACAK,GAAA,WAAAC,KAAA,EAAAC,KAAA;QAAA;UACAC,EAAA,EAAAT,UAAA,GAAAQ,KAAA;UACAD,KAAA,EAAAA;QACA;MAAA;MAEA;QACAhB,IAAA,EAAAA,IAAA;QACAC,KAAA,EAAAY,kBAAA,CAAAT;MACA;IACA;IAEA;IACAe,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MAAA,WAAAjD,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA+C,SAAA;QAAA,IAAA/B,MAAA,EAAAC,QAAA,EAAAC,QAAA;QAAA,WAAAnB,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA8C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5C,IAAA,GAAA4C,SAAA,CAAA3C,IAAA;YAAA;cAAA,IACAwC,MAAA,CAAA3D,eAAA;gBAAA8D,SAAA,CAAA3C,IAAA;gBAAA;cAAA;cACAwC,MAAA,CAAA1D,cAAA;cACA0D,MAAA,CAAAxD,UAAA;cAAA,OAAA2D,SAAA,CAAAC,MAAA;YAAA;cAIAJ,MAAA,CAAAzD,YAAA;cAAA4D,SAAA,CAAA5C,IAAA;cAAA4C,SAAA,CAAA5C,IAAA;cAIAW,MAAA;gBACAmC,UAAA,EAAAL,MAAA,CAAA3D,eAAA,CAAAyD,EAAA;gBACAvB,OAAA,EAAAyB,MAAA,CAAAvD,gBAAA;gBACA+B,QAAA,EAAAwB,MAAA,CAAAtD;cACA;cAAAyD,SAAA,CAAA3C,IAAA;cAAA,OACAiB,cAAA,CAAA6B,gBAAA,CAAApC,MAAA;YAAA;cAAAC,QAAA,GAAAgC,SAAA,CAAAxB,IAAA;cACAqB,MAAA,CAAA1D,cAAA,GAAA6B,QAAA,CAAAS,IAAA,IAAAT,QAAA,CAAAtC,IAAA;cACAmE,MAAA,CAAAxD,UAAA,GAAA2B,QAAA,CAAAU,KAAA;cAAAsB,SAAA,CAAA3C,IAAA;cAAA;YAAA;cAAA2C,SAAA,CAAA5C,IAAA;cAAA4C,SAAA,CAAAzC,EAAA,GAAAyC,SAAA;cAEAxC,OAAA,CAAAmB,IAAA,oBAAAqB,SAAA,CAAAzC,EAAA;cACA;cACAU,QAAA,GAAA4B,MAAA,CAAAO,qBAAA;cACAP,MAAA,CAAA1D,cAAA,GAAA8B,QAAA,CAAAQ,IAAA;cACAoB,MAAA,CAAAxD,UAAA,GAAA4B,QAAA,CAAAS,KAAA;YAAA;cAAAsB,SAAA,CAAA3C,IAAA;cAAA;YAAA;cAAA2C,SAAA,CAAA5C,IAAA;cAAA4C,SAAA,CAAAhB,EAAA,GAAAgB,SAAA;cAGAxC,OAAA,CAAAC,KAAA,cAAAuC,SAAA,CAAAhB,EAAA;cACAa,MAAA,CAAAnC,QAAA,CAAAD,KAAA;YAAA;cAAAuC,SAAA,CAAA5C,IAAA;cAEAyC,MAAA,CAAAzD,YAAA;cAAA,OAAA4D,SAAA,CAAArC,MAAA;YAAA;YAAA;cAAA,OAAAqC,SAAA,CAAApC,IAAA;UAAA;QAAA,GAAAkC,QAAA;MAAA;IAEA;IAEA;IACAM,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,YAAA,IACA;QACAX,EAAA;QACAF,KAAA;QACAc,KAAA;QACAC,UAAA;QACAC,WAAA;QACAC,KAAA;MACA,GACA;QACAf,EAAA;QACAF,KAAA;QACAc,KAAA;QACAC,UAAA;QACAC,WAAA;QACAC,KAAA;MACA,GACA;QACAf,EAAA;QACAF,KAAA;QACAc,KAAA;QACAC,UAAA;QACAC,WAAA;QACAC,KAAA;MACA,GACA;QACAf,EAAA;QACAF,KAAA;QACAc,KAAA;QACAC,UAAA;QACAC,WAAA;QACAC,KAAA;MACA,GACA;QACAf,EAAA;QACAF,KAAA;QACAc,KAAA;QACAC,UAAA;QACAC,WAAA;QACAC,KAAA;MACA,EACA;;MAEA;MACA,IAAAC,uBAAA,GAAAL,YAAA,CAAAd,GAAA,WAAAoB,OAAA,EAAAlB,KAAA;QAAA,WAAAmB,cAAA,CAAAhE,OAAA,MAAAgE,cAAA,CAAAhE,OAAA,MACA+D,OAAA;UACAjB,EAAA,GAAAU,MAAA,CAAA/D,gBAAA,QAAA+D,MAAA,CAAA9D,aAAA,GAAAmD,KAAA;UACAD,KAAA,KAAAqB,MAAA,CAAAT,MAAA,CAAAnE,eAAA,CAAAuD,KAAA,wBAAAqB,MAAA,CAAAF,OAAA,CAAAnB,KAAA;UACAiB,KAAA,EAAAL,MAAA,CAAAnE,eAAA,CAAAuD;QAAA;MAAA,CACA;MAEA,IAAAf,KAAA;MACA,IAAAQ,UAAA,SAAA5C,gBAAA,aAAAC,aAAA;MACA,IAAAkC,IAAA,GAAAkC,uBAAA,CAAApB,KAAA,CACA,GACAH,IAAA,CAAAC,GAAA,MAAA9C,aAAA,EAAAmC,KAAA,GAAAQ,UAAA,CACA;MAEA;QACAT,IAAA,EAAAA,IAAA;QACAC,KAAA,EAAAA;MACA;IACA;IAEA;IACAK,kBAAA,WAAAA,mBAAAgC,GAAA;MACA,KAAA7E,eAAA,GAAA6E,GAAA;MACA,KAAAC,KAAA,CAAAC,SAAA,CAAAC,aAAA,CAAAH,GAAA;;MAEA;MACA,KAAAzE,gBAAA;MACA,KAAAsD,iBAAA;IACA;IAEA;IACAuB,oBAAA,WAAAA,qBAAAC,IAAA;MAAA,IAAAC,IAAA,GAAAD,IAAA,CAAAC,IAAA;QAAAC,KAAA,GAAAF,IAAA,CAAAE,KAAA;MACA,KAAAvF,eAAA,GAAAsF,IAAA;MACA,KAAArF,YAAA,GAAAsF,KAAA;MACA,KAAAhE,gBAAA;IACA;IAEA;IACAiE,qBAAA,WAAAA,sBAAAC,KAAA;MAAA,IAAAH,IAAA,GAAAG,KAAA,CAAAH,IAAA;QAAAC,KAAA,GAAAE,KAAA,CAAAF,KAAA;MACA,KAAAhF,gBAAA,GAAA+E,IAAA;MACA,KAAA9E,aAAA,GAAA+E,KAAA;MACA,KAAA1B,iBAAA;IACA;IAEA;IACA6B,eAAA,WAAAA,gBAAAlB,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA;IACA;IAEA;IACAmB,UAAA,WAAAA,WAAAX,GAAA;MACA;MACA;MACA,IAAAY,GAAA,yBAAAb,MAAA,CAAAC,GAAA,CAAApB,EAAA;MACAiC,MAAA,CAAAC,IAAA,CAAAF,GAAA;IACA;EACA;AACA", "ignoreList": []}]}
{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\tabOne.vue?vue&type=template&id=6cd40d78&scoped=true", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\tabOne.vue", "mtime": 1754397030175}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}